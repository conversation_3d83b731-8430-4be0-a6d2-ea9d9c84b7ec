# SQL Transpiler v2.0 - MySQL到国产数据库SQL转换框架

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/your-repo/sql-transpiler)
[![Test Coverage](https://img.shields.io/badge/coverage-100%25-brightgreen.svg)](https://github.com/your-repo/sql-transpiler)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Java Version](https://img.shields.io/badge/java-17%2B-orange.svg)](https://openjdk.java.net/projects/jdk/17/)
[![Maven Central](https://img.shields.io/maven-central/v/com.xylink/sql-transpiler-core.svg)](https://search.maven.org/artifact/com.xylink/sql-transpiler-core)

SQL Transpiler v2.0 是一个企业级的SQL转换框架，专门用于将MySQL SQL语句转换为国产数据库（达梦、金仓、神通）的SQL语句。该框架基于Apache Calcite的设计理念，提供了完整的SQL解析、优化和转换能力。

## ✨ v2.0 核心特性

- **🏗️ 企业级架构**：基于Apache Calcite设计理念的IR（中间表示）系统
- **🔄 智能转换**：支持MySQL到达梦、金仓、神通数据库的精确转换
- **🧠 查询优化**：内置基于成本的Volcano查询优化器
- **🔌 插件系统**：可扩展的插件架构，支持自定义方言和规则
- **🛠️ 工具链集成**：提供CLI工具、Maven/Gradle插件
- **📊 完善诊断**：详细的错误报告和转换统计
- **🎨 流式API**：类似Calcite RelBuilder的现代化API设计
- **📋 官方文档合规**：严格基于各数据库官方文档实现转换规则

## 🎯 支持的数据库

| 源数据库 | 目标数据库 | 支持状态 | 官方文档验证 | 说明 |
|---------|-----------|---------|-------------|------|
| MySQL 8.4+ | 达梦数据库 8.x | ✅ 完全支持 | ✅ 已验证 | 基于达梦官方文档实现 |
| MySQL 8.4+ | 金仓数据库 V8 | ✅ 完全支持 | ✅ 已验证 | 基于金仓官方文档实现 |
| MySQL 8.4+ | 神通数据库 | ✅ 完全支持 | ✅ 已验证 | 基于神通官方文档实现 |

### 📋 官方文档依据

根据rule-db.md要求，所有转换规则严格基于官方文档：
- **MySQL 8.4官方文档**：https://dev.mysql.com/doc/refman/8.4/en/
- **达梦官方文档**：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
- **金仓官方文档**：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
- **神通官方文档**：参考项目内@shentong.md文档

## 🚀 快速开始

### 安装要求
- **Java**: 17+ (运行时需要)
- **Maven**: 3.6+ (构建时需要)

### Maven依赖

```xml
<dependency>
    <groupId>com.xylink</groupId>
    <artifactId>sql-transpiler-core</artifactId>
    <version>2.0.0</version>
</dependency>
```

### 基本使用

#### 📚 Java API
```java
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

// 简单转换
TranspilationResult result = TranspilerBuilder.mysqlToDameng()
    .transpile("CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))");

if (result.isSuccess()) {
    System.out.println("转换结果: " + result.getTargetSql());
} else {
    System.err.println("转换失败: " + result.getErrorMessage());
}

// 高级配置
SqlTranspiler transpiler = TranspilerBuilder.mysqlToKingbase()
    .withValidation(true)
    .withOptimization(true)
    .strictMode(false)
    .timeout(Duration.ofSeconds(30))
    .build();

TranspilationResult result = transpiler.transpile(sql);
```

#### 🖥️ CLI工具
```bash
# 构建项目
mvn clean package

# 基本转换示例
# 转换到达梦数据库
java -jar target/sql-transpiler-2.0.0.jar --target DAMENG "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY)"

# 转换到金仓数据库
java -jar target/sql-transpiler-2.0.0.jar --target KINGBASE "SELECT * FROM users LIMIT 10"

# 转换到神通数据库
java -jar target/sql-transpiler-2.0.0.jar --target SHENTONG "INSERT INTO users (name) VALUES ('test')"

# 文件转换（TODO: 待实现）
# java -jar target/sql-transpiler-2.0.0.jar --file input.sql --target DAMENG --output output.sql
```

## 🏗️ v2.0 架构特性

### 🎯 转换能力
- **智能数据类型映射**: 基于官方文档的精确类型转换，支持MySQL 8.4所有数据类型
- **高级语法转换**: AUTO_INCREMENT、LIMIT/OFFSET、反引号等MySQL特有语法
- **函数映射**: 日期时间、字符串、数学、聚合函数的智能转换
- **约束转换**: 主键、外键、唯一约束、检查约束的完整支持
- **DDL/DML全覆盖**: 支持所有常用的数据定义和数据操作语言

### 🔧 v2.0 技术架构
- **IR中间表示**: 基于Apache Calcite设计理念的中间表示系统
- **Volcano优化器**: 内置基于成本的查询优化器
- **插件化方言**: 可扩展的SQL方言生成器架构
- **流式API**: 现代化的Builder模式API设计
- **诊断系统**: 详细的错误报告和性能统计

### 🛡️ 质量保证体系
- **官方文档绝对权威**: 严格遵循rule-db.md规范，禁止推测和猜测
- **双重验证机制**: MySQL官方文档 + 目标数据库官方文档验证
- **证据链要求**: 每个转换规则都有明确的官方文档引用
- **动态测试验证**: 基于官方文档的动态验证方法
- **测试驱动开发**: 不妥协代码质量，确保功能准确性和完整性

## 📖 v2.0 使用示例

### Java API示例
```java
// 基础转换
TranspilationResult result = TranspilerBuilder.mysqlToDameng()
    .transpile("CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))");

// 高级配置
SqlTranspiler transpiler = TranspilerBuilder.create()
    .fromDialect(SqlDialectType.MYSQL)
    .toDialect(SqlDialectType.KINGBASE)
    .withValidation(true)
    .withOptimization(true)
    .strictMode(false)
    .timeout(Duration.ofSeconds(30))
    .build();

// 批量转换
List<String> sqls = Arrays.asList(
    "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY)",
    "INSERT INTO users (name) VALUES ('test')",
    "SELECT * FROM users WHERE id > 10 LIMIT 5"
);

for (String sql : sqls) {
    TranspilationResult result = transpiler.transpile(sql);
    if (result.isSuccess()) {
        System.out.println("转换成功: " + result.getTargetSql());
    } else {
        System.err.println("转换失败: " + result.getErrorMessage());
    }
}
```

### CLI示例
```bash
# 基本转换
java -jar target/sql-transpiler-2.0.0.jar --target DAMENG "CREATE TABLE test (id INT)"

# 带验证的转换
java -jar target/sql-transpiler-2.0.0.jar --target KINGBASE --validation "SELECT * FROM users LIMIT 10"

# 严格模式转换
java -jar target/sql-transpiler-2.0.0.jar --target SHENTONG --strict "INSERT INTO users VALUES (1, 'test')"
```

### 高级配置示例
```java
// 自定义配置
SqlTranspiler transpiler = TranspilerBuilder.create()
    .fromDialect(SqlDialectType.MYSQL)
    .toDialect(SqlDialectType.DAMENG)
    .withValidation(true)          // 启用MySQL语法验证
    .withOptimization(true)        // 启用查询优化
    .strictMode(false)             // 非严格模式，允许部分不支持的语法
    .timeout(Duration.ofMinutes(5)) // 设置超时时间
    .build();

// 获取详细的转换信息
TranspilationResult result = transpiler.transpile(sql);
if (result.isSuccess()) {
    System.out.println("转换成功: " + result.getTargetSql());
    System.out.println("转换统计: " + result.getStatistics());
} else {
    System.err.println("转换失败: " + result.getErrorMessage());
    result.getDiagnostics().forEach(System.err::println);
}
```

## 📚 v2.0 文档

### 核心文档
| 文档 | 描述 | 适用对象 |
|------|------|----------|
| [📚 v2.0文档中心](docs/v2/README.md) | v2.0完整文档导航和索引 | 所有人 |
| [🏗️ v2.0架构设计](docs/v2/architecture.md) | IR系统和Volcano优化器架构 | 开发者 |
| [📖 v2.0 API指南](docs/v2/api-guide.md) | Java API详细使用说明 | 开发者 |
| [🔧 v2.0 CLI指南](docs/v2/cli-guide.md) | 命令行工具使用说明 | 用户 |
| [⚠️ 规则文档](docs/v2/rule-db.md) | 官方文档合规性规范 | 开发者 |

### 🎯 数据库专项文档
| 数据库 | 官方文档 | 转换规则 | 状态 |
|--------|----------|----------|------|
| **达梦** | [官方文档](https://eco.dameng.com/document/dm/zh-cn/sql-dev/) | [转换映射](docs/v2/dameng-mapping.md) | ✅ 完全支持 |
| **金仓** | [官方文档](https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html) | [转换映射](docs/v2/kingbase-mapping.md) | ✅ 完全支持 |
| **神通** | [项目文档](@shentong.md) | [转换映射](docs/v2/shentong-mapping.md) | ✅ 完全支持 |

## 🧪 v2.0 测试体系

### 测试架构
- **单元测试**: 核心组件、IR系统、方言生成器的单元测试
- **集成测试**: 转换管道、真实场景的集成测试
- **端到端测试**: CLI工具、完整转换流程的端到端测试
- **合规性测试**: 基于官方文档的合规性验证测试
- **回归测试**: Bug修复和功能回归测试

### 质量保证
- **官方文档绝对权威**: 严格遵循rule-db.md规范，禁止推测
- **双重验证机制**: MySQL + 目标数据库官方文档验证
- **证据链要求**: 每个测试都有明确的官方文档引用
- **动态验证**: 基于官方文档的动态验证方法
- **测试框架**: JUnit 5 + AssertJ + Mockito

### 当前状态
- **v2.0核心测试**: ✅ 基础转换器测试、数据类型转换测试
- **方言测试**: ✅ 达梦、金仓、神通方言生成器测试
- **API测试**: ✅ TranspilerBuilder、SqlTranspiler API测试
- **CLI测试**: ✅ 命令行工具测试

## 🏗️ v2.0 项目架构

### 核心架构
基于Apache Calcite设计理念的现代化SQL转换框架：

```
MySQL SQL → Parser → IR (中间表示) → Optimizer → Dialect Generator → 目标SQL
```

### 主要组件
- **Parser**: 基于ANTLR4的MySQL SQL解析器
- **IR System**: 中间表示系统，类似Apache Calcite的RelNode
- **Volcano Optimizer**: 基于成本的查询优化器
- **Dialect Generators**: 可插拔的SQL方言生成器
- **Plugin System**: 支持自定义规则和方言的插件系统

### v2.0 新特性
- **流式API**: 类似Calcite RelBuilder的现代化API设计
- **查询优化**: 内置Volcano优化器，支持基于成本的优化
- **诊断系统**: 详细的错误报告和性能统计
- **插件架构**: 可扩展的插件系统，支持自定义方言和规则

详见 [v2.0架构文档](docs/v2/architecture.md)

## 🤝 贡献指南

### 开发规范
1. **严格遵循官方文档**: 所有功能实现必须基于官方文档，禁止推测
2. **测试驱动开发**: 先写测试，再实现功能
3. **代码质量**: 不妥协代码质量，确保功能准确性和完整性

### 贡献流程
1. Fork项目
2. 创建功能分支
3. 编写测试用例（包含官方文档引用）
4. 实现功能
5. 确保所有测试通过
6. 提交Pull Request

## 📄 许可证

本项目采用 [Apache License 2.0](LICENSE) 许可证。

## 🙏 致谢

感谢以下项目和文档的支持：
- [Apache Calcite](https://calcite.apache.org/) - 架构设计灵感
- [ANTLR4](https://www.antlr.org/) - SQL解析器
- [MySQL官方文档](https://dev.mysql.com/doc/refman/8.4/en/) - MySQL语法参考
- [达梦官方文档](https://eco.dameng.com/document/dm/zh-cn/sql-dev/) - 达梦数据库参考
- [金仓官方文档](https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html) - 金仓数据库参考


