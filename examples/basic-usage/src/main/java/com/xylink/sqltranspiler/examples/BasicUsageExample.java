package com.xylink.sqltranspiler.examples;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;
import com.xylink.sqltranspiler.v2.diagnostics.DiagnosticReport;
import com.xylink.sqltranspiler.v2.rules.TransformationStatistics;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * SQL Transpiler v2.0 基础使用示例
 * 
 * 本示例展示了SQL Transpiler的基本用法，包括：
 * 1. 简单转换
 * 2. 批量转换
 * 3. 配置选项
 * 4. 错误处理
 * 5. 统计信息
 */
public class BasicUsageExample {
    
    public static void main(String[] args) {
        BasicUsageExample example = new BasicUsageExample();
        
        System.out.println("=== SQL Transpiler v2.0 基础使用示例 ===\n");
        
        // 1. 简单转换示例
        example.simpleTranspilation();
        
        // 2. 不同目标数据库示例
        example.differentTargetDatabases();
        
        // 3. 批量转换示例
        example.batchTranspilation();
        
        // 4. 配置选项示例
        example.configurationOptions();
        
        // 5. 错误处理示例
        example.errorHandling();
        
        // 6. 统计信息示例
        example.statisticsExample();
        
        // 7. 可重用转换器示例
        example.reusableTranspiler();
    }
    
    /**
     * 1. 简单转换示例
     */
    public void simpleTranspilation() {
        System.out.println("1. 简单转换示例");
        System.out.println("================");
        
        String mysqlSql = "CREATE TABLE users (" +
                         "id INT AUTO_INCREMENT PRIMARY KEY, " +
                         "name VARCHAR(100) NOT NULL, " +
                         "email VARCHAR(255) UNIQUE, " +
                         "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                         ")";
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        // 转换为达梦SQL
        TranspilationResult result = TranspilerBuilder.mysqlToDameng()
            .transpile(mysqlSql);
        
        if (result.isSuccess()) {
            System.out.println("✅ 转换成功!");
            System.out.println("达梦SQL:");
            System.out.println(result.getTranspiledSql());
            System.out.println("转换耗时: " + result.getDurationMillis() + "ms");
        } else {
            System.err.println("❌ 转换失败: " + result.getErrorMessage());
        }
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 2. 不同目标数据库示例
     */
    public void differentTargetDatabases() {
        System.out.println("2. 不同目标数据库示例");
        System.out.println("====================");
        
        String mysqlSql = "SELECT * FROM users WHERE created_at >= '2024-01-01' ORDER BY id LIMIT 10";
        
        System.out.println("原始MySQL SQL:");
        System.out.println(mysqlSql);
        System.out.println();
        
        // 转换为达梦
        TranspilationResult damengResult = TranspilerBuilder.mysqlToDameng()
            .transpile(mysqlSql);
        
        // 转换为金仓
        TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase()
            .transpile(mysqlSql);
        
        // 转换为神通
        TranspilationResult shentongResult = TranspilerBuilder.mysqlToShentong()
            .transpile(mysqlSql);
        
        System.out.println("达梦SQL: " + damengResult.getTranspiledSql());
        System.out.println("金仓SQL: " + kingbaseResult.getTranspiledSql());
        System.out.println("神通SQL: " + shentongResult.getTranspiledSql());
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 3. 批量转换示例
     */
    public void batchTranspilation() {
        System.out.println("3. 批量转换示例");
        System.out.println("================");
        
        List<String> sqlStatements = Arrays.asList(
            "CREATE TABLE products (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))",
            "INSERT INTO products (name) VALUES ('Product 1'), ('Product 2')",
            "SELECT * FROM products WHERE name LIKE '%Product%'",
            "UPDATE products SET name = 'Updated Product' WHERE id = 1",
            "DELETE FROM products WHERE id = 2"
        );
        
        System.out.println("批量转换 " + sqlStatements.size() + " 条SQL语句...");
        System.out.println();
        
        List<TranspilationResult> results = TranspilerBuilder.mysqlToDameng()
            .transpileAll(sqlStatements);
        
        for (int i = 0; i < results.size(); i++) {
            TranspilationResult result = results.get(i);
            System.out.printf("SQL %d: %s%n", i + 1, 
                result.isSuccess() ? "✅ 成功" : "❌ 失败");
            
            if (result.isSuccess()) {
                System.out.println("  转换结果: " + result.getTranspiledSql());
            } else {
                System.out.println("  错误信息: " + result.getErrorMessage());
            }
            System.out.println();
        }
        
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        System.out.println("批量转换完成: " + successCount + "/" + results.size() + " 成功");
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 4. 配置选项示例
     */
    public void configurationOptions() {
        System.out.println("4. 配置选项示例");
        System.out.println("================");
        
        String complexSql = """
            SELECT u.name, COUNT(o.id) as order_count
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            WHERE u.created_at >= '2024-01-01'
            GROUP BY u.id, u.name
            HAVING COUNT(o.id) > 5
            ORDER BY order_count DESC
            LIMIT 20
            """;
        
        System.out.println("复杂查询SQL:");
        System.out.println(complexSql);
        System.out.println();
        
        // 基础配置
        System.out.println("基础配置转换:");
        TranspilationResult basicResult = TranspilerBuilder.mysqlToDameng()
            .transpile(complexSql);
        System.out.println("耗时: " + basicResult.getDurationMillis() + "ms");
        
        // 启用验证和优化
        System.out.println("\n启用验证和优化:");
        TranspilationResult optimizedResult = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(true)
            .transpile(complexSql);
        System.out.println("耗时: " + optimizedResult.getDurationMillis() + "ms");
        
        // 严格模式
        System.out.println("\n严格模式:");
        TranspilationResult strictResult = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .strictMode(true)
            .transpile(complexSql);
        System.out.println("耗时: " + strictResult.getDurationMillis() + "ms");
        
        // 自定义超时
        System.out.println("\n自定义超时设置:");
        TranspilationResult timeoutResult = TranspilerBuilder.mysqlToDameng()
            .timeout(Duration.ofSeconds(10))
            .transpile(complexSql);
        System.out.println("耗时: " + timeoutResult.getDurationMillis() + "ms");
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 5. 错误处理示例
     */
    public void errorHandling() {
        System.out.println("5. 错误处理示例");
        System.out.println("================");
        
        // 故意使用无效的SQL
        String invalidSql = "INVALID SQL STATEMENT WITH SYNTAX ERRORS";
        
        System.out.println("无效SQL: " + invalidSql);
        System.out.println();
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .transpile(invalidSql);
        
        if (result.isFailure()) {
            System.out.println("❌ 转换失败（预期结果）");
            System.out.println("错误信息: " + result.getErrorMessage());
            
            // 获取详细诊断信息
            if (result.getDiagnosticReport().isPresent()) {
                DiagnosticReport report = result.getDiagnosticReport().get();
                System.out.println("\n详细诊断信息:");
                System.out.println(report.toFormattedString());
                
                // 分类显示问题
                if (report.hasErrors()) {
                    System.out.println("\n错误列表:");
                    report.getErrors().forEach(error -> 
                        System.out.println("  - [" + error.getCode() + "] " + error.getMessage()));
                }
                
                if (report.hasWarnings()) {
                    System.out.println("\n警告列表:");
                    report.getWarnings().forEach(warning -> 
                        System.out.println("  - [" + warning.getCode() + "] " + warning.getMessage()));
                }
            }
        }
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 6. 统计信息示例
     */
    public void statisticsExample() {
        System.out.println("6. 统计信息示例");
        System.out.println("================");
        
        String sql = "CREATE TABLE test_table (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "data LONGTEXT, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ")";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(true)
            .transpile(sql);
        
        if (result.isSuccess()) {
            System.out.println("✅ 转换成功");
            System.out.println("转换结果: " + result.getTranspiledSql());
            System.out.println();
            
            // 基本统计信息
            System.out.println("基本统计:");
            System.out.println("  转换耗时: " + result.getDurationMillis() + "ms");
            System.out.println("  源方言: " + result.getSourceDialect());
            System.out.println("  目标方言: " + result.getTargetDialect());
            System.out.println("  转换时间: " + result.getTimestamp());
            
            // 详细统计信息
            if (result.getStatistics().isPresent()) {
                TransformationStatistics stats = result.getStatistics().get();
                System.out.println("\n详细统计:");
                System.out.println("  应用的规则数量: " + stats.getAppliedRulesCount());
                System.out.println("  转换的节点数量: " + stats.getTransformedNodesCount());
                System.out.println("  优化改进数量: " + stats.getOptimizationImprovements());
                System.out.println("  转换时间: " + stats.getTransformationTimeMs() + "ms");
                
                // 应用的规则列表
                if (!stats.getAppliedRules().isEmpty()) {
                    System.out.println("\n应用的规则:");
                    stats.getAppliedRules().forEach(rule -> 
                        System.out.println("  - " + rule));
                }
            }
        }
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 7. 可重用转换器示例
     */
    public void reusableTranspiler() {
        System.out.println("7. 可重用转换器示例");
        System.out.println("====================");
        
        // 创建可重用的转换器
        SqlTranspiler transpiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(true)
            .timeout(Duration.ofSeconds(30))
            .build();
        
        System.out.println("创建可重用转换器:");
        System.out.println("  源方言: " + transpiler.getSourceDialect());
        System.out.println("  目标方言: " + transpiler.getTargetDialect());
        System.out.println();
        
        // 多次使用同一个转换器
        String[] testSqls = {
            "SELECT COUNT(*) FROM users",
            "INSERT INTO logs (message) VALUES ('Test message')",
            "UPDATE users SET last_login = NOW() WHERE id = 1"
        };
        
        System.out.println("使用同一转换器处理多个SQL:");
        for (int i = 0; i < testSqls.length; i++) {
            String sql = testSqls[i];
            TranspilationResult result = transpiler.transpile(sql);
            
            System.out.printf("SQL %d: %s (%dms)%n", 
                i + 1, 
                result.isSuccess() ? "✅ 成功" : "❌ 失败",
                result.getDurationMillis());
            
            if (result.isSuccess()) {
                System.out.println("  结果: " + result.getTranspiledSql());
            }
        }
        
        System.out.println("\n转换器可以重复使用，避免重复创建的开销！");
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
}
