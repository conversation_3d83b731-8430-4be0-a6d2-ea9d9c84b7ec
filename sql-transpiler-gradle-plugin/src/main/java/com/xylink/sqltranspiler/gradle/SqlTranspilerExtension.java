package com.xylink.sqltranspiler.gradle;

import org.gradle.api.Project;
import org.gradle.api.file.DirectoryProperty;
import org.gradle.api.provider.Property;
import org.gradle.api.provider.Provider;

/**
 * SQL转换器Gradle扩展配置
 * 
 * 定义插件的配置选项。
 * 
 * 验证日期: 2024-01-15
 */
public abstract class SqlTranspilerExtension {
    
    private final Project project;
    
    public SqlTranspilerExtension(Project project) {
        this.project = project;
    }
    
    /**
     * 源数据库方言
     */
    public abstract Property<String> getSourceDialect();
    
    /**
     * 目标数据库方言
     */
    public abstract Property<String> getTargetDialect();
    
    /**
     * 输入SQL文件目录
     */
    public abstract DirectoryProperty getInputDirectory();
    
    /**
     * 输出SQL文件目录
     */
    public abstract DirectoryProperty getOutputDirectory();
    
    /**
     * 包含的文件模式
     */
    public abstract Property<String[]> getIncludes();
    
    /**
     * 排除的文件模式
     */
    public abstract Property<String[]> getExcludes();
    
    /**
     * 是否启用严格模式
     */
    public abstract Property<Boolean> getStrictMode();
    
    /**
     * 是否启用SQL验证
     */
    public abstract Property<Boolean> getEnableValidation();
    
    /**
     * 是否启用查询优化
     */
    public abstract Property<Boolean> getEnableOptimization();
    
    /**
     * 是否在转换失败时失败构建
     */
    public abstract Property<Boolean> getFailOnError();
    
    /**
     * 输出文件名后缀
     */
    public abstract Property<String> getOutputSuffix();
    
    // 便利方法
    
    /**
     * 设置源方言
     */
    public void sourceDialect(String dialect) {
        getSourceDialect().set(dialect);
    }
    
    /**
     * 设置目标方言
     */
    public void targetDialect(String dialect) {
        getTargetDialect().set(dialect);
    }
    
    /**
     * 设置输入目录
     */
    public void inputDirectory(Object directory) {
        getInputDirectory().set(project.file(directory));
    }
    
    /**
     * 设置输出目录
     */
    public void outputDirectory(Object directory) {
        getOutputDirectory().set(project.file(directory));
    }
    
    /**
     * 设置包含模式
     */
    public void includes(String... patterns) {
        getIncludes().set(patterns);
    }
    
    /**
     * 设置排除模式
     */
    public void excludes(String... patterns) {
        getExcludes().set(patterns);
    }
    
    /**
     * 设置严格模式
     */
    public void strictMode(boolean strict) {
        getStrictMode().set(strict);
    }
    
    /**
     * 设置验证选项
     */
    public void enableValidation(boolean enable) {
        getEnableValidation().set(enable);
    }
    
    /**
     * 设置优化选项
     */
    public void enableOptimization(boolean enable) {
        getEnableOptimization().set(enable);
    }
    
    /**
     * 设置失败时是否中断构建
     */
    public void failOnError(boolean fail) {
        getFailOnError().set(fail);
    }
    
    /**
     * 设置输出文件后缀
     */
    public void outputSuffix(String suffix) {
        getOutputSuffix().set(suffix);
    }
}
