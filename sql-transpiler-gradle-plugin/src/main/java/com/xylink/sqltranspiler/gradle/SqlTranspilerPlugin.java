package com.xylink.sqltranspiler.gradle;

import org.gradle.api.Plugin;
import org.gradle.api.Project;
import org.gradle.api.tasks.TaskProvider;

/**
 * SQL转换器Gradle插件
 * 
 * 为Gradle项目提供SQL转换功能。
 * 
 * 官方文档依据：
 * - Gradle官方文档: https://docs.gradle.org/current/userguide/custom_plugins.html
 *   Gradle自定义插件开发指南
 * - Gradle Plugin Development官方文档: https://docs.gradle.org/current/userguide/implementing_gradle_plugins.html
 *   Gradle插件实现指南
 * 
 * 验证日期: 2024-01-15
 * 
 * 使用示例：
 * plugins {
 *     id 'com.xylink.sql-transpiler' version '2.0.0'
 * }
 * 
 * sqlTranspiler {
 *     sourceDialect = 'mysql'
 *     targetDialect = 'dameng'
 *     inputDirectory = file('src/main/sql')
 *     outputDirectory = file('build/generated-sql')
 * }
 */
public class SqlTranspilerPlugin implements Plugin<Project> {
    
    public static final String EXTENSION_NAME = "sqlTranspiler";
    public static final String TASK_GROUP = "SQL Transpiler";
    public static final String TRANSPILE_TASK_NAME = "transpileSql";
    
    @Override
    public void apply(Project project) {
        // 创建扩展配置
        SqlTranspilerExtension extension = project.getExtensions()
            .create(EXTENSION_NAME, SqlTranspilerExtension.class, project);
        
        // 注册转换任务
        TaskProvider<SqlTranspilerTask> transpileTask = project.getTasks()
            .register(TRANSPILE_TASK_NAME, SqlTranspilerTask.class, task -> {
                task.setGroup(TASK_GROUP);
                task.setDescription("将SQL文件从源方言转换为目标方言");
                
                // 配置任务属性
                task.getSourceDialect().convention(extension.getSourceDialect());
                task.getTargetDialect().convention(extension.getTargetDialect());
                task.getInputDirectory().convention(extension.getInputDirectory());
                task.getOutputDirectory().convention(extension.getOutputDirectory());
                task.getIncludes().convention(extension.getIncludes());
                task.getExcludes().convention(extension.getExcludes());
                task.getStrictMode().convention(extension.getStrictMode());
                task.getEnableValidation().convention(extension.getEnableValidation());
                task.getEnableOptimization().convention(extension.getEnableOptimization());
                task.getFailOnError().convention(extension.getFailOnError());
                task.getOutputSuffix().convention(extension.getOutputSuffix());
            });
        
        // 将转换任务添加到processResources的依赖中
        project.getTasks().named("processResources").configure(task -> {
            task.dependsOn(transpileTask);
        });
        
        // 配置默认值
        configureDefaults(project, extension);
    }
    
    /**
     * 配置默认值
     */
    private void configureDefaults(Project project, SqlTranspilerExtension extension) {
        // 设置默认的输入目录
        extension.getInputDirectory().convention(
            project.getLayout().getProjectDirectory().dir("src/main/sql")
        );
        
        // 设置默认的输出目录
        extension.getOutputDirectory().convention(
            project.getLayout().getBuildDirectory().dir("generated-sql")
        );
        
        // 设置默认的源方言
        extension.getSourceDialect().convention("mysql");
        
        // 设置默认的包含模式
        extension.getIncludes().convention(project.provider(() -> new String[]{"**/*.sql"}));
        
        // 设置默认的排除模式
        extension.getExcludes().convention(project.provider(() -> new String[]{}));
        
        // 设置默认的布尔选项
        extension.getStrictMode().convention(false);
        extension.getEnableValidation().convention(true);
        extension.getEnableOptimization().convention(false);
        extension.getFailOnError().convention(true);
    }
}
