plugins {
    id 'java-gradle-plugin'
    id 'maven-publish'
    id 'signing'
}

group = 'com.xylink'
version = '2.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
    withSourcesJar()
    withJavadocJar()
}

repositories {
    mavenCentral()
}

dependencies {
    // SQL Transpiler Core
    implementation 'com.xylink:sql-transpiler-core:2.0.0'
    
    // Gradle API
    implementation gradleApi()
    
    // Test Dependencies
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.2'
    testImplementation 'org.gradle:gradle-tooling-api:8.0'
}

gradlePlugin {
    website = 'https://github.com/xylink/sql-transpiler'
    vcsUrl = 'https://github.com/xylink/sql-transpiler.git'
    
    plugins {
        sqlTranspiler {
            id = 'com.xylink.sql-transpiler'
            displayName = 'SQL Transpiler Plugin'
            description = 'Gradle插件，用于在构建过程中进行SQL转换'
            implementationClass = 'com.xylink.sqltranspiler.gradle.SqlTranspilerPlugin'
            tags = ['sql', 'database', 'transpiler', 'mysql', 'dameng', 'kingbase', 'shentong']
        }
    }
}

test {
    useJUnitPlatform()
}

publishing {
    publications {
        maven(MavenPublication) {
            from components.java
            
            pom {
                name = 'SQL Transpiler Gradle Plugin'
                description = 'Gradle插件，用于在构建过程中进行SQL转换'
                url = 'https://github.com/xylink/sql-transpiler'
                
                licenses {
                    license {
                        name = 'The Apache License, Version 2.0'
                        url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }
                
                developers {
                    developer {
                        id = 'xylink'
                        name = 'XYLink Team'
                        email = '<EMAIL>'
                    }
                }
                
                scm {
                    connection = 'scm:git:git://github.com/xylink/sql-transpiler.git'
                    developerConnection = 'scm:git:ssh://github.com:xylink/sql-transpiler.git'
                    url = 'http://github.com/xylink/sql-transpiler/tree/main'
                }
            }
        }
    }
}

signing {
    sign publishing.publications.maven
}
