package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.statement.IRSelectStatement;

/**
 * IR子查询类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/subqueries.html
 */
public class IRSubquery implements IRExpression {
    
    private final IRSelectStatement selectStatement;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRSubquery(IRSelectStatement selectStatement) {
        this.selectStatement = selectStatement;
    }
    
    @Override
    public ExpressionType getExpressionType() {
        return ExpressionType.SUBQUERY;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加SUBQUERY类型
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public IRSelectStatement getSelectStatement() { return selectStatement; }
}