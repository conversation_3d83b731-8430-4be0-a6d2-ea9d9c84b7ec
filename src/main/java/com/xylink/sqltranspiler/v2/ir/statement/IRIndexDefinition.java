package com.xylink.sqltranspiler.v2.ir.statement;

import java.util.List;
import java.util.Map;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * 索引定义的中间表示（占位符实现）
 */
public final class IRIndexDefinition implements IRNode {

    private final String indexName;
    private final List<String> columnNames;
    private final boolean unique;
    private final SourceLocation sourceLocation;
    private final Map<String, Object> attributes;

    private IRIndexDefinition(String indexName, List<String> columnNames, boolean unique,
                             SourceLocation sourceLocation, Map<String, Object> attributes) {
        this.indexName = indexName;
        this.columnNames = List.copyOf(columnNames);
        this.unique = unique;
        this.sourceLocation = sourceLocation;
        this.attributes = attributes;
    }
    
    public static IRIndexDefinition of(String indexName, List<String> columnNames) {
        return new IRIndexDefinition(indexName, columnNames, false, SourceLocation.UNKNOWN, Map.of());
    }
    
    public String getIndexName() { return indexName; }
    public List<String> getColumnNames() { return columnNames; }
    public boolean isUnique() { return unique; }

    @Override
    public NodeType getNodeType() {
        return NodeType.INDEX_DEFINITION;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return sourceLocation;
    }

    @Override
    public IRMetadata getMetadata() {
        return IRMetadata.empty();
    }
}
