package com.xylink.sqltranspiler.v2.ir.statement;

/**
 * SQL语句类型枚举
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html
 * 
 * 定义所有支持的SQL语句类型
 */
public enum StatementType {
    
    // DDL语句
    CREATE_TABLE("CREATE TABLE"),
    ALTER_TABLE("ALTER TABLE"),
    DROP_TABLE("DROP TABLE"),
    CREATE_INDEX("CREATE INDEX"),
    DROP_INDEX("DROP INDEX"),
    CREATE_DATABASE("CREATE DATABASE"),
    DROP_DATABASE("DROP DATABASE"),
    
    // DML语句
    SELECT("SELECT"),
    INSERT("INSERT"),
    UPDATE("UPDATE"),
    DELETE("DELETE"),
    
    // 事务语句
    BEGIN("BEGIN"),
    COMMIT("COMMIT"),
    ROLLBACK("ROLLBACK"),
    
    // 其他语句
    USE("USE"),
    SHOW("SHOW"),
    DESCRIBE("DESCRIBE"),
    EXPLAIN("EXPLAIN"),
    SET("SET"),
    
    // MySQL特有语句
    REPLACE("REPLACE"),
    INSERT_IGNORE("INSERT IGNORE"),
    INSERT_ON_DUPLICATE_KEY_UPDATE("INSERT ON DUPLICATE KEY UPDATE"),
    
    // 未知语句
    UNKNOWN("UNKNOWN");
    
    private final String sqlKeyword;
    
    /**
     * 构造函数
     */
    StatementType(String sqlKeyword) {
        this.sqlKeyword = sqlKeyword;
    }
    
    /**
     * 获取SQL关键字
     */
    public String getSqlKeyword() {
        return sqlKeyword;
    }
    
    /**
     * 根据SQL关键字获取语句类型
     */
    public static StatementType fromSqlKeyword(String keyword) {
        if (keyword == null) {
            return UNKNOWN;
        }
        
        String upperKeyword = keyword.toUpperCase().trim();
        for (StatementType type : values()) {
            if (type.sqlKeyword.equals(upperKeyword)) {
                return type;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 检查是否为DDL语句
     */
    public boolean isDDL() {
        switch (this) {
            case CREATE_TABLE:
            case ALTER_TABLE:
            case DROP_TABLE:
            case CREATE_INDEX:
            case DROP_INDEX:
            case CREATE_DATABASE:
            case DROP_DATABASE:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 检查是否为DML语句
     */
    public boolean isDML() {
        switch (this) {
            case SELECT:
            case INSERT:
            case UPDATE:
            case DELETE:
            case REPLACE:
            case INSERT_IGNORE:
            case INSERT_ON_DUPLICATE_KEY_UPDATE:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 检查是否为事务语句
     */
    public boolean isTransaction() {
        switch (this) {
            case BEGIN:
            case COMMIT:
            case ROLLBACK:
                return true;
            default:
                return false;
        }
    }
}
