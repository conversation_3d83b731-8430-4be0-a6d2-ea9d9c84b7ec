package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR字面量类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/literals.html
 */
public class IRLiteral implements IRExpression {
    
    private final Object value;
    private final LiteralType literalType;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRLiteral(Object value, LiteralType literalType) {
        this.value = value;
        this.literalType = literalType;
    }
    
    @Override
    public ExpressionType getExpressionType() {
        return ExpressionType.LITERAL;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.LITERAL;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public Object getValue() { return value; }
    public LiteralType getLiteralType() { return literalType; }
    
    /**
     * 字面量类型枚举
     */
    public enum LiteralType {
        STRING,
        INTEGER,
        DECIMAL,
        BOOLEAN,
        NULL,
        DATE,
        TIME,
        TIMESTAMP
    }
}