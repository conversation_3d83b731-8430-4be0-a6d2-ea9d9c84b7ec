package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR列引用类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/identifiers.html
 */
public class IRColumnReference implements IRExpression {

    private final String tableName;
    private final String columnName;
    private final String alias;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRColumnReference(String tableName, String columnName, String alias) {
        this.tableName = tableName;
        this.columnName = columnName;
        this.alias = alias;
    }

    /**
     * 构造函数（无别名）
     */
    public IRColumnReference(String tableName, String columnName) {
        this(tableName, columnName, null);
    }

    /**
     * 构造函数（仅列名）
     */
    public IRColumnReference(String columnName) {
        this(null, columnName, null);
    }

    @Override
    public ExpressionType getExpressionType() {
        return ExpressionType.COLUMN_REFERENCE;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.COLUMN_REFERENCE;
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public String getTableName() { return tableName; }
    public String getColumnName() { return columnName; }
    public String getAlias() { return alias; }

    /**
     * 获取完全限定名
     */
    public String getQualifiedName() {
        if (tableName != null) {
            return tableName + "." + columnName;
        }
        return columnName;
    }
}