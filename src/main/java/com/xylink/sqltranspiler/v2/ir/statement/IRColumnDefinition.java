package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.type.IRDataType;
import java.util.List;

/**
 * IR列定义类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
 */
public class IRColumnDefinition implements IRNode {

    private final String columnName;
    private final IRDataType dataType;
    private final boolean nullable;
    private final String defaultValue;
    private final boolean autoIncrement;
    private final String comment;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRColumnDefinition(String columnName, IRDataType dataType, boolean nullable,
                             String defaultValue, boolean autoIncrement, String comment) {
        this.columnName = columnName;
        this.dataType = dataType;
        this.nullable = nullable;
        this.defaultValue = defaultValue;
        this.autoIncrement = autoIncrement;
        this.comment = comment;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加COLUMN_DEFINITION类型
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public String getColumnName() { return columnName; }
    public IRDataType getDataType() { return dataType; }
    public boolean isNullable() { return nullable; }
    public String getDefaultValue() { return defaultValue; }
    public boolean isAutoIncrement() { return autoIncrement; }
    public String getComment() { return comment; }
}