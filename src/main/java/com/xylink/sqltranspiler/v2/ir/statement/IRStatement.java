package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRNode;

/**
 * IR语句接口
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html
 */
public interface IRStatement extends IRNode {

    /**
     * 获取语句类型
     */
    StatementType getStatementType();

    /**
     * 语句类型枚举
     */
    enum StatementType {
        SELECT,
        INSERT,
        UPDATE,
        DELETE,
        CREATE_TABLE,
        ALTER_TABLE,
        DROP_TABLE,
        CREATE_INDEX,
        DROP_INDEX
    }
}