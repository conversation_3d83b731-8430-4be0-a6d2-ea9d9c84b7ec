package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;
import java.util.List;

/**
 * IR INSERT语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/insert.html
 */
public class IRInsertStatement implements IRStatement {
    
    private final String tableName;
    private final List<String> columns;
    private final List<List<IRExpression>> values;
    private final IRSelectStatement selectStatement;
    private final boolean ignore;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数（INSERT VALUES）
     */
    public IRInsertStatement(String tableName, List<String> columns, 
                           List<List<IRExpression>> values, boolean ignore) {
        this.tableName = tableName;
        this.columns = List.copyOf(columns);
        this.values = values != null ? List.copyOf(values) : null;
        this.selectStatement = null;
        this.ignore = ignore;
    }
    
    /**
     * 构造函数（INSERT SELECT）
     */
    public IRInsertStatement(String tableName, List<String> columns, 
                           IRSelectStatement selectStatement, boolean ignore) {
        this.tableName = tableName;
        this.columns = List.copyOf(columns);
        this.values = null;
        this.selectStatement = selectStatement;
        this.ignore = ignore;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.INSERT;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.INSERT;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getTableName() { return tableName; }
    public List<String> getColumns() { return columns; }
    public List<List<IRExpression>> getValues() { return values; }
    public IRSelectStatement getSelectStatement() { return selectStatement; }
    public boolean isIgnore() { return ignore; }
    
    /**
     * 是否为INSERT VALUES语句
     */
    public boolean isInsertValues() {
        return values != null;
    }
    
    /**
     * 是否为INSERT SELECT语句
     */
    public boolean isInsertSelect() {
        return selectStatement != null;
    }
}