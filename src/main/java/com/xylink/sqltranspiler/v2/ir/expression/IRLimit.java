package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR LIMIT子句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRLimit implements IRNode {
    
    private final long limit;
    private final long offset;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRLimit(long limit, long offset) {
        this.limit = limit;
        this.offset = offset;
    }
    
    /**
     * 构造函数（仅限制数量）
     */
    public IRLimit(long limit) {
        this(limit, 0);
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加LIMIT类型
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public long getLimit() { return limit; }
    public long getOffset() { return offset; }
    
    /**
     * 是否有偏移量
     */
    public boolean hasOffset() {
        return offset > 0;
    }
}