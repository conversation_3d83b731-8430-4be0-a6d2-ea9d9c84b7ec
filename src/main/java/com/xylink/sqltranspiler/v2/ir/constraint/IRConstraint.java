package com.xylink.sqltranspiler.v2.ir.constraint;

import com.xylink.sqltranspiler.v2.ir.IRNode;

/**
 * IR约束接口
 *
 * 根据官方文档实现
 */
public interface IRConstraint extends IRNode {

    /**
     * 获取约束名称
     */
    String getConstraintName();

    /**
     * 获取约束类型
     */
    ConstraintType getConstraintType();

    /**
     * 约束类型枚举
     */
    enum ConstraintType {
        PRIMARY_KEY,
        FOREIGN_KEY,
        UNIQUE,
        CHECK,
        NOT_NULL
    }
}