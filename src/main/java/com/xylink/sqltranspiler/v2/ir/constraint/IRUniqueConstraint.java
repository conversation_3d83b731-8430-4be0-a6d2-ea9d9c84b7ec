package com.xylink.sqltranspiler.v2.ir.constraint;

import java.util.List;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR唯一约束类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
 */
public class IRUniqueConstraint implements IRConstraint {

    private final String constraintName;
    private final List<String> columnNames;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRUniqueConstraint(String constraintName, List<String> columnNames) {
        this.constraintName = constraintName;
        this.columnNames = List.copyOf(columnNames);
    }

    /**
     * 构造函数（无约束名）
     */
    public IRUniqueConstraint(List<String> columnNames) {
        this(null, columnNames);
    }

    @Override
    public String getConstraintName() {
        return constraintName;
    }

    @Override
    public ConstraintType getConstraintType() {
        return ConstraintType.UNIQUE;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加UNIQUE_CONSTRAINT类型
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public List<String> getColumnNames() { return columnNames; }
}
