package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR DROP INDEX语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/drop-index.html
 */
public class IRDropIndex implements IRStatement {
    
    private final String indexName;
    private final String tableName;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRDropIndex(String indexName, String tableName) {
        this.indexName = indexName;
        this.tableName = tableName;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_INDEX;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.DROP_INDEX;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getIndexName() { return indexName; }
    public String getTableName() { return tableName; }
}