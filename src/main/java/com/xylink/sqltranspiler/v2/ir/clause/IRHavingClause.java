package com.xylink.sqltranspiler.v2.ir.clause;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;

/**
 * IR HAVING子句类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRHavingClause implements IRNode {

    private final IRExpression condition;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRHavingClause(IRExpression condition) {
        this.condition = condition;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.HAVING_CLAUSE;
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public IRExpression getCondition() { return condition; }
}