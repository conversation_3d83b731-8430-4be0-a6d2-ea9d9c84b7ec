package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR ORDER BY子句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IROrderByClause implements IRNode {
    
    private final List<IROrderByItem> orderByItems;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IROrderByClause(List<IROrderByItem> orderByItems) {
        this.orderByItems = List.copyOf(orderByItems);
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.ORDER_BY_CLAUSE;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public List<IROrderByItem> getOrderByItems() { return orderByItems; }
    
    /**
     * ORDER BY项类
     */
    public static class IROrderByItem {
        private final IRExpression expression;
        private final SortOrder sortOrder;
        
        public IROrderByItem(IRExpression expression, SortOrder sortOrder) {
            this.expression = expression;
            this.sortOrder = sortOrder;
        }
        
        public IRExpression getExpression() { return expression; }
        public SortOrder getSortOrder() { return sortOrder; }
        
        /**
         * 排序顺序枚举
         */
        public enum SortOrder {
            ASC("ASC"),
            DESC("DESC");
            
            private final String sql;
            
            SortOrder(String sql) {
                this.sql = sql;
            }
            
            public String getSql() {
                return sql;
            }
        }
    }
}