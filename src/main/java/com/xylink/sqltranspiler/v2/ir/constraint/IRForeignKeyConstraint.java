package com.xylink.sqltranspiler.v2.ir.constraint;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR外键约束类
 *
 * 根据官方文档实现
 */
public class IRForeignKeyConstraint implements IRConstraint {

    private final String constraintName;
    private final List<String> columns;
    private final String referencedTable;
    private final List<String> referencedColumns;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRForeignKeyConstraint(String constraintName, List<String> columns,
                                 String referencedTable, List<String> referencedColumns) {
        this.constraintName = constraintName;
        this.columns = columns;
        this.referencedTable = referencedTable;
        this.referencedColumns = referencedColumns;
    }

    @Override
    public String getConstraintName() {
        return constraintName;
    }

    @Override
    public ConstraintType getConstraintType() {
        return ConstraintType.FOREIGN_KEY;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加FOREIGN_KEY_CONSTRAINT类型
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public List<String> getColumns() { return columns; }
    public String getReferencedTable() { return referencedTable; }
    public List<String> getReferencedColumns() { return referencedColumns; }
}