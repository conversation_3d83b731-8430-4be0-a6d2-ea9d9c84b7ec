package com.xylink.sqltranspiler.v2.ir;

/**
 * IR节点接口
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public interface IRNode {
    
    /**
     * 节点类型枚举
     */
    enum NodeType {
        // DDL语句
        CREATE_TABLE,
        ALTER_TABLE,
        DROP_TABLE,
        CREATE_INDEX,
        DROP_INDEX,

        // DML语句
        SELECT,
        INSERT,
        UPDATE,
        DELETE,

        // 表达式
        BINARY_EXPRESSION,
        COLUMN_REFERENCE,
        LITERAL,
        FUNCTION_CALL,
        SUBQUERY,

        // 子句和项目
        FROM_CLAUSE,
        WHERE_CLAUSE,
        ORDER_BY_CLAUSE,
        GROUP_BY_CLAUSE,
        HAVING_CLAUSE,
        LIMIT_CLAUSE,
        SELECT_ITEM,
        ORDER_BY_ITEM,

        // 表引用
        TABLE_REFERENCE,

        // 约束
        CHECK_CONSTRAINT,
        NOT_NULL_CONSTRAINT,
        PRIMARY_KEY_CONSTRAINT,
        FOREIGN_KEY_CONSTRAINT,
        UNIQUE_CONSTRAINT,

        // 列定义和索引
        COLUMN_DEFINITION,
        INDEX_DEFINITION,

        // 其他
        UNKNOWN
    }
    
    /**
     * 获取节点类型
     */
    NodeType getNodeType();
    
    /**
     * 获取元数据
     */
    IRMetadata getMetadata();
    
    /**
     * 获取源位置
     */
    SourceLocation getSourceLocation();
}