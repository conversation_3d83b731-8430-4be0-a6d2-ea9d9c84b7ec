package com.xylink.sqltranspiler.v2.ir.constraint;

import java.util.Map;
import java.util.Optional;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * 非空约束的中间表示
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public final class IRNotNullConstraint implements IRConstraint {

    private final String columnName;
    private final Optional<String> constraintName;
    private final SourceLocation sourceLocation;
    private final Map<String, Object> attributes;

    private IRNotNullConstraint(String columnName,
                               Optional<String> constraintName,
                               SourceLocation sourceLocation,
                               Map<String, Object> attributes) {
        this.columnName = columnName;
        this.constraintName = constraintName;
        this.sourceLocation = sourceLocation;
        this.attributes = attributes;
    }
    
    public static IRNotNullConstraint of(String columnName) {
        return new IRNotNullConstraint(columnName, Optional.empty(),
                                      SourceLocation.UNKNOWN, Map.of());
    }
    
    public String getColumnName() { return columnName; }

    @Override
    public NodeType getNodeType() {
        return NodeType.NOT_NULL_CONSTRAINT;
    }

    @Override
    public String getConstraintName() {
        return constraintName.orElse(null);
    }

    @Override
    public ConstraintType getConstraintType() {
        return ConstraintType.NOT_NULL;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return sourceLocation;
    }

    @Override
    public IRMetadata getMetadata() {
        return IRMetadata.empty();
    }
}
