package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.*;
import java.util.List;

/**
 * IR SELECT语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRSelectStatement implements IRStatement {
    
    private final List<IRSelectItem> selectItems;
    private final IRFromClause fromClause;
    private final IRWhereClause whereClause;
    private final IRGroupByClause groupByClause;
    private final IRHavingClause havingClause;
    private final IROrderByClause orderByClause;
    private final IRLimit limitClause;
    private final boolean distinct;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRSelectStatement(List<IRSelectItem> selectItems, IRFromClause fromClause,
                           IRWhereClause whereClause, IRGroupByClause groupByClause,
                           IRHavingClause havingClause, IROrderByClause orderByClause,
                           IRLimit limitClause, boolean distinct) {
        this.selectItems = List.copyOf(selectItems);
        this.fromClause = fromClause;
        this.whereClause = whereClause;
        this.groupByClause = groupByClause;
        this.havingClause = havingClause;
        this.orderByClause = orderByClause;
        this.limitClause = limitClause;
        this.distinct = distinct;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.SELECT;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.SELECT;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public List<IRSelectItem> getSelectItems() { return selectItems; }
    public IRFromClause getFromClause() { return fromClause; }
    public IRWhereClause getWhereClause() { return whereClause; }
    public IRGroupByClause getGroupByClause() { return groupByClause; }
    public IRHavingClause getHavingClause() { return havingClause; }
    public IROrderByClause getOrderByClause() { return orderByClause; }
    public IRLimit getLimitClause() { return limitClause; }
    public boolean isDistinct() { return distinct; }
}