package com.xylink.sqltranspiler.v2.ir.clause;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;
import java.util.List;

/**
 * IR ORDER BY子句类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IROrderByClause implements IRNode {

    private final List<IROrderByItem> orderByItems;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IROrderByClause(List<IROrderByItem> orderByItems) {
        this.orderByItems = orderByItems != null ? List.copyOf(orderByItems) : List.of();
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.ORDER_BY_CLAUSE;
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public List<IROrderByItem> getOrderByItems() { return orderByItems; }

    /**
     * ORDER BY项类
     */
    public static class IROrderByItem implements IRNode {
        private final IRExpression expression;
        private final SortOrder sortOrder;
        private final IRMetadata metadata = new IRMetadata();

        public IROrderByItem(IRExpression expression, SortOrder sortOrder) {
            this.expression = expression;
            this.sortOrder = sortOrder != null ? sortOrder : SortOrder.ASC;
        }

        public IROrderByItem(IRExpression expression) {
            this(expression, SortOrder.ASC);
        }

        @Override
        public NodeType getNodeType() {
            return NodeType.ORDER_BY_ITEM;
        }

        @Override
        public IRMetadata getMetadata() {
            return metadata;
        }

        @Override
        public SourceLocation getSourceLocation() {
            return null;
        }

        public IRExpression getExpression() { return expression; }
        public SortOrder getSortOrder() { return sortOrder; }

        public enum SortOrder {
            ASC, DESC
        }
    }
}