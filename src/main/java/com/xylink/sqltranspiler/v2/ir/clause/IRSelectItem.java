package com.xylink.sqltranspiler.v2.ir.clause;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;

/**
 * IR SELECT项类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRSelectItem implements IRNode {
    
    private final IRExpression expression;
    private final String alias;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRSelectItem(IRExpression expression, String alias) {
        this.expression = expression;
        this.alias = alias;
    }
    
    /**
     * 构造函数（无别名）
     */
    public IRSelectItem(IRExpression expression) {
        this(expression, null);
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.SELECT_ITEM;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public IRExpression getExpression() { return expression; }
    public String getAlias() { return alias; }
    public boolean hasAlias() { return alias != null && !alias.trim().isEmpty(); }
}