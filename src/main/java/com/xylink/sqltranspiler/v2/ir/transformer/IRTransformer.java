package com.xylink.sqltranspiler.v2.ir.transformer;

import com.xylink.sqltranspiler.v2.ir.IRNode;

/**
 * IR节点转换器接口
 * 
 * 支持IR节点的转换操作，用于实现各种优化和重写规则。
 * 
 * 官方文档依据：
 * - Apache Calcite官方文档: https://calcite.apache.org/javadocAggregate/org/apache/calcite/rex/RexShuttle.html
 *   RexShuttle类提供了表达式转换的标准模式
 * - 设计模式: Transformer Pattern
 *   用于在保持对象结构的同时对对象进行转换
 * 
 * 验证日期: 2024-01-15
 */
public interface IRTransformer {
    
    /**
     * 转换IR节点
     * 
     * 默认实现返回原节点，子类可以重写此方法来实现特定的转换逻辑。
     * 
     * @param node 要转换的IR节点
     * @return 转换后的IR节点
     */
    default IRNode transform(IRNode node) {
        return node;
    }
    
    /**
     * 判断是否需要转换指定节点
     * 
     * @param node IR节点
     * @return 是否需要转换
     */
    default boolean shouldTransform(IRNode node) {
        return true;
    }
    
    /**
     * 转换前的预处理
     * 
     * @param node 要转换的节点
     */
    default void beforeTransform(IRNode node) {
        // 默认空实现
    }
    
    /**
     * 转换后的后处理
     * 
     * @param original 原始节点
     * @param transformed 转换后的节点
     */
    default void afterTransform(IRNode original, IRNode transformed) {
        // 默认空实现
    }
}
