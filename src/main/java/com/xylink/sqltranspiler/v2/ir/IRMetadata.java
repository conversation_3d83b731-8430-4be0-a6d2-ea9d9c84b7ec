package com.xylink.sqltranspiler.v2.ir;

import java.util.HashMap;
import java.util.Map;

/**
 * IR元数据类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public class IRMetadata {

    private final Map<String, Object> metadata = new HashMap<>();

    /**
     * 创建空的元数据实例
     */
    public static IRMetadata empty() {
        return new IRMetadata();
    }
    
    /**
     * 设置元数据
     */
    public void set(String key, Object value) {
        metadata.put(key, value);
    }
    
    /**
     * 获取元数据
     */
    public Object get(String key) {
        return metadata.get(key);
    }
    
    /**
     * 检查是否包含指定键
     */
    public boolean contains(String key) {
        return metadata.containsKey(key);
    }
}