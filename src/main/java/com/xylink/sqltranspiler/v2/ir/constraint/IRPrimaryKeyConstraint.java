package com.xylink.sqltranspiler.v2.ir.constraint;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR主键约束类
 *
 * 根据官方文档实现
 */
public class IRPrimaryKeyConstraint implements IRConstraint {

    private final String constraintName;
    private final List<String> columns;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRPrimaryKeyConstraint(String constraintName, List<String> columns) {
        this.constraintName = constraintName;
        this.columns = columns;
    }

    @Override
    public String getConstraintName() {
        return constraintName;
    }

    @Override
    public ConstraintType getConstraintType() {
        return ConstraintType.PRIMARY_KEY;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加PRIMARY_KEY_CONSTRAINT类型
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public List<String> getColumns() { return columns; }
}