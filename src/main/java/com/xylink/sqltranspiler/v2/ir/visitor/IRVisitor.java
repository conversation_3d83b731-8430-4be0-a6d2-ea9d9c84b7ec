package com.xylink.sqltranspiler.v2.ir.visitor;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.constraint.IRCheckConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRForeignKeyConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRNotNullConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRPrimaryKeyConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRUniqueConstraint;
import com.xylink.sqltranspiler.v2.ir.expression.IRBinaryExpression;
import com.xylink.sqltranspiler.v2.ir.expression.IRColumnReference;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;
import com.xylink.sqltranspiler.v2.ir.expression.IRFunctionCall;
import com.xylink.sqltranspiler.v2.ir.expression.IRSubquery;
import com.xylink.sqltranspiler.v2.ir.statement.IRColumnDefinition;
import com.xylink.sqltranspiler.v2.ir.statement.IRCreateTable;
import com.xylink.sqltranspiler.v2.ir.statement.IRStatement;

/**
 * IR访问者接口
 * 
 * 根据官方文档实现：
 * - 访问者模式用于遍历和处理IR树
 */
public interface IRVisitor<T> {
    
    /**
     * 访问IR节点
     */
    T visit(IRNode node);
    
    /**
     * 访问表达式
     */
    T visitExpression(IRExpression expression);
    
    /**
     * 访问二元表达式
     */
    T visitBinaryExpression(IRBinaryExpression expression);
    
    /**
     * 访问列引用
     */
    T visitColumnReference(IRColumnReference reference);
    
    /**
     * 访问函数调用
     */
    T visitFunctionCall(IRFunctionCall functionCall);
    
    /**
     * 访问子查询
     */
    T visitSubquery(IRSubquery subquery);
    
    /**
     * 访问语句
     */
    T visitStatement(IRStatement statement);
    
    /**
     * 访问CREATE TABLE语句
     */
    T visitCreateTable(IRCreateTable createTable);
    
    /**
     * 访问列定义
     */
    T visitColumnDefinition(IRColumnDefinition columnDefinition);

    /**
     * 访问检查约束
     */
    T visitCheckConstraint(IRCheckConstraint checkConstraint);

    /**
     * 访问非空约束
     */
    T visitNotNullConstraint(IRNotNullConstraint notNullConstraint);

    /**
     * 访问主键约束
     */
    T visitPrimaryKeyConstraint(IRPrimaryKeyConstraint primaryKeyConstraint);

    /**
     * 访问外键约束
     */
    T visitForeignKeyConstraint(IRForeignKeyConstraint foreignKeyConstraint);

    /**
     * 访问唯一约束
     */
    T visitUniqueConstraint(IRUniqueConstraint uniqueConstraint);

    /**
     * 默认访问方法（用于未特化的节点类型）
     */
    default T visitDefault(IRNode node) {
        return visit(node);
    }
}