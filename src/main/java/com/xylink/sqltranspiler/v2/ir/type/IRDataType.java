package com.xylink.sqltranspiler.v2.ir.type;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR数据类型类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/data-types.html
 */
public class IRDataType implements IRNode {
    
    private final String typeName;
    private final Integer precision;
    private final Integer scale;
    private final String charset;
    private final String collation;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRDataType(String typeName, Integer precision, Integer scale, String charset, String collation) {
        this.typeName = typeName;
        this.precision = precision;
        this.scale = scale;
        this.charset = charset;
        this.collation = collation;
    }
    
    /**
     * 构造函数（简化版）
     */
    public IRDataType(String typeName) {
        this(typeName, null, null, null, null);
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加DATA_TYPE类型
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getTypeName() { return typeName; }
    public Integer getPrecision() { return precision; }
    public Integer getScale() { return scale; }
    public String getCharset() { return charset; }
    public String getCollation() { return collation; }
    
    /**
     * 是否有精度
     */
    public boolean hasPrecision() {
        return precision != null;
    }
    
    /**
     * 是否有标度
     */
    public boolean hasScale() {
        return scale != null;
    }
}