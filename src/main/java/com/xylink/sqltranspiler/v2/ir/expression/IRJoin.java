package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR连接类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/join.html
 */
public class IRJoin implements IRNode {
    
    private final JoinType joinType;
    private final IRFromClause.IRTableReference leftTable;
    private final IRFromClause.IRTableReference rightTable;
    private final IRExpression joinCondition;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRJoin(JoinType joinType, IRFromClause.IRTableReference leftTable, 
                  IRFromClause.IRTableReference rightTable, IRExpression joinCondition) {
        this.joinType = joinType;
        this.leftTable = leftTable;
        this.rightTable = rightTable;
        this.joinCondition = joinCondition;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加JOIN类型
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public JoinType getJoinType() { return joinType; }
    public IRFromClause.IRTableReference getLeftTable() { return leftTable; }
    public IRFromClause.IRTableReference getRightTable() { return rightTable; }
    public IRExpression getJoinCondition() { return joinCondition; }
    
    /**
     * 连接类型枚举
     */
    public enum JoinType {
        INNER("INNER JOIN"),
        LEFT("LEFT JOIN"),
        RIGHT("RIGHT JOIN"),
        FULL("FULL JOIN"),
        CROSS("CROSS JOIN");
        
        private final String sql;
        
        JoinType(String sql) {
            this.sql = sql;
        }
        
        public String getSql() {
            return sql;
        }
    }
}