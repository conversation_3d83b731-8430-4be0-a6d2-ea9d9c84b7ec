package com.xylink.sqltranspiler.v2.ir.expression;

/**
 * IR二元操作枚举
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/expressions.html
 */
public enum IRBinaryOperation {
    // 算术操作
    ADD("+"),
    SUBTRACT("-"),
    MULTIPLY("*"),
    DIVIDE("/"),
    MODULO("%"),

    // 比较操作
    EQUAL("="),
    NOT_EQUAL("!="),
    LESS_THAN("<"),
    LESS_THAN_OR_EQUAL("<="),
    GREATER_THAN(">"),
    GREATER_THAN_OR_EQUAL(">="),

    // 逻辑操作
    AND("AND"),
    OR("OR"),

    // 字符串操作
    LIKE("LIKE"),
    NOT_LIKE("NOT LIKE"),
    REGEXP("REGEXP"),

    // 其他操作
    IN("IN"),
    NOT_IN("NOT IN"),
    IS("IS"),
    IS_NOT("IS NOT");

    private final String symbol;

    IRBinaryOperation(String symbol) {
        this.symbol = symbol;
    }

    public String getSymbol() {
        return symbol;
    }
}