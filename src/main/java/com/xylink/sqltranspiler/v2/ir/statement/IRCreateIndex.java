package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR CREATE INDEX语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-index.html
 */
public class IRCreateIndex implements IRStatement {
    
    private final String indexName;
    private final String tableName;
    private final List<String> columns;
    private final boolean unique;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRCreateIndex(String indexName, String tableName, List<String> columns, boolean unique) {
        this.indexName = indexName;
        this.tableName = tableName;
        this.columns = List.copyOf(columns);
        this.unique = unique;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_INDEX;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.CREATE_INDEX;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getIndexName() { return indexName; }
    public String getTableName() { return tableName; }
    public List<String> getColumns() { return columns; }
    public boolean isUnique() { return unique; }
}