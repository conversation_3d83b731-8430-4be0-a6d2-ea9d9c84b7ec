package com.xylink.sqltranspiler.v2.ir.constraint;

import java.util.Map;
import java.util.Optional;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;

/**
 * 检查约束的中间表示
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table-check-constraints.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public final class IRCheckConstraint implements IRConstraint {

    private final IRExpression checkExpression;
    private final Optional<String> constraintName;
    private final SourceLocation sourceLocation;
    private final Map<String, Object> attributes;

    private IRCheckConstraint(IRExpression checkExpression,
                             Optional<String> constraintName,
                             SourceLocation sourceLocation,
                             Map<String, Object> attributes) {
        this.checkExpression = checkExpression;
        this.constraintName = constraintName;
        this.sourceLocation = sourceLocation;
        this.attributes = attributes;
    }
    
    public static IRCheckConstraint of(IRExpression checkExpression) {
        return new IRCheckConstraint(checkExpression, Optional.empty(),
                                    SourceLocation.UNKNOWN, Map.of());
    }
    
    public IRExpression getCheckExpression() { return checkExpression; }

    @Override
    public NodeType getNodeType() {
        return NodeType.CHECK_CONSTRAINT;
    }

    @Override
    public String getConstraintName() {
        return constraintName.orElse(null);
    }

    @Override
    public ConstraintType getConstraintType() {
        return ConstraintType.CHECK;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return sourceLocation;
    }

    @Override
    public IRMetadata getMetadata() {
        return IRMetadata.empty();
    }
}
