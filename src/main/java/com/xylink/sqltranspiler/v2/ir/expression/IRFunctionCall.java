package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR函数调用类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/functions.html
 */
public class IRFunctionCall implements IRExpression {
    
    private final String functionName;
    private final List<IRExpression> arguments;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRFunctionCall(String functionName, List<IRExpression> arguments) {
        this.functionName = functionName;
        this.arguments = arguments;
    }
    
    @Override
    public ExpressionType getExpressionType() {
        return ExpressionType.FUNCTION_CALL;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.UNKNOWN; // TODO: 添加FUNCTION_CALL类型
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getFunctionName() { return functionName; }
    public List<IRExpression> getArguments() { return arguments; }
}