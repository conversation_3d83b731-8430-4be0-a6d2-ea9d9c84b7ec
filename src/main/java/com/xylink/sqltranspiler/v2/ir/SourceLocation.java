package com.xylink.sqltranspiler.v2.ir;

/**
 * 源位置类
 * 
 * 根据官方文档实现：
 * - 用于记录IR节点在源代码中的位置信息
 */
public class SourceLocation {
    
    private final int line;
    private final int column;
    private final String fileName;
    
    /**
     * 构造函数
     */
    public SourceLocation(int line, int column, String fileName) {
        this.line = line;
        this.column = column;
        this.fileName = fileName;
    }
    
    /**
     * 构造函数（无文件名）
     */
    public SourceLocation(int line, int column) {
        this(line, column, null);
    }
    
    /**
     * 未知位置常量
     */
    public static final SourceLocation UNKNOWN = new SourceLocation(-1, -1, "unknown");
    
    // Getters
    public int getLine() { return line; }
    public int getColumn() { return column; }
    public String getFileName() { return fileName; }
    
    /**
     * 检查是否为未知位置
     */
    public boolean isUnknown() {
        return line < 0 || column < 0;
    }
    
    @Override
    public String toString() {
        if (fileName != null) {
            return String.format("%s:%d:%d", fileName, line, column);
        } else {
            return String.format("%d:%d", line, column);
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        SourceLocation that = (SourceLocation) obj;
        return line == that.line && 
               column == that.column && 
               java.util.Objects.equals(fileName, that.fileName);
    }
    
    @Override
    public int hashCode() {
        return java.util.Objects.hash(line, column, fileName);
    }
}