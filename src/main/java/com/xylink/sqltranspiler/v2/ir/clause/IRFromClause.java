package com.xylink.sqltranspiler.v2.ir.clause;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR FROM子句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRFromClause implements IRNode {
    
    private final List<IRTableReference> tableReferences;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRFromClause(List<IRTableReference> tableReferences) {
        this.tableReferences = tableReferences != null ? List.copyOf(tableReferences) : List.of();
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.FROM_CLAUSE;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public List<IRTableReference> getTableReferences() { return tableReferences; }
    
    /**
     * 表引用类
     */
    public static class IRTableReference implements IRNode {
        private final String tableName;
        private final String alias;
        private final IRMetadata metadata = new IRMetadata();
        
        public IRTableReference(String tableName, String alias) {
            this.tableName = tableName;
            this.alias = alias;
        }
        
        public IRTableReference(String tableName) {
            this(tableName, null);
        }
        
        @Override
        public NodeType getNodeType() {
            return NodeType.TABLE_REFERENCE;
        }
        
        @Override
        public IRMetadata getMetadata() {
            return metadata;
        }
        
        @Override
        public SourceLocation getSourceLocation() {
            return null;
        }
        
        public String getTableName() { return tableName; }
        public String getAlias() { return alias; }
        public boolean hasAlias() { return alias != null && !alias.trim().isEmpty(); }
    }
}