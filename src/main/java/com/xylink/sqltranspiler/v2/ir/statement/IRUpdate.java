package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;
import com.xylink.sqltranspiler.v2.ir.clause.IRWhereClause;
import java.util.Map;

/**
 * IR UPDATE语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/update.html
 */
public class IRUpdate implements IRStatement {
    
    private final String tableName;
    private final Map<String, IRExpression> setClause;
    private final IRWhereClause whereClause;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRUpdate(String tableName, Map<String, IRExpression> setClause, IRWhereClause whereClause) {
        this.tableName = tableName;
        this.setClause = setClause != null ? Map.copyOf(setClause) : Map.of();
        this.whereClause = whereClause;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.UPDATE;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.UPDATE;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getTableName() { return tableName; }
    public Map<String, IRExpression> getSetClause() { return setClause; }
    public IRWhereClause getWhereClause() { return whereClause; }
}