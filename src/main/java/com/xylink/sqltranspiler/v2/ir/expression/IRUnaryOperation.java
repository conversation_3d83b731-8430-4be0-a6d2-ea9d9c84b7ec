package com.xylink.sqltranspiler.v2.ir.expression;

/**
 * IR一元操作枚举
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/expressions.html
 */
public enum IRUnaryOperation {
    // 逻辑操作
    NOT("NOT"),

    // 算术操作
    PLUS("+"),
    MINUS("-"),

    // 其他操作
    IS_NULL("IS NULL"),
    IS_NOT_NULL("IS NOT NULL");

    private final String symbol;

    IRUnaryOperation(String symbol) {
        this.symbol = symbol;
    }

    public String getSymbol() {
        return symbol;
    }
}