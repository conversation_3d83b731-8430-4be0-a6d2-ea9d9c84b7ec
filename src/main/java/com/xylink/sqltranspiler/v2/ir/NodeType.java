package com.xylink.sqltranspiler.v2.ir;

/**
 * IR节点类型枚举
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html
 * 
 * 定义所有支持的IR节点类型
 */
public enum NodeType {
    
    // 语句类型
    SELECT("SELECT"),
    INSERT("INSERT"),
    UPDATE("UPDATE"),
    DELETE("DELETE"),
    CREATE_TABLE("CREATE_TABLE"),
    ALTER_TABLE("ALTER_TABLE"),
    DROP_TABLE("DROP_TABLE"),
    CREATE_INDEX("CREATE_INDEX"),
    DROP_INDEX("DROP_INDEX"),
    
    // 表达式类型
    BINARY_EXPRESSION("BINARY_EXPRESSION"),
    UNARY_EXPRESSION("UNARY_EXPRESSION"),
    COLUMN_REFERENCE("COLUMN_REFERENCE"),
    LITERAL("LITERAL"),
    FUNCTION_CALL("FUNCTION_CALL"),
    SUBQUERY("SUBQUERY"),
    
    // 子句类型
    SELECT_ITEM("SELECT_ITEM"),
    FROM_CLAUSE("FROM_CLAUSE"),
    WHERE_CLAUSE("WHERE_CLAUSE"),
    GROUP_BY_CLAUSE("GROUP_BY_CLAUSE"),
    HAVING_CLAUSE("HAVING_CLAUSE"),
    ORDER_BY_CLAUSE("ORDER_BY_CLAUSE"),
    ORDER_BY_ITEM("ORDER_BY_ITEM"),
    LIMIT_CLAUSE("LIMIT_CLAUSE"),
    
    // 表引用类型
    TABLE_REFERENCE("TABLE_REFERENCE"),
    JOIN("JOIN"),
    
    // 约束类型
    PRIMARY_KEY_CONSTRAINT("PRIMARY_KEY_CONSTRAINT"),
    FOREIGN_KEY_CONSTRAINT("FOREIGN_KEY_CONSTRAINT"),
    UNIQUE_CONSTRAINT("UNIQUE_CONSTRAINT"),
    CHECK_CONSTRAINT("CHECK_CONSTRAINT"),
    NOT_NULL_CONSTRAINT("NOT_NULL_CONSTRAINT"),
    
    // 数据类型
    DATA_TYPE("DATA_TYPE"),
    
    // 列定义
    COLUMN_DEFINITION("COLUMN_DEFINITION"),
    
    // 索引定义
    INDEX_DEFINITION("INDEX_DEFINITION"),
    
    // 未知类型
    UNKNOWN("UNKNOWN");
    
    private final String name;
    
    /**
     * 构造函数
     */
    NodeType(String name) {
        this.name = name;
    }
    
    /**
     * 获取节点类型名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 根据名称获取节点类型
     */
    public static NodeType fromName(String name) {
        if (name == null) {
            return UNKNOWN;
        }
        
        String upperName = name.toUpperCase().trim();
        for (NodeType type : values()) {
            if (type.name.equals(upperName)) {
                return type;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 检查是否为语句类型
     */
    public boolean isStatement() {
        switch (this) {
            case SELECT:
            case INSERT:
            case UPDATE:
            case DELETE:
            case CREATE_TABLE:
            case ALTER_TABLE:
            case DROP_TABLE:
            case CREATE_INDEX:
            case DROP_INDEX:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 检查是否为表达式类型
     */
    public boolean isExpression() {
        switch (this) {
            case BINARY_EXPRESSION:
            case UNARY_EXPRESSION:
            case COLUMN_REFERENCE:
            case LITERAL:
            case FUNCTION_CALL:
            case SUBQUERY:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 检查是否为子句类型
     */
    public boolean isClause() {
        switch (this) {
            case SELECT_ITEM:
            case FROM_CLAUSE:
            case WHERE_CLAUSE:
            case GROUP_BY_CLAUSE:
            case HAVING_CLAUSE:
            case ORDER_BY_CLAUSE:
            case ORDER_BY_ITEM:
            case LIMIT_CLAUSE:
                return true;
            default:
                return false;
        }
    }
    
    @Override
    public String toString() {
        return name;
    }
}
