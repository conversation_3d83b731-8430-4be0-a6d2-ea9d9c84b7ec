package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR GROUP BY子句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRGroupByClause implements IRNode {
    
    private final List<IRExpression> groupByItems;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRGroupByClause(List<IRExpression> groupByItems) {
        this.groupByItems = List.copyOf(groupByItems);
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.GROUP_BY_CLAUSE;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public List<IRExpression> getGroupByItems() { return groupByItems; }
}