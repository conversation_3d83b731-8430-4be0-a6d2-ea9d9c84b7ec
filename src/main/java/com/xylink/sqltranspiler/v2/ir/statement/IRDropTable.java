package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR DROP TABLE语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/drop-table.html
 */
public class IRDropTable implements IRStatement {
    
    private final String tableName;
    private final boolean ifExists;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRDropTable(String tableName, boolean ifExists) {
        this.tableName = tableName;
        this.ifExists = ifExists;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.DROP_TABLE;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.DROP_TABLE;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getTableName() { return tableName; }
    public boolean isIfExists() { return ifExists; }
}