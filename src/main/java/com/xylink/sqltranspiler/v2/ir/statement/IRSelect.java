package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;
import com.xylink.sqltranspiler.v2.ir.clause.*;
import java.util.List;
import java.util.Optional;

/**
 * IR SELECT语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRSelect implements IRStatement {
    
    private final List<IRSelectItem> selectItems;
    private final IRFromClause fromClause;
    private final IRWhereClause whereClause;
    private final IRGroupByClause groupByClause;
    private final IRHavingClause havingClause;
    private final IROrderByClause orderByClause;
    private final IRLimitClause limitClause;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRSelect(List<IRSelectItem> selectItems, IRFromClause fromClause,
                   IRWhereClause whereClause, IRGroupByClause groupByClause,
                   IRHavingClause havingClause, IROrderByClause orderByClause,
                   IRLimitClause limitClause) {
        this.selectItems = selectItems != null ? List.copyOf(selectItems) : List.of();
        this.fromClause = fromClause;
        this.whereClause = whereClause;
        this.groupByClause = groupByClause;
        this.havingClause = havingClause;
        this.orderByClause = orderByClause;
        this.limitClause = limitClause;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.SELECT;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.SELECT;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public List<IRSelectItem> getSelectItems() { return selectItems; }
    public IRFromClause getFromClause() { return fromClause; }
    public IRWhereClause getWhereClause() { return whereClause; }
    public IRGroupByClause getGroupByClause() { return groupByClause; }
    public IRHavingClause getHavingClause() { return havingClause; }
    public IROrderByClause getOrderByClause() { return orderByClause; }
    public IRLimitClause getLimitClause() { return limitClause; }
    
    /**
     * 获取可选的WHERE子句
     */
    public Optional<IRWhereClause> getOptionalWhereClause() {
        return Optional.ofNullable(whereClause);
    }
    
    /**
     * 获取可选的LIMIT子句
     */
    public Optional<IRLimitClause> getOptionalLimitClause() {
        return Optional.ofNullable(limitClause);
    }
}