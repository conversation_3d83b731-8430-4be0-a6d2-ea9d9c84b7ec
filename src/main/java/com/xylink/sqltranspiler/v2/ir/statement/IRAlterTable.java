package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR ALTER TABLE语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/alter-table.html
 */
public class IRAlterTable implements IRStatement {
    
    private final String tableName;
    private final String alterAction;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRAlterTable(String tableName, String alterAction) {
        this.tableName = tableName;
        this.alterAction = alterAction;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.ALTER_TABLE;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.ALTER_TABLE;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getTableName() { return tableName; }
    public String getAlterAction() { return alterAction; }
}