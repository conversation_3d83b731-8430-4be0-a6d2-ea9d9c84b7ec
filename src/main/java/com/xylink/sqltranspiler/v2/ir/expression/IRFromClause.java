package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import java.util.List;

/**
 * IR FROM子句类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 */
public class IRFromClause implements IRNode {

    private final List<IRTableReference> tables;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRFromClause(List<IRTableReference> tables) {
        this.tables = tables;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.FROM_CLAUSE;
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public List<IRTableReference> getTables() { return tables; }

    /**
     * 表引用接口
     */
    public interface IRTableReference {
        String getTableName();
        String getAlias();
    }

    /**
     * 简单表引用实现
     */
    public static class SimpleTableReference implements IRTableReference {
        private final String tableName;
        private final String alias;

        public SimpleTableReference(String tableName, String alias) {
            this.tableName = tableName;
            this.alias = alias;
        }

        @Override
        public String getTableName() { return tableName; }

        @Override
        public String getAlias() { return alias; }
    }
}