package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.expression.IRExpression;
import java.util.List;
import java.util.Map;

/**
 * IR INSERT语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/insert.html
 */
public class IRInsert implements IRStatement {
    
    private final String tableName;
    private final List<String> columns;
    private final List<List<IRExpression>> values;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRInsert(String tableName, List<String> columns, List<List<IRExpression>> values) {
        this.tableName = tableName;
        this.columns = columns != null ? List.copyOf(columns) : List.of();
        this.values = values != null ? List.copyOf(values) : List.of();
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.INSERT;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.INSERT;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getTableName() { return tableName; }
    public List<String> getColumns() { return columns; }
    public List<List<IRExpression>> getValues() { return values; }
}