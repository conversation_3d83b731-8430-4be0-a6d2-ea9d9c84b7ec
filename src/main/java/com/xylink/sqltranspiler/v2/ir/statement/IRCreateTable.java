package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.constraint.IRConstraint;
import java.util.List;

/**
 * IR CREATE TABLE语句类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
 */
public class IRCreateTable implements IRStatement {

    private final String tableName;
    private final List<IRColumnDefinition> columns;
    private final List<IRConstraint> constraints;
    private final String engine;
    private final String charset;
    private final String comment;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRCreateTable(String tableName, List<IRColumnDefinition> columns,
                        List<IRConstraint> constraints, String engine,
                        String charset, String comment) {
        this.tableName = tableName;
        this.columns = columns;
        this.constraints = constraints;
        this.engine = engine;
        this.charset = charset;
        this.comment = comment;
    }

    @Override
    public StatementType getStatementType() {
        return StatementType.CREATE_TABLE;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.CREATE_TABLE;
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public String getTableName() { return tableName; }
    public List<IRColumnDefinition> getColumns() { return columns; }
    public List<IRConstraint> getConstraints() { return constraints; }
    public String getEngine() { return engine; }
    public String getCharset() { return charset; }
    public String getComment() { return comment; }
}