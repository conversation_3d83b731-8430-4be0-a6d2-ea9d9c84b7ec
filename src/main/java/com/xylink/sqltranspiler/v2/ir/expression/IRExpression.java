package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRNode;

/**
 * IR表达式接口
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/expressions.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public interface IRExpression extends IRNode {

    /**
     * 获取表达式类型
     */
    ExpressionType getExpressionType();

    /**
     * 表达式类型枚举
     */
    enum ExpressionType {
        BINARY,
        UNARY,
        COLUMN_REFERENCE,
        LITERAL,
        FUNCTION_CALL,
        SUBQUERY,
        CASE_EXPRESSION
    }
}