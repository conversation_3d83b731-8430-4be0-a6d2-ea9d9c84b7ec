package com.xylink.sqltranspiler.v2.ir.statement;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.clause.IRWhereClause;

/**
 * IR DELETE语句类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/delete.html
 */
public class IRDelete implements IRStatement {
    
    private final String tableName;
    private final IRWhereClause whereClause;
    private final IRMetadata metadata = new IRMetadata();
    
    /**
     * 构造函数
     */
    public IRDelete(String tableName, IRWhereClause whereClause) {
        this.tableName = tableName;
        this.whereClause = whereClause;
    }
    
    @Override
    public StatementType getStatementType() {
        return StatementType.DELETE;
    }
    
    @Override
    public NodeType getNodeType() {
        return NodeType.DELETE;
    }
    
    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }
    
    // Getters
    public String getTableName() { return tableName; }
    public IRWhereClause getWhereClause() { return whereClause; }
}