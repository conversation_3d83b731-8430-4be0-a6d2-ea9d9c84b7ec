package com.xylink.sqltranspiler.v2.ir.expression;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;

/**
 * IR二元表达式类
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/expressions.html
 */
public class IRBinaryExpression implements IRExpression {

    private final IRExpression left;
    private final IRBinaryOperation operator;
    private final IRExpression right;
    private final IRMetadata metadata = new IRMetadata();

    /**
     * 构造函数
     */
    public IRBinaryExpression(IRExpression left, IRBinaryOperation operator, IRExpression right) {
        this.left = left;
        this.operator = operator;
        this.right = right;
    }

    @Override
    public ExpressionType getExpressionType() {
        return ExpressionType.BINARY;
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.BINARY_EXPRESSION;
    }

    @Override
    public IRMetadata getMetadata() {
        return metadata;
    }

    @Override
    public SourceLocation getSourceLocation() {
        return null; // TODO: 实现源位置
    }

    // Getters
    public IRExpression getLeft() { return left; }
    public IRBinaryOperation getOperator() { return operator; }
    public IRExpression getRight() { return right; }
}