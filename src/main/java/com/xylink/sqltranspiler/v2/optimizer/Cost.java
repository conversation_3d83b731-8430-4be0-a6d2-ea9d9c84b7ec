package com.xylink.sqltranspiler.v2.optimizer;

/**
 * 成本类
 * 
 * 根据官方文档实现：
 * - 用于查询优化器的成本计算
 */
public class Cost {
    
    private final double cpuCost;
    private final double ioCost;
    private final double networkCost;
    
    /**
     * 构造函数
     */
    public Cost(double cpuCost, double ioCost, double networkCost) {
        this.cpuCost = cpuCost;
        this.ioCost = ioCost;
        this.networkCost = networkCost;
    }
    
    /**
     * 构造函数（仅CPU成本）
     */
    public Cost(double cpuCost) {
        this(cpuCost, 0.0, 0.0);
    }
    
    /**
     * 获取总成本
     */
    public double getTotalCost() {
        return cpuCost + ioCost + networkCost;
    }
    
    /**
     * 添加成本
     */
    public Cost add(Cost other) {
        return new Cost(
            this.cpuCost + other.cpuCost,
            this.ioCost + other.ioCost,
            this.networkCost + other.networkCost
        );
    }
    
    // Getters
    public double getCpuCost() { return cpuCost; }
    public double getIoCost() { return ioCost; }
    public double getNetworkCost() { return networkCost; }
    
    @Override
    public String toString() {
        return String.format("Cost{cpu=%.2f, io=%.2f, network=%.2f, total=%.2f}", 
                           cpuCost, ioCost, networkCost, getTotalCost());
    }
}