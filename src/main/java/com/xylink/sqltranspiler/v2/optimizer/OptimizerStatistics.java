package com.xylink.sqltranspiler.v2.optimizer;

import java.time.Duration;
import java.util.Map;
import java.util.HashMap;

/**
 * 优化器统计信息类
 * 
 * 根据官方文档实现：
 * - 用于收集和报告查询优化器的统计信息
 */
public class OptimizerStatistics {
    
    private final long optimizationTimeMs;
    private final int rulesApplied;
    private final int nodesProcessed;
    private final Cost originalCost;
    private final Cost optimizedCost;
    private final Map<String, Object> additionalMetrics;
    
    /**
     * 构造函数
     */
    public OptimizerStatistics(long optimizationTimeMs, int rulesApplied, int nodesProcessed,
                              Cost originalCost, Cost optimizedCost, Map<String, Object> additionalMetrics) {
        this.optimizationTimeMs = optimizationTimeMs;
        this.rulesApplied = rulesApplied;
        this.nodesProcessed = nodesProcessed;
        this.originalCost = originalCost;
        this.optimizedCost = optimizedCost;
        this.additionalMetrics = additionalMetrics != null ? Map.copyOf(additionalMetrics) : Map.of();
    }
    
    /**
     * 获取优化时间
     */
    public Duration getOptimizationTime() {
        return Duration.ofMillis(optimizationTimeMs);
    }
    
    /**
     * 获取成本改善比例
     */
    public double getCostImprovement() {
        if (originalCost == null || optimizedCost == null) {
            return 0.0;
        }
        double originalTotal = originalCost.getTotalCost();
        double optimizedTotal = optimizedCost.getTotalCost();
        if (originalTotal == 0.0) {
            return 0.0;
        }
        return (originalTotal - optimizedTotal) / originalTotal;
    }
    
    // Getters
    public long getOptimizationTimeMs() { return optimizationTimeMs; }
    public int getRulesApplied() { return rulesApplied; }
    public int getNodesProcessed() { return nodesProcessed; }
    public Cost getOriginalCost() { return originalCost; }
    public Cost getOptimizedCost() { return optimizedCost; }
    public Map<String, Object> getAdditionalMetrics() { return additionalMetrics; }
}