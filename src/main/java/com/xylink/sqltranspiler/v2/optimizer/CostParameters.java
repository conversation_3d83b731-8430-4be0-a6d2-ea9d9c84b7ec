package com.xylink.sqltranspiler.v2.optimizer;

/**
 * 成本参数
 * 
 * 定义成本模型中使用的各种参数。
 * 
 * 官方文档依据：
 * - PostgreSQL官方文档: https://www.postgresql.org/docs/current/runtime-config-query.html
 *   PostgreSQL的成本参数配置，提供了实际的成本模型参数参考
 * 
 * 验证日期: 2024-01-15
 */
public final class CostParameters {
    
    // CPU相关参数
    private final double cpuTupleCost;        // 处理每个元组的CPU成本
    private final double cpuIndexTupleCost;   // 处理每个索引元组的CPU成本
    private final double cpuOperatorCost;     // 每个操作符的CPU成本
    
    // I/O相关参数
    private final double seqPageCost;         // 顺序页面访问成本
    private final double randomPageCost;      // 随机页面访问成本
    
    // 内存相关参数
    private final double workMemCost;         // 工作内存成本
    private final double sharedBufferCost;    // 共享缓冲区成本
    
    // 网络相关参数
    private final double networkByteCost;     // 网络传输每字节成本
    
    /**
     * 构造函数
     */
    private CostParameters(double cpuTupleCost,
                          double cpuIndexTupleCost,
                          double cpuOperatorCost,
                          double seqPageCost,
                          double randomPageCost,
                          double workMemCost,
                          double sharedBufferCost,
                          double networkByteCost) {
        this.cpuTupleCost = cpuTupleCost;
        this.cpuIndexTupleCost = cpuIndexTupleCost;
        this.cpuOperatorCost = cpuOperatorCost;
        this.seqPageCost = seqPageCost;
        this.randomPageCost = randomPageCost;
        this.workMemCost = workMemCost;
        this.sharedBufferCost = sharedBufferCost;
        this.networkByteCost = networkByteCost;
    }
    
    /**
     * 创建默认参数（基于PostgreSQL默认值）
     * 
     * @return 默认成本参数
     */
    public static CostParameters defaultParameters() {
        return new CostParameters(
            0.01,   // cpu_tuple_cost
            0.005,  // cpu_index_tuple_cost
            0.0025, // cpu_operator_cost
            1.0,    // seq_page_cost
            4.0,    // random_page_cost
            0.1,    // work_mem_cost
            0.05,   // shared_buffer_cost
            0.001   // network_byte_cost
        );
    }
    
    /**
     * 创建建造者
     * 
     * @return 建造者实例
     */
    public static Builder builder() {
        return new Builder();
    }
    
    // Getter方法
    public double getCpuTupleCost() { return cpuTupleCost; }
    public double getCpuIndexTupleCost() { return cpuIndexTupleCost; }
    public double getCpuOperatorCost() { return cpuOperatorCost; }
    public double getSeqPageCost() { return seqPageCost; }
    public double getRandomPageCost() { return randomPageCost; }
    public double getWorkMemCost() { return workMemCost; }
    public double getSharedBufferCost() { return sharedBufferCost; }
    public double getNetworkByteCost() { return networkByteCost; }
    
    /**
     * 建造者类
     */
    public static class Builder {
        private double cpuTupleCost = 0.01;
        private double cpuIndexTupleCost = 0.005;
        private double cpuOperatorCost = 0.0025;
        private double seqPageCost = 1.0;
        private double randomPageCost = 4.0;
        private double workMemCost = 0.1;
        private double sharedBufferCost = 0.05;
        private double networkByteCost = 0.001;
        
        public Builder cpuTupleCost(double cpuTupleCost) {
            this.cpuTupleCost = cpuTupleCost;
            return this;
        }
        
        public Builder cpuIndexTupleCost(double cpuIndexTupleCost) {
            this.cpuIndexTupleCost = cpuIndexTupleCost;
            return this;
        }
        
        public Builder cpuOperatorCost(double cpuOperatorCost) {
            this.cpuOperatorCost = cpuOperatorCost;
            return this;
        }
        
        public Builder seqPageCost(double seqPageCost) {
            this.seqPageCost = seqPageCost;
            return this;
        }
        
        public Builder randomPageCost(double randomPageCost) {
            this.randomPageCost = randomPageCost;
            return this;
        }
        
        public Builder workMemCost(double workMemCost) {
            this.workMemCost = workMemCost;
            return this;
        }
        
        public Builder sharedBufferCost(double sharedBufferCost) {
            this.sharedBufferCost = sharedBufferCost;
            return this;
        }
        
        public Builder networkByteCost(double networkByteCost) {
            this.networkByteCost = networkByteCost;
            return this;
        }
        
        public CostParameters build() {
            return new CostParameters(cpuTupleCost, cpuIndexTupleCost, cpuOperatorCost,
                                    seqPageCost, randomPageCost, workMemCost,
                                    sharedBufferCost, networkByteCost);
        }
    }
    
    @Override
    public String toString() {
        return String.format("CostParameters{cpuTuple=%.4f, cpuIndex=%.4f, cpuOp=%.4f, " +
                           "seqPage=%.2f, randomPage=%.2f, workMem=%.3f, sharedBuf=%.3f, network=%.4f}",
                           cpuTupleCost, cpuIndexTupleCost, cpuOperatorCost,
                           seqPageCost, randomPageCost, workMemCost, sharedBufferCost, networkByteCost);
    }
}
