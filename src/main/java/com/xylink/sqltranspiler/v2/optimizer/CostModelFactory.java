package com.xylink.sqltranspiler.v2.optimizer;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

import com.xylink.sqltranspiler.v2.dialects.SqlDialectType;
import com.xylink.sqltranspiler.v2.optimizer.dialect.DamengCostModel;
import com.xylink.sqltranspiler.v2.optimizer.dialect.KingbaseCostModel;
import com.xylink.sqltranspiler.v2.optimizer.dialect.ShentongCostModel;

/**
 * 成本模型工厂
 * 
 * 根据数据库方言创建相应的成本模型。
 * 
 * 验证日期: 2024-01-15
 */
public class CostModelFactory {
    
    private static final Map<SqlDialectType, Supplier<CostModel>> COST_MODEL_SUPPLIERS = new HashMap<>();

    static {
        // 注册各种数据库的成本模型
        COST_MODEL_SUPPLIERS.put(SqlDialectType.MYSQL, DefaultCostModel::new);
        COST_MODEL_SUPPLIERS.put(SqlDialectType.DAMENG, DamengCostModel::new);
        COST_MODEL_SUPPLIERS.put(SqlDialectType.KINGBASE, KingbaseCostModel::new);
        COST_MODEL_SUPPLIERS.put(SqlDialectType.SHENTONG, ShentongCostModel::new);
    }
    
    /**
     * 创建成本模型
     *
     * @param dialect 数据库方言
     * @return 成本模型
     */
    public static CostModel createCostModel(SqlDialectType dialect) {
        Supplier<CostModel> supplier = COST_MODEL_SUPPLIERS.get(dialect);
        if (supplier != null) {
            return supplier.get();
        }

        // 默认返回通用成本模型
        return new DefaultCostModel();
    }

    /**
     * 创建带参数的成本模型
     *
     * @param dialect 数据库方言
     * @param parameters 成本参数
     * @return 成本模型
     */
    public static CostModel createCostModel(SqlDialectType dialect, CostParameters parameters) {
        CostModel costModel = createCostModel(dialect);
        costModel.setParameters(parameters);
        return costModel;
    }

    /**
     * 注册自定义成本模型
     *
     * @param dialect 数据库方言
     * @param supplier 成本模型供应商
     */
    public static void registerCostModel(SqlDialectType dialect, Supplier<CostModel> supplier) {
        COST_MODEL_SUPPLIERS.put(dialect, supplier);
    }

    /**
     * 获取支持的方言列表
     *
     * @return 支持的方言数组
     */
    public static SqlDialectType[] getSupportedDialects() {
        return COST_MODEL_SUPPLIERS.keySet().toArray(new SqlDialectType[0]);
    }

    /**
     * 判断是否支持指定方言
     *
     * @param dialect 数据库方言
     * @return 是否支持
     */
    public static boolean isSupported(SqlDialectType dialect) {
        return COST_MODEL_SUPPLIERS.containsKey(dialect);
    }
}
