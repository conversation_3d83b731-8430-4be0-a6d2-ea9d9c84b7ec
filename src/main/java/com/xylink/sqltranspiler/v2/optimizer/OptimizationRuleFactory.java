package com.xylink.sqltranspiler.v2.optimizer;

import com.xylink.sqltranspiler.v2.optimizer.rules.OptimizationRule;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import com.xylink.sqltranspiler.v2.optimizer.rules.SubqueryOptimizationRule;
import com.xylink.sqltranspiler.v2.optimizer.rules.JoinReorderRule;
import com.xylink.sqltranspiler.v2.optimizer.rules.PredicatePushdownRule;

/**
 * 优化规则工厂
 * 
 * 提供各种预定义的优化规则集合。
 * 
 * 验证日期: 2024-01-15
 */
public class OptimizationRuleFactory {
    
    /**
     * 获取所有标准优化规则
     * 
     * @return 标准优化规则列表
     */
    public static List<OptimizationRule> getAllStandardRules() {
        return Arrays.asList(
            new PredicatePushdownRule(),
            new SubqueryOptimizationRule(),
            new JoinReorderRule()
        );
    }
    
    /**
     * 获取基础优化规则
     * 
     * @return 基础优化规则列表
     */
    public static List<OptimizationRule> getBasicRules() {
        return Arrays.asList(
            new PredicatePushdownRule()
        );
    }
    
    /**
     * 获取高级优化规则
     * 
     * @return 高级优化规则列表
     */
    public static List<OptimizationRule> getAdvancedRules() {
        return Arrays.asList(
            new SubqueryOptimizationRule(),
            new JoinReorderRule()
        );
    }
    
    /**
     * 获取按类别分组的优化规则
     *
     * @param category 规则类别
     * @return 指定类别的优化规则列表
     */
    public static List<OptimizationRule> getRulesByCategory(RuleCategory category) {
        List<OptimizationRule> allRules = getAllStandardRules();
        List<OptimizationRule> categoryRules = new ArrayList<>();
        
        for (OptimizationRule rule : allRules) {
            if (rule.getCategory() == category) {
                categoryRules.add(rule);
            }
        }
        
        return categoryRules;
    }
    
    /**
     * 获取按优先级排序的优化规则
     * 
     * @return 按优先级排序的优化规则列表
     */
    public static List<OptimizationRule> getRulesByPriority() {
        List<OptimizationRule> rules = new ArrayList<>(getAllStandardRules());
        
        // 按优先级降序排序
        rules.sort((r1, r2) -> Double.compare(r2.getPriority(), r1.getPriority()));
        
        return rules;
    }
    
    /**
     * 创建自定义规则集合
     * 
     * @return 规则集合建造者
     */
    public static RuleSetBuilder customRuleSet() {
        return new RuleSetBuilder();
    }
    
    /**
     * 规则集合建造者
     */
    public static class RuleSetBuilder {
        private final List<OptimizationRule> rules = new ArrayList<>();
        
        /**
         * 添加谓词下推规则
         * 
         * @return 建造者实例
         */
        public RuleSetBuilder withPredicatePushdown() {
            rules.add(new PredicatePushdownRule());
            return this;
        }
        
        /**
         * 添加子查询优化规则
         * 
         * @return 建造者实例
         */
        public RuleSetBuilder withSubqueryOptimization() {
            rules.add(new SubqueryOptimizationRule());
            return this;
        }
        
        /**
         * 添加JOIN重排规则
         * 
         * @return 建造者实例
         */
        public RuleSetBuilder withJoinReorder() {
            rules.add(new JoinReorderRule());
            return this;
        }
        
        /**
         * 添加自定义规则
         * 
         * @param rule 自定义规则
         * @return 建造者实例
         */
        public RuleSetBuilder withCustomRule(OptimizationRule rule) {
            rules.add(rule);
            return this;
        }
        
        /**
         * 添加多个自定义规则
         * 
         * @param customRules 自定义规则列表
         * @return 建造者实例
         */
        public RuleSetBuilder withCustomRules(List<OptimizationRule> customRules) {
            rules.addAll(customRules);
            return this;
        }
        
        /**
         * 构建规则列表
         * 
         * @return 规则列表
         */
        public List<OptimizationRule> build() {
            return new ArrayList<>(rules);
        }
        
        /**
         * 构建按优先级排序的规则列表
         * 
         * @return 按优先级排序的规则列表
         */
        public List<OptimizationRule> buildSorted() {
            List<OptimizationRule> sortedRules = new ArrayList<>(rules);
            sortedRules.sort((r1, r2) -> Double.compare(r2.getPriority(), r1.getPriority()));
            return sortedRules;
        }
    }
}
