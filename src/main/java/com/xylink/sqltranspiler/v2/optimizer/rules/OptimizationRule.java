package com.xylink.sqltranspiler.v2.optimizer.rules;

import com.xylink.sqltranspiler.v2.ir.IRNode;

/**
 * 优化规则接口
 * 
 * 根据官方文档实现：
 * - 用于定义查询优化规则
 */
public interface OptimizationRule {
    
    /**
     * 获取规则名称
     */
    String getRuleName();
    
    /**
     * 检查是否适用于指定节点
     */
    boolean isApplicable(IRNode node);
    
    /**
     * 应用优化规则
     */
    IRNode apply(IRNode node);
    
    /**
     * 获取规则优先级
     */
    int getPriority();
    
    /**
     * 获取规则描述
     */
    String getDescription();

    /**
     * 获取规则类别
     */
    default com.xylink.sqltranspiler.v2.optimizer.RuleCategory getCategory() {
        return com.xylink.sqltranspiler.v2.optimizer.RuleCategory.GENERAL;
    }
}
