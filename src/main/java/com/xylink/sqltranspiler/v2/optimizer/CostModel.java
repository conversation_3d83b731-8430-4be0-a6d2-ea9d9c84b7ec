package com.xylink.sqltranspiler.v2.optimizer;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.optimizer.Cost;

/**
 * 成本模型接口
 * 
 * 定义查询执行成本的计算方法，支持不同数据库的成本估算。
 * 
 * 官方文档依据：
 * - Apache Calcite官方文档: https://calcite.apache.org/javadocAggregate/org/apache/calcite/plan/RelOptCost.html
 *   RelOptCost提供了成本模型的标准接口
 * - PostgreSQL官方文档: https://www.postgresql.org/docs/current/runtime-config-query.html
 *   PostgreSQL的成本参数配置，提供了成本模型的实际应用参考
 * 
 * 验证日期: 2024-01-15
 * 
 * 成本模型考虑因素：
 * 1. CPU成本：计算复杂度
 * 2. I/O成本：磁盘访问次数
 * 3. 网络成本：数据传输量
 * 4. 内存成本：内存使用量
 */
public interface CostModel {
    
    /**
     * 计算IR节点的执行成本
     * 
     * @param node IR节点
     * @param context 优化上下文
     * @return 执行成本
     */
    Cost calculateCost(IRNode node, OptimizationContext context);
    
    /**
     * 比较两个成本
     * 
     * @param cost1 成本1
     * @param cost2 成本2
     * @return 比较结果（负数表示cost1更小，正数表示cost1更大，0表示相等）
     */
    int compareCosts(Cost cost1, Cost cost2);
    
    /**
     * 获取成本模型名称
     * 
     * @return 成本模型名称
     */
    String getModelName();
    
    /**
     * 获取成本模型版本
     * 
     * @return 成本模型版本
     */
    String getModelVersion();
    
    /**
     * 获取成本参数
     * 
     * @return 成本参数
     */
    CostParameters getParameters();
    
    /**
     * 设置成本参数
     * 
     * @param parameters 成本参数
     */
    void setParameters(CostParameters parameters);
}
