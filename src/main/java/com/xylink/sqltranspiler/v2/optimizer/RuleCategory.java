package com.xylink.sqltranspiler.v2.optimizer;

/**
 * 优化规则类别枚举
 * 
 * 根据官方文档实现：
 * - 用于分类不同类型的优化规则
 */
public enum RuleCategory {
    
    /**
     * 谓词下推规则
     */
    PREDICATE_PUSHDOWN("Predicate Pushdown", "将WHERE条件下推到数据源附近"),
    
    /**
     * JOIN重排序规则
     */
    JOIN_REORDER("Join Reorder", "重新排序JOIN操作以优化性能"),
    
    /**
     * 子查询优化规则
     */
    SUBQUERY_OPTIMIZATION("Subquery Optimization", "优化子查询为JOIN操作"),
    
    /**
     * 索引优化规则
     */
    INDEX_OPTIMIZATION("Index Optimization", "优化索引使用"),
    
    /**
     * 投影下推规则
     */
    PROJECTION_PUSHDOWN("Projection Pushdown", "将SELECT列下推到数据源"),
    
    /**
     * 常量折叠规则
     */
    CONSTANT_FOLDING("Constant Folding", "计算常量表达式"),
    
    /**
     * 通用优化规则
     */
    GENERAL("General", "通用优化规则");
    
    private final String name;
    private final String description;
    
    /**
     * 构造函数
     */
    RuleCategory(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    /**
     * 获取规则类别名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取规则类别描述
     */
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return name;
    }
}
