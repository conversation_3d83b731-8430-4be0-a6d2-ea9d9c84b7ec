package com.xylink.sqltranspiler.v2.optimizer;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import java.util.List;

/**
 * 优化结果类
 * 
 * 根据官方文档实现：
 * - 用于表示查询优化的结果
 */
public class OptimizationResult {
    
    private final boolean success;
    private final IRNode optimizedNode;
    private final IRNode originalNode;
    private final Cost cost;
    private final List<String> appliedRules;
    private final String errorMessage;
    
    /**
     * 构造函数
     */
    private OptimizationResult(boolean success, IRNode optimizedNode, IRNode originalNode,
                              Cost cost, List<String> appliedRules, String errorMessage) {
        this.success = success;
        this.optimizedNode = optimizedNode;
        this.originalNode = originalNode;
        this.cost = cost;
        this.appliedRules = appliedRules != null ? List.copyOf(appliedRules) : List.of();
        this.errorMessage = errorMessage;
    }
    
    /**
     * 创建成功的优化结果
     */
    public static OptimizationResult success(IRNode optimizedNode, IRNode originalNode, 
                                           Cost cost, List<String> appliedRules) {
        return new OptimizationResult(true, optimizedNode, originalNode, cost, appliedRules, null);
    }
    
    /**
     * 创建失败的优化结果
     */
    public static OptimizationResult failure(IRNode originalNode, String errorMessage) {
        return new OptimizationResult(false, null, originalNode, null, null, errorMessage);
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public boolean isFailure() { return !success; }
    public IRNode getOptimizedNode() { return optimizedNode; }
    public IRNode getOriginalNode() { return originalNode; }
    public Cost getCost() { return cost; }
    public List<String> getAppliedRules() { return appliedRules; }
    public String getErrorMessage() { return errorMessage; }
}