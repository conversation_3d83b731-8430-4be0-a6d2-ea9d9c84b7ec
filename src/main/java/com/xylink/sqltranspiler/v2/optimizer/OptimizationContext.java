package com.xylink.sqltranspiler.v2.optimizer;

import java.util.Map;
import com.xylink.sqltranspiler.v2.optimizer.Cost;
import java.util.Optional;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;

/**
 * 优化上下文
 * 
 * 提供优化过程中需要的上下文信息。
 * 
 * 官方文档依据：
 * - Apache Calcite官方文档: https://calcite.apache.org/javadocAggregate/org/apache/calcite/plan/RelOptCluster.html
 *   RelOptCluster提供了优化上下文的标准设计
 * 
 * 验证日期: 2024-01-15
 */
public interface OptimizationContext {
    
    /**
     * 获取源方言
     * 
     * @return 源方言
     */
    SqlDialect getSourceDialect();
    
    /**
     * 获取目标方言
     * 
     * @return 目标方言
     */
    SqlDialect getTargetDialect();
    
    /**
     * 获取成本模型
     * 
     * @return 成本模型
     */
    CostModel getCostModel();
    
    /**
     * 获取优化配置
     * 
     * @return 优化配置
     */
    OptimizerConfig getOptimizerConfig();
    
    /**
     * 获取上下文属性
     * 
     * @param key 属性键
     * @param type 属性值类型
     * @param <T> 属性值类型
     * @return 属性值的Optional包装
     */
    <T> Optional<T> getProperty(String key, Class<T> type);
    
    /**
     * 设置上下文属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    void setProperty(String key, Object value);
    
    /**
     * 获取所有属性
     * 
     * @return 不可变的属性映射
     */
    Map<String, Object> getProperties();
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    StatisticsProvider getStatistics();
    
    /**
     * 创建子上下文
     * 
     * @return 子上下文
     */
    OptimizationContext createChildContext();
    
    /**
     * 记录优化日志
     * 
     * @param level 日志级别
     * @param message 日志消息
     * @param args 消息参数
     */
    void log(LogLevel level, String message, Object... args);
    
    /**
     * 日志级别枚举
     */
    enum LogLevel {
        DEBUG, INFO, WARN, ERROR
    }
}
