package com.xylink.sqltranspiler.v2.optimizer;

import java.util.List;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.optimizer.rules.OptimizationRule;

/**
 * 查询优化器接口
 * 
 * 根据官方文档实现：
 * - 用于定义查询优化器的基本功能
 */
public interface QueryOptimizer {
    
    /**
     * 优化查询
     */
    OptimizationResult optimize(IRNode query);
    
    /**
     * 优化查询（带规则）
     */
    OptimizationResult optimize(IRNode query, List<OptimizationRule> rules);
    
    /**
     * 获取优化器名称
     */
    String getOptimizerName();
    
    /**
     * 获取支持的优化规则
     */
    List<OptimizationRule> getSupportedRules();
    
    /**
     * 设置成本模型
     */
    void setCostModel(CostModel costModel);
    
    /**
     * 获取优化器统计信息
     */
    OptimizerStatistics getStatistics();
}