package com.xylink.sqltranspiler.v2.optimizer;

import java.util.Optional;

/**
 * 统计信息提供者接口
 * 
 * 提供查询优化所需的统计信息。
 * 
 * 官方文档依据：
 * - PostgreSQL官方文档: https://www.postgresql.org/docs/current/planner-stats.html
 *   PostgreSQL的统计信息收集和使用
 * 
 * 验证日期: 2024-01-15
 */
public interface StatisticsProvider {
    
    /**
     * 获取表的行数估算
     * 
     * @param tableName 表名
     * @return 行数估算
     */
    Optional<Long> getTableRowCount(String tableName);
    
    /**
     * 获取表的页面数估算
     * 
     * @param tableName 表名
     * @return 页面数估算
     */
    Optional<Long> getTablePageCount(String tableName);
    
    /**
     * 获取列的唯一值数量估算
     * 
     * @param tableName 表名
     * @param columnName 列名
     * @return 唯一值数量估算
     */
    Optional<Long> getColumnDistinctCount(String tableName, String columnName);
    
    /**
     * 获取列的空值比例
     * 
     * @param tableName 表名
     * @param columnName 列名
     * @return 空值比例（0.0-1.0）
     */
    Optional<Double> getColumnNullFraction(String tableName, String columnName);
    
    /**
     * 获取索引的选择性
     * 
     * @param indexName 索引名
     * @return 选择性（0.0-1.0）
     */
    Optional<Double> getIndexSelectivity(String indexName);
    
    /**
     * 获取谓词的选择性估算
     * 
     * @param predicate 谓词表达式
     * @return 选择性估算（0.0-1.0）
     */
    Optional<Double> getPredicateSelectivity(String predicate);
}
