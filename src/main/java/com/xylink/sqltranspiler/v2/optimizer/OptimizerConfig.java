package com.xylink.sqltranspiler.v2.optimizer;

/**
 * 优化器配置
 * 
 * 定义优化器的各种配置参数。
 * 
 * 验证日期: 2024-01-15
 */
public final class OptimizerConfig {
    
    private final int maxIterations;        // 最大迭代次数
    private final long timeoutMillis;       // 超时时间（毫秒）
    private final boolean enableLogging;    // 是否启用日志
    private final double costThreshold;     // 成本阈值
    private final int maxRuleApplications;  // 最大规则应用次数
    
    /**
     * 构造函数
     */
    private OptimizerConfig(int maxIterations,
                           long timeoutMillis,
                           boolean enableLogging,
                           double costThreshold,
                           int maxRuleApplications) {
        this.maxIterations = maxIterations;
        this.timeoutMillis = timeoutMillis;
        this.enableLogging = enableLogging;
        this.costThreshold = costThreshold;
        this.maxRuleApplications = maxRuleApplications;
    }
    
    /**
     * 创建默认配置
     * 
     * @return 默认配置
     */
    public static OptimizerConfig defaultConfig() {
        return new OptimizerConfig(
            1000,    // maxIterations
            30000,   // timeoutMillis (30秒)
            false,   // enableLogging
            0.01,    // costThreshold
            10000    // maxRuleApplications
        );
    }
    
    /**
     * 创建建造者
     * 
     * @return 建造者实例
     */
    public static Builder builder() {
        return new Builder();
    }
    
    // Getter方法
    public int getMaxIterations() { return maxIterations; }
    public long getTimeoutMillis() { return timeoutMillis; }
    public boolean isEnableLogging() { return enableLogging; }
    public double getCostThreshold() { return costThreshold; }
    public int getMaxRuleApplications() { return maxRuleApplications; }
    
    /**
     * 建造者类
     */
    public static class Builder {
        private int maxIterations = 1000;
        private long timeoutMillis = 30000;
        private boolean enableLogging = false;
        private double costThreshold = 0.01;
        private int maxRuleApplications = 10000;
        
        public Builder maxIterations(int maxIterations) {
            this.maxIterations = maxIterations;
            return this;
        }
        
        public Builder timeoutMillis(long timeoutMillis) {
            this.timeoutMillis = timeoutMillis;
            return this;
        }
        
        public Builder enableLogging(boolean enableLogging) {
            this.enableLogging = enableLogging;
            return this;
        }
        
        public Builder costThreshold(double costThreshold) {
            this.costThreshold = costThreshold;
            return this;
        }
        
        public Builder maxRuleApplications(int maxRuleApplications) {
            this.maxRuleApplications = maxRuleApplications;
            return this;
        }
        
        public OptimizerConfig build() {
            return new OptimizerConfig(maxIterations, timeoutMillis, enableLogging,
                                     costThreshold, maxRuleApplications);
        }
    }
    
    @Override
    public String toString() {
        return String.format("OptimizerConfig{maxIter=%d, timeout=%dms, logging=%s, " +
                           "costThreshold=%.4f, maxRuleApps=%d}",
                           maxIterations, timeoutMillis, enableLogging,
                           costThreshold, maxRuleApplications);
    }
}
