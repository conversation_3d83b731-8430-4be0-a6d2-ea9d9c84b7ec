package com.xylink.sqltranspiler.v2.api;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 转换器统计信息类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public class TranspilerStatistics {
    
    private final AtomicLong totalTranspilations = new AtomicLong(0);
    private final AtomicLong successfulTranspilations = new AtomicLong(0);
    private final AtomicLong failedTranspilations = new AtomicLong(0);
    private final AtomicLong totalExecutionTime = new AtomicLong(0);
    private final Instant createdAt;
    
    /**
     * 构造函数
     */
    public TranspilerStatistics() {
        this.createdAt = Instant.now();
    }
    
    /**
     * 记录成功的转换
     */
    public void recordSuccess(Duration executionTime) {
        totalTranspilations.incrementAndGet();
        successfulTranspilations.incrementAndGet();
        totalExecutionTime.addAndGet(executionTime.toMillis());
    }
    
    /**
     * 记录失败的转换
     */
    public void recordFailure(Duration executionTime) {
        totalTranspilations.incrementAndGet();
        failedTranspilations.incrementAndGet();
        totalExecutionTime.addAndGet(executionTime.toMillis());
    }
    
    /**
     * 获取总转换次数
     */
    public long getTotalTranspilations() {
        return totalTranspilations.get();
    }
    
    /**
     * 获取成功转换次数
     */
    public long getSuccessfulTranspilations() {
        return successfulTranspilations.get();
    }
    
    /**
     * 获取失败转换次数
     */
    public long getFailedTranspilations() {
        return failedTranspilations.get();
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        long total = totalTranspilations.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulTranspilations.get() / total;
    }
    
    /**
     * 获取平均执行时间
     */
    public Duration getAverageExecutionTime() {
        long total = totalTranspilations.get();
        if (total == 0) {
            return Duration.ZERO;
        }
        return Duration.ofMillis(totalExecutionTime.get() / total);
    }
    
    /**
     * 获取总执行时间
     */
    public Duration getTotalExecutionTime() {
        return Duration.ofMillis(totalExecutionTime.get());
    }
    
    /**
     * 获取创建时间
     */
    public Instant getCreatedAt() {
        return createdAt;
    }
    
    /**
     * 获取运行时间
     */
    public Duration getUptime() {
        return Duration.between(createdAt, Instant.now());
    }
    
    /**
     * 重置统计信息
     */
    public void reset() {
        totalTranspilations.set(0);
        successfulTranspilations.set(0);
        failedTranspilations.set(0);
        totalExecutionTime.set(0);
    }
    
    @Override
    public String toString() {
        return String.format(
            "TranspilerStatistics{total=%d, successful=%d, failed=%d, successRate=%.2f%%, avgTime=%s}",
            getTotalTranspilations(),
            getSuccessfulTranspilations(),
            getFailedTranspilations(),
            getSuccessRate() * 100,
            getAverageExecutionTime()
        );
    }
}
