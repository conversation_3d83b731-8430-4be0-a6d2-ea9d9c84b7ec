package com.xylink.sqltranspiler.v2.api;

import java.time.Duration;
import java.util.List;

import com.xylink.sqltranspiler.v2.dialects.SqlDialectType;

/**
 * SQL转换器构建器
 *
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public class TranspilerBuilder {

    private SqlDialectType sourceDialect;
    private SqlDialectType targetDialect;
    private TranspilerConfig.Builder configBuilder = TranspilerConfig.builder();

    /**
     * 私有构造函数
     */
    private TranspilerBuilder() {}

    /**
     * 创建通用构建器
     */
    public static TranspilerBuilder create() {
        return new TranspilerBuilder();
    }

    /**
     * 创建MySQL到达梦的构建器
     */
    public static TranspilerBuilder mysqlToDameng() {
        return new TranspilerBuilder()
            .fromDialect(SqlDialectType.MYSQL)
            .toDialect(SqlDialectType.DAMENG);
    }

    /**
     * 创建MySQL到金仓的构建器
     */
    public static TranspilerBuilder mysqlToKingbase() {
        return new TranspilerBuilder()
            .fromDialect(SqlDialectType.MYSQL)
            .toDialect(SqlDialectType.KINGBASE);
    }

    /**
     * 创建MySQL到神通的构建器
     */
    public static TranspilerBuilder mysqlToShentong() {
        return new TranspilerBuilder()
            .fromDialect(SqlDialectType.MYSQL)
            .toDialect(SqlDialectType.SHENTONG);
    }

    /**
     * 设置源方言
     */
    public TranspilerBuilder fromDialect(SqlDialectType sourceDialect) {
        this.sourceDialect = sourceDialect;
        return this;
    }

    /**
     * 设置目标方言
     */
    public TranspilerBuilder toDialect(SqlDialectType targetDialect) {
        this.targetDialect = targetDialect;
        return this;
    }

    /**
     * 启用/禁用验证
     */
    public TranspilerBuilder withValidation(boolean enabled) {
        configBuilder.validationEnabled(enabled);
        return this;
    }

    /**
     * 启用/禁用优化
     */
    public TranspilerBuilder withOptimization(boolean enabled) {
        configBuilder.optimizationEnabled(enabled);
        return this;
    }

    /**
     * 设置严格模式
     */
    public TranspilerBuilder strictMode(boolean enabled) {
        configBuilder.strictMode(enabled);
        return this;
    }

    /**
     * 设置超时时间
     */
    public TranspilerBuilder timeout(Duration timeout) {
        configBuilder.timeout(timeout);
        return this;
    }

    /**
     * 设置最大迭代次数
     */
    public TranspilerBuilder maxIterations(int maxIterations) {
        configBuilder.maxIterations(maxIterations);
        return this;
    }

    /**
     * 设置属性
     */
    public TranspilerBuilder property(String key, Object value) {
        configBuilder.property(key, value);
        return this;
    }

    /**
     * 设置配置
     */
    public TranspilerBuilder withConfig(TranspilerConfig config) {
        this.configBuilder = TranspilerConfig.builder()
            .validationEnabled(config.isValidationEnabled())
            .optimizationEnabled(config.isOptimizationEnabled())
            .strictMode(config.isStrictMode())
            .timeout(config.getTimeout())
            .maxIterations(config.getMaxIterations())
            .properties(config.getProperties());
        return this;
    }

    /**
     * 构建转换器
     */
    public SqlTranspiler build() {
        validateConfiguration();
        TranspilerConfig config = configBuilder.build();
        return new DefaultSqlTranspiler(sourceDialect, targetDialect, config);
    }

    /**
     * 直接转换（一次性使用）
     */
    public TranspilationResult transpile(String sql) {
        return build().transpile(sql);
    }

    /**
     * 批量转换（一次性使用）
     */
    public List<TranspilationResult> transpileAll(List<String> sqlStatements) {
        return build().transpileAll(sqlStatements);
    }

    /**
     * 验证配置
     */
    private void validateConfiguration() {
        if (sourceDialect == null) {
            throw new IllegalStateException("Source dialect must be specified");
        }
        if (targetDialect == null) {
            throw new IllegalStateException("Target dialect must be specified");
        }
        if (sourceDialect == targetDialect) {
            throw new IllegalStateException("Source and target dialects cannot be the same");
        }
    }
}