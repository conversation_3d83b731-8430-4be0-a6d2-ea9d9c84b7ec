package com.xylink.sqltranspiler.v2.api;

import java.util.List;
import com.xylink.sqltranspiler.v2.dialects.SqlDialectType;

/**
 * SQL转换器接口
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public interface SqlTranspiler {
    
    /**
     * 转换单个SQL语句
     * 
     * @param sql 源SQL语句
     * @return 转换结果
     */
    TranspilationResult transpile(String sql);
    
    /**
     * 批量转换SQL语句
     * 
     * @param sqlStatements SQL语句列表
     * @return 转换结果列表
     */
    List<TranspilationResult> transpileAll(List<String> sqlStatements);
    
    /**
     * 获取源方言类型
     * 
     * @return 源方言类型
     */
    SqlDialectType getSourceDialect();
    
    /**
     * 获取目标方言类型
     * 
     * @return 目标方言类型
     */
    SqlDialectType getTargetDialect();
    
    /**
     * 获取转换器配置
     * 
     * @return 转换器配置
     */
    TranspilerConfig getConfig();
    
    /**
     * 检查是否支持指定的SQL语句类型
     * 
     * @param sqlType SQL语句类型
     * @return 是否支持
     */
    boolean isSupported(String sqlType);
    
    /**
     * 获取转换器统计信息
     * 
     * @return 统计信息
     */
    TranspilerStatistics getStatistics();
}
