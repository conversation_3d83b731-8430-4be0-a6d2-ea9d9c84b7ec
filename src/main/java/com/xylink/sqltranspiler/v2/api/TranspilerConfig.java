package com.xylink.sqltranspiler.v2.api;

import java.time.Duration;
import java.util.Map;
import java.util.HashMap;

/**
 * 转换器配置类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public class TranspilerConfig {
    
    private final boolean validationEnabled;
    private final boolean optimizationEnabled;
    private final boolean strictMode;
    private final Duration timeout;
    private final int maxIterations;
    private final Map<String, Object> properties;
    
    /**
     * 私有构造函数
     */
    private TranspilerConfig(boolean validationEnabled, boolean optimizationEnabled, 
                           boolean strictMode, Duration timeout, int maxIterations,
                           Map<String, Object> properties) {
        this.validationEnabled = validationEnabled;
        this.optimizationEnabled = optimizationEnabled;
        this.strictMode = strictMode;
        this.timeout = timeout;
        this.maxIterations = maxIterations;
        this.properties = new HashMap<>(properties);
    }
    
    /**
     * 创建默认配置
     */
    public static TranspilerConfig defaultConfig() {
        return new Builder().build();
    }
    
    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    // Getters
    public boolean isValidationEnabled() { return validationEnabled; }
    public boolean isOptimizationEnabled() { return optimizationEnabled; }
    public boolean isStrictMode() { return strictMode; }
    public Duration getTimeout() { return timeout; }
    public int getMaxIterations() { return maxIterations; }
    public Map<String, Object> getProperties() { return new HashMap<>(properties); }
    
    /**
     * 获取属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, T defaultValue) {
        return (T) properties.getOrDefault(key, defaultValue);
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private boolean validationEnabled = true;
        private boolean optimizationEnabled = false;
        private boolean strictMode = false;
        private Duration timeout = Duration.ofSeconds(30);
        private int maxIterations = 100;
        private Map<String, Object> properties = new HashMap<>();
        
        public Builder validationEnabled(boolean enabled) {
            this.validationEnabled = enabled;
            return this;
        }
        
        public Builder optimizationEnabled(boolean enabled) {
            this.optimizationEnabled = enabled;
            return this;
        }
        
        public Builder strictMode(boolean enabled) {
            this.strictMode = enabled;
            return this;
        }
        
        public Builder timeout(Duration timeout) {
            this.timeout = timeout;
            return this;
        }
        
        public Builder maxIterations(int maxIterations) {
            this.maxIterations = maxIterations;
            return this;
        }
        
        public Builder property(String key, Object value) {
            this.properties.put(key, value);
            return this;
        }
        
        public Builder properties(Map<String, Object> properties) {
            this.properties.putAll(properties);
            return this;
        }
        
        public TranspilerConfig build() {
            return new TranspilerConfig(validationEnabled, optimizationEnabled, 
                                      strictMode, timeout, maxIterations, properties);
        }
    }
}
