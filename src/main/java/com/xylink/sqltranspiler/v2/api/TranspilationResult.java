package com.xylink.sqltranspiler.v2.api;

import java.util.List;
import java.util.Objects;

/**
 * 转换结果类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public class TranspilationResult {
    
    private final boolean success;
    private final String originalSql;
    private final String targetSql;
    private final String errorMessage;
    private final List<String> appliedRules;
    private final List<String> messages;
    private final Object statistics;
    
    /**
     * 构造函数
     */
    private TranspilationResult(boolean success, String originalSql, String targetSql, 
                               String errorMessage, List<String> appliedRules, 
                               List<String> messages, Object statistics) {
        this.success = success;
        this.originalSql = Objects.requireNonNull(originalSql, "originalSql cannot be null");
        this.targetSql = targetSql;
        this.errorMessage = errorMessage;
        this.appliedRules = appliedRules != null ? List.copyOf(appliedRules) : List.of();
        this.messages = messages != null ? List.copyOf(messages) : List.of();
        this.statistics = statistics;
    }
    
    /**
     * 创建成功的转换结果
     */
    public static TranspilationResult success(String originalSql, String targetSql) {
        return new TranspilationResult(true, originalSql, targetSql, null, null, null, null);
    }
    
    /**
     * 创建成功的转换结果（带详细信息）
     */
    public static TranspilationResult success(String originalSql, String targetSql, 
                                            List<String> appliedRules, List<String> messages, 
                                            Object statistics) {
        return new TranspilationResult(true, originalSql, targetSql, null, appliedRules, messages, statistics);
    }
    
    /**
     * 创建失败的转换结果
     */
    public static TranspilationResult failure(String originalSql, String errorMessage) {
        return new TranspilationResult(false, originalSql, null, errorMessage, null, null, null);
    }
    
    /**
     * 创建失败的转换结果（带详细信息）
     */
    public static TranspilationResult failure(String originalSql, String errorMessage, 
                                            List<String> messages) {
        return new TranspilationResult(false, originalSql, null, errorMessage, null, messages, null);
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public boolean isFailure() { return !success; }
    public String getOriginalSql() { return originalSql; }
    public String getTargetSql() { return targetSql; }
    public String getErrorMessage() { return errorMessage; }
    public List<String> getAppliedRules() { return appliedRules; }
    public List<String> getMessages() { return messages; }
    public Object getStatistics() { return statistics; }
}