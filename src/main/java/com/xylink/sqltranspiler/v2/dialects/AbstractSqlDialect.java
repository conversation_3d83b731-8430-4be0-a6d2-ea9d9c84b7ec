package com.xylink.sqltranspiler.v2.dialects;

import java.util.Set;

/**
 * 抽象SQL方言类
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
public abstract class AbstractSqlDialect implements SqlDialect {
    
    /**
     * 生成标识符引用
     * 
     * @param identifier 标识符
     * @return 引用后的标识符
     */
    protected String quoteIdentifier(String identifier) {
        // 默认使用双引号，子类可以重写
        return "\"" + identifier + "\"";
    }
    
    /**
     * 生成字符串字面量引用
     * 
     * @param literal 字符串字面量
     * @return 引用后的字符串
     */
    protected String quoteLiteral(String literal) {
        // 默认使用单引号，子类可以重写
        return "'" + literal.replace("'", "''") + "'";
    }
    
    /**
     * 检查是否需要引用标识符
     * 
     * @param identifier 标识符
     * @return 是否需要引用
     */
    protected boolean needsQuoting(String identifier) {
        // 检查是否为保留字或包含特殊字符
        return getReservedWords().contains(identifier.toUpperCase()) ||
               !identifier.matches("[a-zA-Z_][a-zA-Z0-9_]*");
    }
    
    /**
     * 获取保留字集合
     * 
     * @return 保留字集合
     */
    protected abstract Set<String> getReservedWords();
}