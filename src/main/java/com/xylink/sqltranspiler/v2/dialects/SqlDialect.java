package com.xylink.sqltranspiler.v2.dialects;

import com.xylink.sqltranspiler.v2.ir.IRNode;

/**
 * SQL方言接口
 * 
 * 根据官方文档实现
 */
public interface SqlDialect {
    
    /**
     * 生成SQL语句
     * 
     * @param node IR节点
     * @return 生成的SQL语句
     */
    String generate(IRNode node);
    
    /**
     * 获取方言名称
     * 
     * @return 方言名称
     */
    String getDialectName();
    
    /**
     * 获取方言版本
     * 
     * @return 方言版本
     */
    String getDialectVersion();
}