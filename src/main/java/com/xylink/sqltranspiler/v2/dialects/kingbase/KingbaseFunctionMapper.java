package com.xylink.sqltranspiler.v2.dialects.kingbase;

import java.util.HashMap;
import java.util.Map;

/**
 * 金仓数据库函数映射器
 * 
 * 根据金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * 处理MySQL函数到金仓函数的映射和转换
 * 
 * 严格遵循官方文档绝对权威原则：
 * - 每个函数映射都基于金仓官方文档的明确说明
 * - 金仓在MySQL兼容模式下支持大部分MySQL函数
 * - 所有转换都有官方文档引用
 */
public class KingbaseFunctionMapper {
    
    /**
     * 基础函数映射表
     * 根据金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
     * 金仓在MySQL兼容模式下支持大部分MySQL函数
     */
    private static final Map<String, String> FUNCTION_MAPPING = new HashMap<>();
    
    static {
        // 在MySQL兼容模式下，金仓支持大部分MySQL函数
        // 根据金仓官方文档，以下函数都直接支持：
        
        // 字符串函数映射
        FUNCTION_MAPPING.put("CONCAT", "CONCAT");           // 字符串连接
        FUNCTION_MAPPING.put("SUBSTRING", "SUBSTRING");     // 字符串截取
        FUNCTION_MAPPING.put("LENGTH", "LENGTH");           // 字符串长度
        FUNCTION_MAPPING.put("UPPER", "UPPER");             // 大写转换
        FUNCTION_MAPPING.put("LOWER", "LOWER");             // 小写转换
        FUNCTION_MAPPING.put("TRIM", "TRIM");               // 去除空格
        FUNCTION_MAPPING.put("LTRIM", "LTRIM");             // 去除左空格
        FUNCTION_MAPPING.put("RTRIM", "RTRIM");             // 去除右空格
        
        // 数学函数映射
        FUNCTION_MAPPING.put("ROUND", "ROUND");             // 四舍五入
        FUNCTION_MAPPING.put("CEIL", "CEIL");               // 向上取整
        FUNCTION_MAPPING.put("CEILING", "CEILING");         // 向上取整
        FUNCTION_MAPPING.put("FLOOR", "FLOOR");             // 向下取整
        FUNCTION_MAPPING.put("ABS", "ABS");                 // 绝对值
        FUNCTION_MAPPING.put("MOD", "MOD");                 // 取模
        FUNCTION_MAPPING.put("POWER", "POWER");             // 幂运算
        FUNCTION_MAPPING.put("SQRT", "SQRT");               // 平方根
        
        // 日期时间函数映射
        FUNCTION_MAPPING.put("NOW", "NOW");                 // 当前时间
        FUNCTION_MAPPING.put("CURDATE", "CURDATE");         // 当前日期
        FUNCTION_MAPPING.put("CURTIME", "CURTIME");         // 当前时间
        FUNCTION_MAPPING.put("YEAR", "YEAR");               // 年份
        FUNCTION_MAPPING.put("MONTH", "MONTH");             // 月份
        FUNCTION_MAPPING.put("DAY", "DAY");                 // 日期
        FUNCTION_MAPPING.put("HOUR", "HOUR");               // 小时
        FUNCTION_MAPPING.put("MINUTE", "MINUTE");           // 分钟
        FUNCTION_MAPPING.put("SECOND", "SECOND");           // 秒
        FUNCTION_MAPPING.put("DATE_FORMAT", "DATE_FORMAT"); // 日期格式化
        
        // 聚合函数映射
        FUNCTION_MAPPING.put("COUNT", "COUNT");             // 计数
        FUNCTION_MAPPING.put("SUM", "SUM");                 // 求和
        FUNCTION_MAPPING.put("AVG", "AVG");                 // 平均值
        FUNCTION_MAPPING.put("MAX", "MAX");                 // 最大值
        FUNCTION_MAPPING.put("MIN", "MIN");                 // 最小值
        
        // MySQL特有函数映射
        // 根据金仓官方文档，在MySQL兼容模式下这些函数都支持
        FUNCTION_MAPPING.put("IF", "IF");                   // 条件函数
        FUNCTION_MAPPING.put("IFNULL", "IFNULL");           // 空值处理
        FUNCTION_MAPPING.put("COALESCE", "COALESCE");       // 返回第一个非空值
        FUNCTION_MAPPING.put("NULLIF", "NULLIF");           // 空值判断
    }
    
    /**
     * 映射SQL中的函数
     * 
     * @param sql 包含MySQL函数的SQL语句
     * @return 转换后的SQL语句
     */
    public String mapFunctions(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }
        
        // 金仓在MySQL兼容模式下支持大部分MySQL函数
        // 根据金仓官方文档，大部分情况下不需要函数映射
        // 因此直接返回原始SQL
        
        return sql;
    }
    
    /**
     * 检查函数是否在金仓中支持
     * 
     * @param functionName 函数名
     * @return 是否支持
     */
    public boolean isFunctionSupported(String functionName) {
        if (functionName == null) {
            return false;
        }
        
        // 金仓在MySQL兼容模式下支持大部分MySQL函数
        return FUNCTION_MAPPING.containsKey(functionName.toUpperCase());
    }
    
    /**
     * 获取函数的金仓等价函数
     * 
     * @param mysqlFunction MySQL函数名
     * @return 金仓函数名
     */
    public String getMappedFunction(String mysqlFunction) {
        if (mysqlFunction == null) {
            return null;
        }
        
        return FUNCTION_MAPPING.getOrDefault(mysqlFunction.toUpperCase(), mysqlFunction);
    }
    
    /**
     * 获取所有函数映射
     * 
     * @return 函数映射表
     */
    public Map<String, String> getAllMappings() {
        return new HashMap<>(FUNCTION_MAPPING);
    }
}
