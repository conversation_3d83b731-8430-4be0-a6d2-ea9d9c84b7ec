package com.xylink.sqltranspiler.v2.dialects.shentong;

import java.util.Map;
import java.util.HashMap;

/**
 * 神通数据库数据类型映射器
 *
 * 根据神通官方文档实现：
 * - 神通官方文档：参考项目内@shentong.md文档
 *
 * 数据类型映射规则：
 * 1. MySQL TINYINT -> 神通 SMALLINT
 * 2. MySQL MEDIUMINT -> 神通 INTEGER
 * 3. MySQL LONGTEXT -> 神通 CLOB
 * 4. MySQL LONGBLOB -> 神通 BLOB
 */
public class ShentongDataTypeMapper {

    private static final Map<String, String> TYPE_MAPPING = new HashMap<>();

    static {
        // 整数类型映射
        TYPE_MAPPING.put("TINYINT", "SMALLINT");
        TYPE_MAPPING.put("MEDIUMINT", "INTEGER");
        TYPE_MAPPING.put("BIGINT", "BIGINT");

        // 字符串类型映射
        TYPE_MAPPING.put("TINYTEXT", "VARCHAR(255)");
        TYPE_MAPPING.put("MEDIUMTEXT", "CLOB");
        TYPE_MAPPING.put("LONGTEXT", "CLOB");

        // 二进制类型映射
        TYPE_MAPPING.put("TINYBLOB", "BLOB");
        TYPE_MAPPING.put("MEDIUMBLOB", "BLOB");
        TYPE_MAPPING.put("LONGBLOB", "BLOB");

        // 日期时间类型映射
        TYPE_MAPPING.put("DATETIME", "TIMESTAMP");
        TYPE_MAPPING.put("TIMESTAMP", "TIMESTAMP");

        // 其他类型映射
        TYPE_MAPPING.put("ENUM", "VARCHAR(255)");
        TYPE_MAPPING.put("SET", "VARCHAR(1000)");
        TYPE_MAPPING.put("JSON", "CLOB"); // 神通数据库不支持JSON类型，转换为CLOB
    }

    /**
     * 映射数据类型
     *
     * @param mysqlType MySQL数据类型
     * @return 神通数据类型
     */
    public String mapDataType(String mysqlType) {
        return TYPE_MAPPING.getOrDefault(mysqlType.toUpperCase(), mysqlType);
    }

    /**
     * 获取所有类型映射
     *
     * @return 类型映射表
     */
    public Map<String, String> getAllMappings() {
        return new HashMap<>(TYPE_MAPPING);
    }
}