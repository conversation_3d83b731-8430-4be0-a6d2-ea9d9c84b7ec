package com.xylink.sqltranspiler.v2.dialects.dameng;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 达梦数据库函数映射器
 * 
 * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 处理MySQL函数到达梦函数的映射和转换
 * 
 * 严格遵循官方文档绝对权威原则：
 * - 每个函数映射都基于达梦官方文档的明确说明
 * - 不支持的函数提供清晰的替代方案
 * - 所有转换都有官方文档引用
 */
public class DamengFunctionMapper {
    
    /**
     * 基础函数映射表
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private static final Map<String, String> FUNCTION_MAPPING = new HashMap<>();
    
    static {
        // 字符串函数映射
        // 根据达梦官方文档，这些函数都支持
        FUNCTION_MAPPING.put("CONCAT", "CONCAT");           // 字符串连接
        FUNCTION_MAPPING.put("SUBSTRING", "SUBSTRING");     // 字符串截取
        FUNCTION_MAPPING.put("LENGTH", "LENGTH");           // 字符串长度
        FUNCTION_MAPPING.put("UPPER", "UPPER");             // 大写转换
        FUNCTION_MAPPING.put("LOWER", "LOWER");             // 小写转换
        FUNCTION_MAPPING.put("TRIM", "TRIM");               // 去除空格
        FUNCTION_MAPPING.put("LTRIM", "LTRIM");             // 去除左空格
        FUNCTION_MAPPING.put("RTRIM", "RTRIM");             // 去除右空格
        
        // 数学函数映射
        // 根据达梦官方文档，这些函数都支持
        FUNCTION_MAPPING.put("ROUND", "ROUND");             // 四舍五入
        FUNCTION_MAPPING.put("CEIL", "CEIL");               // 向上取整
        FUNCTION_MAPPING.put("CEILING", "CEIL");            // 向上取整（别名）
        FUNCTION_MAPPING.put("FLOOR", "FLOOR");             // 向下取整
        FUNCTION_MAPPING.put("ABS", "ABS");                 // 绝对值
        FUNCTION_MAPPING.put("MOD", "MOD");                 // 取模
        FUNCTION_MAPPING.put("POWER", "POWER");             // 幂运算
        FUNCTION_MAPPING.put("SQRT", "SQRT");               // 平方根
        
        // 日期时间函数映射
        // 根据达梦官方文档，这些函数都支持
        FUNCTION_MAPPING.put("NOW", "NOW");                 // 当前时间
        FUNCTION_MAPPING.put("CURDATE", "CURDATE");         // 当前日期
        FUNCTION_MAPPING.put("CURTIME", "CURTIME");         // 当前时间
        FUNCTION_MAPPING.put("YEAR", "YEAR");               // 年份
        FUNCTION_MAPPING.put("MONTH", "MONTH");             // 月份
        FUNCTION_MAPPING.put("DAY", "DAY");                 // 日期
        FUNCTION_MAPPING.put("HOUR", "HOUR");               // 小时
        FUNCTION_MAPPING.put("MINUTE", "MINUTE");           // 分钟
        FUNCTION_MAPPING.put("SECOND", "SECOND");           // 秒
        
        // 聚合函数映射
        // 根据达梦官方文档，这些函数都支持
        FUNCTION_MAPPING.put("COUNT", "COUNT");             // 计数
        FUNCTION_MAPPING.put("SUM", "SUM");                 // 求和
        FUNCTION_MAPPING.put("AVG", "AVG");                 // 平均值
        FUNCTION_MAPPING.put("MAX", "MAX");                 // 最大值
        FUNCTION_MAPPING.put("MIN", "MIN");                 // 最小值
        
        // 标准SQL函数映射
        // 根据达梦官方文档，这些函数都支持
        FUNCTION_MAPPING.put("COALESCE", "COALESCE");       // 返回第一个非空值
        FUNCTION_MAPPING.put("NULLIF", "NULLIF");           // 空值判断
        
        // MySQL特有函数需要特殊处理
        // IFNULL -> NVL (根据达梦官方文档，达梦支持NVL函数)
        FUNCTION_MAPPING.put("IFNULL", "NVL");
    }
    
    /**
     * 映射SQL中的函数
     * 
     * @param sql 包含MySQL函数的SQL语句
     * @return 转换后的SQL语句
     */
    public String mapFunctions(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }
        
        String result = sql;
        
        // 处理基础函数映射
        for (Map.Entry<String, String> entry : FUNCTION_MAPPING.entrySet()) {
            String mysqlFunction = entry.getKey();
            String damengFunction = entry.getValue();
            
            // 使用正则表达式匹配函数调用，确保是完整的函数名
            Pattern pattern = Pattern.compile("\\b" + mysqlFunction + "\\s*\\(", Pattern.CASE_INSENSITIVE);
            result = pattern.matcher(result).replaceAll(damengFunction + "(");
        }
        
        // 处理复杂的MySQL特有函数
        result = handleMysqlSpecificFunctions(result);
        
        return result;
    }
    
    /**
     * 处理MySQL特有函数的转换
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String handleMysqlSpecificFunctions(String sql) {
        String result = sql;
        
        // 处理IF函数：IF(condition, value1, value2) -> CASE WHEN condition THEN value1 ELSE value2 END
        // 根据达梦官方文档，达梦不支持IF函数，需要转换为CASE WHEN
        result = handleIfFunction(result);
        
        // 处理DATE_FORMAT函数
        // 根据达梦官方文档，达梦可能不完全支持MySQL的DATE_FORMAT语法
        result = handleDateFormatFunction(result);
        
        return result;
    }
    
    /**
     * 处理IF函数转换
     * IF(condition, value1, value2) -> CASE WHEN condition THEN value1 ELSE value2 END
     */
    private String handleIfFunction(String sql) {
        // 简化的IF函数转换
        // 在完整实现中，需要更复杂的解析来正确处理嵌套的IF函数
        Pattern ifPattern = Pattern.compile("IF\\s*\\(([^,]+),\\s*([^,]+),\\s*([^)]+)\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = ifPattern.matcher(sql);
        
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String condition = matcher.group(1).trim();
            String value1 = matcher.group(2).trim();
            String value2 = matcher.group(3).trim();
            
            String replacement = String.format("CASE WHEN %s THEN %s ELSE %s END", condition, value1, value2);
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 处理DATE_FORMAT函数转换
     * 根据达梦官方文档，达梦使用不同的日期格式化语法
     */
    private String handleDateFormatFunction(String sql) {
        // 简化的DATE_FORMAT处理
        // 在完整实现中，需要将MySQL的格式字符串转换为达梦的格式字符串
        
        // 如果包含DATE_FORMAT，添加注释说明
        if (sql.toUpperCase().contains("DATE_FORMAT")) {
            return "-- 注意：达梦数据库的日期格式化语法可能与MySQL不同\n" +
                   "-- 原MySQL语句中的DATE_FORMAT可能需要调整\n" +
                   "-- 请参考达梦官方文档的TO_CHAR函数\n" + sql;
        }
        
        return sql;
    }
    
    /**
     * 获取所有函数映射
     * 
     * @return 函数映射表
     */
    public Map<String, String> getAllMappings() {
        return new HashMap<>(FUNCTION_MAPPING);
    }
}
