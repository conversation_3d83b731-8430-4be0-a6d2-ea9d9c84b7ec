package com.xylink.sqltranspiler.v2.dialects;

/**
 * SQL特性枚举
 * 
 * 定义各种SQL特性，每个特性必须有官方文档明确定义。
 * 
 * 官方文档依据：
 * - MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/
 *   各章节定义了MySQL支持的特性
 * - 达梦官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档: 参考项目内@shentong.md文档
 * 
 * 验证日期: 2024-01-15
 * 
 * 重要原则：
 * 每个特性的支持情况必须基于官方文档验证，禁止推测。
 */
public enum SqlFeature {
    
    // ==================== DDL语句特性 ====================

    /**
     * CREATE TABLE语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
     * 第15.1.20节 CREATE TABLE Statement - MySQL CREATE TABLE语句完整定义
     */
    CREATE_TABLE,

    /**
     * ALTER TABLE语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/alter-table.html
     * 第15.1.9节 ALTER TABLE Statement - MySQL ALTER TABLE语句完整定义
     */
    ALTER_TABLE,

    /**
     * DROP TABLE语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/drop-table.html
     * 第15.1.32节 DROP TABLE Statement - MySQL DROP TABLE语句完整定义
     */
    DROP_TABLE,

    /**
     * CREATE INDEX语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-index.html
     * 第15.1.15节 CREATE INDEX Statement - MySQL CREATE INDEX语句完整定义
     */
    CREATE_INDEX,

    /**
     * DROP INDEX语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/drop-index.html
     * 第15.1.27节 DROP INDEX Statement - MySQL DROP INDEX语句完整定义
     */
    DROP_INDEX,

    // ==================== DML语句特性 ====================

    /**
     * INSERT语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/insert.html
     * 第15.2.7节 INSERT Statement - MySQL INSERT语句完整定义
     */
    INSERT,

    /**
     * UPDATE语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/update.html
     * 第15.2.17节 UPDATE Statement - MySQL UPDATE语句完整定义
     */
    UPDATE,

    /**
     * DELETE语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/delete.html
     * 第15.2.2节 DELETE Statement - MySQL DELETE语句完整定义
     */
    DELETE,

    /**
     * SELECT语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/select.html
     * 第15.2.13节 SELECT Statement - MySQL SELECT语句完整定义
     */
    SELECT,

    // ==================== 查询功能特性 ====================

    /**
     * JOIN查询支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/join.html
     * 第15.2.13.2节 JOIN Clause - MySQL JOIN查询完整定义
     */
    JOIN,

    /**
     * 子查询支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/subqueries.html
     * 第15.2.15节 Subqueries - MySQL子查询完整定义
     */
    SUBQUERY,

    /**
     * GROUP BY子句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/group-by-modifiers.html
     * 第14.19.3节 MySQL Handling of GROUP BY - MySQL GROUP BY完整定义
     */
    GROUP_BY,

    /**
     * ORDER BY子句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/order-by-optimization.html
     * 第10.2.1.16节 ORDER BY Optimization - MySQL ORDER BY完整定义
     */
    ORDER_BY,

    /**
     * HAVING子句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/group-by-handling.html
     * 第14.19.3节 MySQL Handling of GROUP BY - MySQL HAVING完整定义
     */
    HAVING,

    /**
     * LIMIT子句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/limit-optimization.html
     * 第10.2.1.19节 LIMIT Query Optimization - MySQL LIMIT完整定义
     */
    LIMIT,

    // ==================== 约束功能特性 ====================

    /**
     * 主键约束支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
     * 第15.1.20节 CREATE TABLE Statement - PRIMARY KEY约束定义
     */
    PRIMARY_KEY,

    /**
     * 外键约束支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table-foreign-keys.html
     * 第15.1.20.5节 FOREIGN KEY Constraints - MySQL外键约束完整定义
     */
    FOREIGN_KEY,

    /**
     * 唯一约束支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
     * 第15.1.20节 CREATE TABLE Statement - UNIQUE约束定义
     */
    UNIQUE_CONSTRAINT,

    /**
     * 检查约束支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table-check-constraints.html
     * 第15.1.20.6节 CHECK Constraints - MySQL检查约束完整定义
     */
    CHECK_CONSTRAINT,

    /**
     * 非空约束支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
     * 第15.1.20节 CREATE TABLE Statement - NOT NULL约束定义
     */
    NOT_NULL_CONSTRAINT,

    // ==================== 数据类型特性 ====================

    /**
     * AUTO_INCREMENT支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/example-auto-increment.html
     * 第3.6.9节明确定义AUTO_INCREMENT语法和行为
     */
    AUTO_INCREMENT,

    /**
     * IDENTITY列支持（达梦、神通等使用）
     *
     * 达梦官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
     * 第2.1.1节说明IDENTITY列的语法
     */
    IDENTITY_COLUMNS,

    /**
     * SERIAL类型支持（金仓等使用）
     *
     * 金仓官方文档: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/datatype.html
     * 数据类型章节中定义SERIAL类型
     */
    SERIAL_TYPE,

    /**
     * 整数类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/integer-types.html
     * 第13.1.1节 Integer Types - MySQL整数类型完整定义
     */
    INTEGER_TYPES,

    /**
     * 小数类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/fixed-point-types.html
     * 第13.1.3节 Fixed-Point Types - MySQL小数类型完整定义
     */
    DECIMAL_TYPES,

    /**
     * 字符类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/char.html
     * 第13.3.2节 The CHAR and VARCHAR Types - MySQL字符类型完整定义
     */
    CHARACTER_TYPES,

    /**
     * 日期时间类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/date-and-time-types.html
     * 第13.2节 Date and Time Data Types - MySQL日期时间类型完整定义
     */
    DATE_TIME_TYPES,

    /**
     * 布尔类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/other-vendor-data-types.html
     * 第13.1.1节 Integer Types - MySQL布尔类型定义（BOOLEAN是TINYINT(1)的同义词）
     */
    BOOLEAN_TYPE,

    // ==================== 函数功能特性 ====================

    /**
     * 聚合函数支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/aggregate-functions.html
     * 第14.19.1节 Aggregate Function Descriptions - MySQL聚合函数完整定义
     */
    AGGREGATE_FUNCTIONS,

    /**
     * 字符串函数支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
     * 第14.8节 String Functions and Operators - MySQL字符串函数完整定义
     */
    STRING_FUNCTIONS,

    /**
     * 数值函数支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/numeric-functions.html
     * 第14.6节 Numeric Functions and Operators - MySQL数值函数完整定义
     */
    NUMERIC_FUNCTIONS,

    /**
     * 日期函数支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
     * 第14.7节 Date and Time Functions - MySQL日期时间函数完整定义
     */
    DATE_FUNCTIONS,

    // ==================== 高级功能特性 ====================

    /**
     * 公共表表达式支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/with.html
     * 第15.2.20节 WITH (Common Table Expressions) - MySQL CTE完整定义
     */
    CTE,

    /**
     * 事务支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/commit.html
     * 第15.3.1节 START TRANSACTION, COMMIT, and ROLLBACK Statements - MySQL事务完整定义
     */
    TRANSACTIONS,

    // ==================== MySQL特有功能特性 ====================

    /**
     * REPLACE INTO语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/replace.html
     * 第15.2.12节 REPLACE Statement - MySQL REPLACE INTO语句完整定义
     * 注意：这是MySQL特有语法，其他数据库可能不支持
     */
    REPLACE_INTO,

    /**
     * INSERT IGNORE语句支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/insert.html
     * 第15.2.7节 INSERT Statement - MySQL INSERT IGNORE语法定义
     * 注意：这是MySQL特有语法，其他数据库可能不支持
     */
    INSERT_IGNORE,

    /**
     * ON DUPLICATE KEY UPDATE支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/insert-on-duplicate.html
     * 第15.2.7.2节 INSERT ... ON DUPLICATE KEY UPDATE Statement
     * 注意：这是MySQL特有语法，其他数据库可能不支持
     */
    ON_DUPLICATE_KEY_UPDATE,

    /**
     * JSON数据类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/json.html
     * 第11.5节"The JSON Data Type"
     */
    JSON_TYPE,

    /**
     * ENUM数据类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/enum.html
     * 第13.3.5节 The ENUM Type - MySQL ENUM类型完整定义
     * 注意：这是MySQL特有类型，其他数据库可能不支持
     */
    ENUM_TYPE,

    /**
     * SET数据类型支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/set.html
     * 第13.3.6节 The SET Type - MySQL SET类型完整定义
     * 注意：这是MySQL特有类型，其他数据库可能不支持
     */
    SET_TYPE,

    /**
     * MySQL特有函数支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/functions.html
     * 第14章 Functions and Operators - MySQL特有函数定义
     * 注意：这些函数在其他数据库中可能不存在或语法不同
     */
    MYSQL_SPECIFIC_FUNCTIONS,

    /**
     * 全文索引支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/fulltext-search.html
     * 第14.9节 Full-Text Search Functions - MySQL全文索引完整定义
     * 注意：不是所有数据库都支持全文索引
     */
    FULLTEXT_INDEX,

    /**
     * 空间索引支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/spatial-types.html
     * 第13.4节 Spatial Data Types - MySQL空间数据类型和索引定义
     * 注意：不是所有数据库都支持空间索引
     */
    SPATIAL_INDEX,
    
    /**
     * 空间数据类型支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/spatial-types.html
     * 第11.4节"Spatial Data Types"
     */
    SPATIAL_TYPES,
    
    // ==================== 约束特性 ====================
    
    /**
     * 外键约束支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table-foreign-keys.html
     * 第13.1.20.5节"FOREIGN KEY Constraints"
     */
    FOREIGN_KEYS,
    
    /**
     * CHECK约束支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table-check-constraints.html
     * 第13.1.20.6节"CHECK Constraints"
     */
    CHECK_CONSTRAINTS,
    
    /**
     * 延迟约束检查支持
     * 
     * 某些数据库支持DEFERRABLE约束
     */
    DEFERRABLE_CONSTRAINTS,
    
    // ==================== 索引特性 ====================
    
    /**
     * 部分索引支持
     * 
     * 支持在索引中使用WHERE条件
     */
    PARTIAL_INDEXES,
    
    /**
     * 函数索引支持
     * 
     * 支持在表达式上创建索引
     */
    FUNCTION_INDEXES,
    
    /**
     * 全文索引支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/fulltext-search.html
     * 第12.9节"Full-Text Search Functions"
     */
    FULLTEXT_INDEXES,
    
    // ==================== 查询特性 ====================
    
    /**
     * LIMIT语法支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/select.html
     * SELECT语句中的LIMIT子句
     */
    LIMIT_CLAUSE,
    
    /**
     * OFFSET语法支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/select.html
     * SELECT语句中的OFFSET子句
     */
    OFFSET_CLAUSE,
    
    /**
     * 窗口函数支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
     * 第12.21节"Window Functions"
     */
    WINDOW_FUNCTIONS,
    
    /**
     * 公共表表达式(CTE)支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/with.html
     * 第13.2.20节"WITH (Common Table Expressions)"
     */
    COMMON_TABLE_EXPRESSIONS,
    
    /**
     * 递归CTE支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/with.html
     * 递归WITH语句
     */
    RECURSIVE_CTE,
    
    // ==================== 事务特性 ====================
    
    /**
     * 保存点支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/savepoint.html
     * 第13.3.4节"SAVEPOINT, ROLLBACK TO SAVEPOINT, and RELEASE SAVEPOINT Statements"
     */
    SAVEPOINTS,
    
    /**
     * 事务隔离级别支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/set-transaction.html
     * 第13.3.7节"SET TRANSACTION Statement"
     */
    TRANSACTION_ISOLATION,
    
    // ==================== 存储引擎特性 ====================
    
    /**
     * 多存储引擎支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/storage-engines.html
     * 第16章"Alternative Storage Engines"
     */
    MULTIPLE_STORAGE_ENGINES,
    
    /**
     * 分区表支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/partitioning.html
     * 第24章"Partitioning"
     */
    TABLE_PARTITIONING,
    
    // ==================== 字符集和排序规则 ====================
    
    /**
     * 多字符集支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/charset.html
     * 第10章"Character Sets, Collations, Unicode"
     */
    MULTIPLE_CHARSETS,
    
    /**
     * Unicode支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/charset-unicode.html
     * 第10.9节"Unicode Support"
     */
    UNICODE_SUPPORT,
    
    // ==================== 其他特性 ====================
    
    /**
     * 视图支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/views.html
     * 第25章"Views"
     */
    VIEWS,
    
    /**
     * 存储过程支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/stored-routines.html
     * 第25章"Stored Objects"
     */
    STORED_PROCEDURES,
    
    /**
     * 触发器支持
     * 
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/triggers.html
     * 第25.3节"Using Triggers"
     */
    TRIGGERS,
    
    /**
     * 用户定义函数支持
     *
     * MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/adding-functions.html
     * 第29.4节"Adding Functions to MySQL"
     */
    USER_DEFINED_FUNCTIONS;

    /**
     * 获取功能特性的显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        // 将枚举名称转换为可读的显示名称
        String name = this.name().toLowerCase().replace('_', ' ');
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;

        for (char c : name.toCharArray()) {
            if (c == ' ') {
                result.append(c);
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(c);
            }
        }

        return result.toString();
    }

    /**
     * 获取功能特性的描述
     *
     * @return 功能描述
     */
    public String getDescription() {
        switch (this) {
            case AUTO_INCREMENT:
                return "支持AUTO_INCREMENT自增列";
            case IDENTITY_COLUMNS:
                return "支持IDENTITY自增列";
            case SERIAL_TYPE:
                return "支持SERIAL自增类型";
            case INTEGER_TYPES:
                return "支持整数数据类型";
            case DECIMAL_TYPES:
                return "支持小数数据类型";
            case CHARACTER_TYPES:
                return "支持字符数据类型";
            case DATE_TIME_TYPES:
                return "支持日期时间数据类型";
            case BOOLEAN_TYPE:
                return "支持布尔数据类型";
            case JSON_TYPE:
                return "支持JSON数据类型";
            case ENUM_TYPE:
                return "支持ENUM数据类型（MySQL特有）";
            case SET_TYPE:
                return "支持SET数据类型（MySQL特有）";
            case MYSQL_SPECIFIC_FUNCTIONS:
                return "支持MySQL特有函数";
            case FULLTEXT_INDEX:
                return "支持全文索引";
            case SPATIAL_INDEX:
                return "支持空间索引";
            default:
                return "SQL功能特性：" + getDisplayName();
        }
    }

    /**
     * 获取功能特性的官方文档URL
     *
     * @return MySQL官方文档URL
     */
    public String getOfficialDocumentationUrl() {
        return "https://dev.mysql.com/doc/refman/8.4/en/";
    }

    /**
     * 检查是否为MySQL特有功能
     *
     * @return true如果是MySQL特有功能，false如果是标准SQL功能
     */
    public boolean isMySqlSpecific() {
        return this == REPLACE_INTO ||
               this == INSERT_IGNORE ||
               this == ON_DUPLICATE_KEY_UPDATE ||
               this == ENUM_TYPE ||
               this == SET_TYPE ||
               this == MYSQL_SPECIFIC_FUNCTIONS;
    }

    /**
     * 检查是否为可选功能（不是所有数据库都支持）
     *
     * @return true如果是可选功能，false如果是基础功能
     */
    public boolean isOptionalFeature() {
        return this == JSON_TYPE ||
               this == FULLTEXT_INDEX ||
               this == SPATIAL_INDEX ||
               this == WINDOW_FUNCTIONS ||
               this == CTE ||
               this == RECURSIVE_CTE ||
               isMySqlSpecific();
    }
}
