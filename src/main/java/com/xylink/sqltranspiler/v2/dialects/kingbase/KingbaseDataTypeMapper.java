package com.xylink.sqltranspiler.v2.dialects.kingbase;

import java.util.Map;
import java.util.HashMap;

/**
 * 金仓数据库数据类型映射器
 *
 * 根据金仓官方文档实现：
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 *
 * 数据类型映射规则：
 * 1. MySQL兼容模式下大部分类型直接支持
 * 2. 特殊类型需要转换
 */
public class KingbaseDataTypeMapper {

    private static final Map<String, String> TYPE_MAPPING = new HashMap<>();

    static {
        // 在MySQL兼容模式下，大部分类型直接支持
        // 只需要映射特殊类型
        TYPE_MAPPING.put("ENUM", "VARCHAR(255)");
        TYPE_MAPPING.put("SET", "VARCHAR(1000)");
        TYPE_MAPPING.put("JSON", "JSONB");
    }

    /**
     * 映射数据类型
     *
     * @param mysqlType MySQL数据类型
     * @return 金仓数据类型
     */
    public String mapDataType(String mysqlType) {
        return TYPE_MAPPING.getOrDefault(mysqlType.toUpperCase(), mysqlType);
    }

    /**
     * 获取所有类型映射
     *
     * @return 类型映射表
     */
    public Map<String, String> getAllMappings() {
        return new HashMap<>(TYPE_MAPPING);
    }
}