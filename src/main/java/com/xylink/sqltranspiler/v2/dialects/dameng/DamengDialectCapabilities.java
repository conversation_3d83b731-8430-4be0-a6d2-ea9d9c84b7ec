package com.xylink.sqltranspiler.v2.dialects.dameng;

import java.util.Set;
import java.util.HashSet;

/**
 * 达梦数据库方言能力类
 * 
 * 根据达梦官方文档实现：
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 定义达梦数据库支持的SQL功能特性
 */
public class DamengDialectCapabilities {
    
    private static final Set<String> SUPPORTED_FEATURES = new HashSet<>();
    
    static {
        // 基础DDL功能
        SUPPORTED_FEATURES.add("CREATE_TABLE");
        SUPPORTED_FEATURES.add("ALTER_TABLE");
        SUPPORTED_FEATURES.add("DROP_TABLE");
        SUPPORTED_FEATURES.add("CREATE_INDEX");
        SUPPORTED_FEATURES.add("DROP_INDEX");
        
        // 基础DML功能
        SUPPORTED_FEATURES.add("SELECT");
        SUPPORTED_FEATURES.add("INSERT");
        SUPPORTED_FEATURES.add("UPDATE");
        SUPPORTED_FEATURES.add("DELETE");
        
        // 查询功能
        SUPPORTED_FEATURES.add("SUBQUERY");
        SUPPORTED_FEATURES.add("JOIN");
        SUPPORTED_FEATURES.add("CTE");
        SUPPORTED_FEATURES.add("WINDOW_FUNCTIONS");
        
        // 数据类型
        SUPPORTED_FEATURES.add("INTEGER_TYPES");
        SUPPORTED_FEATURES.add("DECIMAL_TYPES");
        SUPPORTED_FEATURES.add("CHARACTER_TYPES");
        SUPPORTED_FEATURES.add("DATE_TIME_TYPES");
        SUPPORTED_FEATURES.add("BOOLEAN_TYPE");
        
        // 约束
        SUPPORTED_FEATURES.add("PRIMARY_KEY");
        SUPPORTED_FEATURES.add("FOREIGN_KEY");
        SUPPORTED_FEATURES.add("UNIQUE_CONSTRAINT");
        SUPPORTED_FEATURES.add("CHECK_CONSTRAINT");
        SUPPORTED_FEATURES.add("NOT_NULL_CONSTRAINT");
        
        // 函数
        SUPPORTED_FEATURES.add("AGGREGATE_FUNCTIONS");
        SUPPORTED_FEATURES.add("STRING_FUNCTIONS");
        SUPPORTED_FEATURES.add("NUMERIC_FUNCTIONS");
        SUPPORTED_FEATURES.add("DATE_FUNCTIONS");
        
        // 事务
        SUPPORTED_FEATURES.add("TRANSACTIONS");
        SUPPORTED_FEATURES.add("SAVEPOINTS");
        
        // 高级功能
        SUPPORTED_FEATURES.add("STORED_PROCEDURES");
        SUPPORTED_FEATURES.add("TRIGGERS");
        SUPPORTED_FEATURES.add("VIEWS");
    }
    
    /**
     * 检查是否支持指定功能
     * 
     * @param feature 功能名称
     * @return 是否支持
     */
    public static boolean supports(String feature) {
        return SUPPORTED_FEATURES.contains(feature);
    }
    
    /**
     * 获取所有支持的功能
     * 
     * @return 支持的功能集合
     */
    public static Set<String> getSupportedFeatures() {
        return new HashSet<>(SUPPORTED_FEATURES);
    }
}