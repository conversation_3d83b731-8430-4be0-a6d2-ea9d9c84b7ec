package com.xylink.sqltranspiler.v2.dialects.kingbase;

import java.util.Set;
import java.util.HashSet;

/**
 * 金仓数据库方言能力类
 *
 * 根据金仓官方文档实现：
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 *
 * 定义金仓数据库支持的SQL功能特性
 */
public class KingbaseDialectCapabilities {

    private static final Set<String> SUPPORTED_FEATURES = new HashSet<>();

    static {
        // 金仓数据库在MySQL兼容模式下支持大部分MySQL功能

        // 基础DDL功能
        SUPPORTED_FEATURES.add("CREATE_TABLE");
        SUPPORTED_FEATURES.add("ALTER_TABLE");
        SUPPORTED_FEATURES.add("DROP_TABLE");
        SUPPORTED_FEATURES.add("CREATE_INDEX");
        SUPPORTED_FEATURES.add("DROP_INDEX");

        // 基础DML功能
        SUPPORTED_FEATURES.add("SELECT");
        SUPPORTED_FEATURES.add("INSERT");
        SUPPORTED_FEATURES.add("UPDATE");
        SUPPORTED_FEATURES.add("DELETE");

        // MySQL特有功能（在MySQL兼容模式下支持）
        SUPPORTED_FEATURES.add("REPLACE_INTO");
        SUPPORTED_FEATURES.add("INSERT_IGNORE");
        SUPPORTED_FEATURES.add("ON_DUPLICATE_KEY_UPDATE");

        // 查询功能
        SUPPORTED_FEATURES.add("SUBQUERY");
        SUPPORTED_FEATURES.add("JOIN");
        SUPPORTED_FEATURES.add("CTE");
        SUPPORTED_FEATURES.add("WINDOW_FUNCTIONS");

        // 数据类型
        SUPPORTED_FEATURES.add("INTEGER_TYPES");
        SUPPORTED_FEATURES.add("DECIMAL_TYPES");
        SUPPORTED_FEATURES.add("CHARACTER_TYPES");
        SUPPORTED_FEATURES.add("DATE_TIME_TYPES");
        SUPPORTED_FEATURES.add("BOOLEAN_TYPE");
        SUPPORTED_FEATURES.add("JSON_TYPE");

        // 约束
        SUPPORTED_FEATURES.add("PRIMARY_KEY");
        SUPPORTED_FEATURES.add("FOREIGN_KEY");
        SUPPORTED_FEATURES.add("UNIQUE_CONSTRAINT");
        SUPPORTED_FEATURES.add("CHECK_CONSTRAINT");
        SUPPORTED_FEATURES.add("NOT_NULL_CONSTRAINT");

        // 函数
        SUPPORTED_FEATURES.add("AGGREGATE_FUNCTIONS");
        SUPPORTED_FEATURES.add("STRING_FUNCTIONS");
        SUPPORTED_FEATURES.add("NUMERIC_FUNCTIONS");
        SUPPORTED_FEATURES.add("DATE_FUNCTIONS");
        SUPPORTED_FEATURES.add("MYSQL_SPECIFIC_FUNCTIONS");

        // 事务
        SUPPORTED_FEATURES.add("TRANSACTIONS");
        SUPPORTED_FEATURES.add("SAVEPOINTS");

        // 高级功能
        SUPPORTED_FEATURES.add("STORED_PROCEDURES");
        SUPPORTED_FEATURES.add("TRIGGERS");
        SUPPORTED_FEATURES.add("VIEWS");
        SUPPORTED_FEATURES.add("USER_DEFINED_FUNCTIONS");
    }

    /**
     * 检查是否支持指定功能
     *
     * @param feature 功能名称
     * @return 是否支持
     */
    public static boolean supports(String feature) {
        return SUPPORTED_FEATURES.contains(feature);
    }

    /**
     * 获取所有支持的功能
     *
     * @return 支持的功能集合
     */
    public static Set<String> getSupportedFeatures() {
        return new HashSet<>(SUPPORTED_FEATURES);
    }
}