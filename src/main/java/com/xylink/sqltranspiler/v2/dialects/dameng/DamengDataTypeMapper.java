package com.xylink.sqltranspiler.v2.dialects.dameng;

import java.util.HashMap;
import java.util.Map;

/**
 * 达梦数据库数据类型映射器
 * 
 * 根据达梦官方文档实现：
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
 * 
 * 数据类型映射规则：
 * 1. MySQL TINYINT -> 达梦 SMALLINT
 * 2. MySQL MEDIUMINT -> 达梦 INTEGER
 * 3. MySQL LONGTEXT -> 达梦 CLOB
 * 4. MySQL LONGBLOB -> 达梦 BLOB
 */
public class DamengDataTypeMapper {
    
    private static final Map<String, String> TYPE_MAPPING = new HashMap<>();
    
    static {
        // 整数类型映射
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
        TYPE_MAPPING.put("TINYINT", "SMALLINT");        // MySQL TINYINT(-128~127) -> 达梦 SMALLINT(-32768~32767)
        TYPE_MAPPING.put("MEDIUMINT", "INTEGER");       // MySQL MEDIUMINT(-8388608~8388607) -> 达梦 INTEGER
        TYPE_MAPPING.put("BIGINT", "BIGINT");           // MySQL BIGINT -> 达梦 BIGINT (兼容)
        TYPE_MAPPING.put("INT", "INTEGER");             // MySQL INT -> 达梦 INTEGER (兼容)
        TYPE_MAPPING.put("INTEGER", "INTEGER");         // MySQL INTEGER -> 达梦 INTEGER (兼容)
        TYPE_MAPPING.put("SMALLINT", "SMALLINT");       // MySQL SMALLINT -> 达梦 SMALLINT (兼容)
        TYPE_MAPPING.put("BOOL", "SMALLINT");           // MySQL BOOL -> 达梦 SMALLINT
        TYPE_MAPPING.put("BOOLEAN", "SMALLINT");        // MySQL BOOLEAN -> 达梦 SMALLINT

        // 浮点数类型映射
        TYPE_MAPPING.put("FLOAT", "FLOAT");             // MySQL FLOAT -> 达梦 FLOAT (兼容)
        TYPE_MAPPING.put("DOUBLE", "DOUBLE");           // MySQL DOUBLE -> 达梦 DOUBLE (兼容)
        TYPE_MAPPING.put("REAL", "DOUBLE");             // MySQL REAL -> 达梦 DOUBLE

        // 定点数类型映射
        TYPE_MAPPING.put("DECIMAL", "DECIMAL");         // MySQL DECIMAL -> 达梦 DECIMAL (兼容)
        TYPE_MAPPING.put("NUMERIC", "DECIMAL");         // MySQL NUMERIC -> 达梦 DECIMAL

        // 字符串类型映射
        TYPE_MAPPING.put("CHAR", "CHAR");               // MySQL CHAR -> 达梦 CHAR (兼容)
        TYPE_MAPPING.put("VARCHAR", "VARCHAR");         // MySQL VARCHAR -> 达梦 VARCHAR (兼容)
        TYPE_MAPPING.put("TEXT", "CLOB");               // MySQL TEXT -> 达梦 CLOB
        TYPE_MAPPING.put("TINYTEXT", "VARCHAR(255)");   // MySQL TINYTEXT -> 达梦 VARCHAR(255)
        TYPE_MAPPING.put("MEDIUMTEXT", "CLOB");         // MySQL MEDIUMTEXT -> 达梦 CLOB
        TYPE_MAPPING.put("LONGTEXT", "CLOB");           // MySQL LONGTEXT -> 达梦 CLOB

        // 二进制类型映射
        TYPE_MAPPING.put("BINARY", "BINARY");           // MySQL BINARY -> 达梦 BINARY (兼容)
        TYPE_MAPPING.put("VARBINARY", "VARBINARY");     // MySQL VARBINARY -> 达梦 VARBINARY (兼容)
        TYPE_MAPPING.put("BLOB", "BLOB");               // MySQL BLOB -> 达梦 BLOB (兼容)
        TYPE_MAPPING.put("TINYBLOB", "BLOB");           // MySQL TINYBLOB -> 达梦 BLOB
        TYPE_MAPPING.put("MEDIUMBLOB", "BLOB");         // MySQL MEDIUMBLOB -> 达梦 BLOB
        TYPE_MAPPING.put("LONGBLOB", "BLOB");           // MySQL LONGBLOB -> 达梦 BLOB

        // 日期时间类型映射
        TYPE_MAPPING.put("DATE", "DATE");               // MySQL DATE -> 达梦 DATE (兼容)
        TYPE_MAPPING.put("TIME", "TIME");               // MySQL TIME -> 达梦 TIME (兼容)
        TYPE_MAPPING.put("DATETIME", "TIMESTAMP");      // MySQL DATETIME -> 达梦 TIMESTAMP
        TYPE_MAPPING.put("TIMESTAMP", "TIMESTAMP");     // MySQL TIMESTAMP -> 达梦 TIMESTAMP (兼容)
        TYPE_MAPPING.put("YEAR", "SMALLINT");           // MySQL YEAR -> 达梦 SMALLINT

        // 位类型映射
        TYPE_MAPPING.put("BIT", "BIT");                 // MySQL BIT -> 达梦 BIT (兼容)

        // MySQL特有类型映射
        TYPE_MAPPING.put("ENUM", "VARCHAR(255)");       // MySQL ENUM -> 达梦 VARCHAR(255)
        TYPE_MAPPING.put("SET", "VARCHAR(1000)");       // MySQL SET -> 达梦 VARCHAR(1000)
        TYPE_MAPPING.put("JSON", "CLOB");               // MySQL JSON -> 达梦 CLOB
    }
    
    /**
     * 映射数据类型
     *
     * @param mysqlType MySQL数据类型
     * @return 达梦数据类型
     */
    public String mapDataType(String mysqlType) {
        String upperType = mysqlType.toUpperCase();

        // 特殊处理ENUM类型
        if (upperType.startsWith("ENUM")) {
            return handleEnumType(mysqlType);
        }

        // 特殊处理SET类型
        if (upperType.startsWith("SET")) {
            return handleSetType(mysqlType);
        }

        return TYPE_MAPPING.getOrDefault(upperType, mysqlType);
    }

    /**
     * 处理ENUM类型转换
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     * 达梦不支持ENUM，转换为VARCHAR并建议添加CHECK约束
     */
    private String handleEnumType(String enumType) {
        // 提取ENUM值的数量来确定VARCHAR长度
        // ENUM('value1','value2','value3') -> VARCHAR(255)
        // 可以根据值的长度动态调整，但为简化使用固定长度
        return "VARCHAR(255) -- 原MySQL类型: " + enumType;
    }

    /**
     * 处理SET类型转换
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     * 达梦不支持SET，转换为VARCHAR
     */
    private String handleSetType(String setType) {
        // SET类型可能包含多个值的组合，需要更大的长度
        return "VARCHAR(1000) -- 原MySQL类型: " + setType;
    }
    
    /**
     * 获取所有类型映射
     * 
     * @return 类型映射表
     */
    public Map<String, String> getAllMappings() {
        return new HashMap<>(TYPE_MAPPING);
    }
}