package com.xylink.sqltranspiler.v2.dialects;

import com.xylink.sqltranspiler.v2.ir.statement.IRSelect;
import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.statement.*;
import com.xylink.sqltranspiler.v2.ir.expression.*;

/**
 * 方言生成器接口
 * 
 * 官方文档依据：
 * - Apache Calcite官方文档: https://calcite.apache.org/javadocAggregate/org/apache/calcite/sql/SqlDialect.html
 *   SqlDialect类提供了数据库方言的标准抽象
 * - MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/
 *   作为源方言的参考标准
 * 
 * 验证日期: 2024-01-15
 * 
 * 设计原则：
 * 1. 每个生成方法必须有官方文档支撑
 * 2. 禁止基于推测的SQL生成
 * 3. 所有生成的SQL必须符合目标数据库官方语法规范
 */
public interface DialectGenerator {
    
    /**
     * 获取目标方言
     */
    SqlDialect getTargetDialect();
    
    /**
     * 获取生成器版本
     */
    String getVersion();
    
    /**
     * 生成CREATE TABLE语句
     */
    String generateCreateTable(IRCreateTable createTable);
    
    /**
     * 生成ALTER TABLE语句
     */
    String generateAlterTable(IRAlterTable alterTable);
    
    /**
     * 生成DROP TABLE语句
     */
    String generateDropTable(IRDropTable dropTable);
    
    /**
     * 生成CREATE INDEX语句
     */
    String generateCreateIndex(IRCreateIndex createIndex);
    
    /**
     * 生成DROP INDEX语句
     */
    String generateDropIndex(IRDropIndex dropIndex);
    
    /**
     * 生成SELECT语句
     */
    String generateSelect(IRSelect select);
    
    /**
     * 生成INSERT语句
     */
    String generateInsert(IRInsert insert);
    
    /**
     * 生成UPDATE语句
     */
    String generateUpdate(IRUpdate update);
    
    /**
     * 生成DELETE语句
     */
    String generateDelete(IRDelete delete);
    
    /**
     * 生成表达式
     */
    String generateExpression(IRExpression expression);
    
    /**
     * 生成函数调用
     */
    String generateFunctionCall(IRFunctionCall functionCall);
    
    /**
     * 生成二元表达式
     */
    String generateBinaryExpression(IRBinaryExpression binaryExpression);
    
    /**
     * 生成JOIN表达式
     */
    String generateJoin(IRJoin join);
    
    /**
     * 生成子查询
     */
    String generateSubquery(IRExpression subquery);
    
    /**
     * 生成通用IR节点
     */
    String generate(IRNode node);
    
    /**
     * 检查是否支持指定特性
     */
    boolean supportsFeature(SqlFeature feature);
    
    /**
     * 获取数据类型映射
     */
    DataTypeMapping getDataTypeMapping();
}
