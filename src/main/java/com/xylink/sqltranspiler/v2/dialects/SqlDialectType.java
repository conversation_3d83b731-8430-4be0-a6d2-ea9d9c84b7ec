package com.xylink.sqltranspiler.v2.dialects;

/**
 * SQL方言类型枚举
 * 
 * 官方文档依据：
 * - MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/
 *   MySQL 8.4参考手册 - 源数据库标准
 * - 达梦数据库官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 *   达梦数据库SQL开发指南 - 目标数据库标准
 * - 金仓数据库官方文档: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 *   金仓数据库SQL参考手册 - 目标数据库标准
 * - 神通数据库官方文档: 参考项目内@shentong.md文档
 *   神通数据库SQL参考手册 - 目标数据库标准
 * 
 * 验证日期: 2024-01-15
 * 
 * 设计原则：严格基于官方文档定义支持的数据库方言类型
 */
public enum SqlDialectType {
    
    /**
     * MySQL数据库方言
     * 
     * 官方文档: https://dev.mysql.com/doc/refman/8.4/en/
     */
    MYSQL("MySQL", "MySQL Community Server", "8.4"),
    
    /**
     * 达梦数据库方言
     * 
     * 官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    DAMENG("DM", "达梦数据库", "8.0"),
    
    /**
     * 金仓数据库方言
     * 
     * 官方文档: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
     */
    KINGBASE("KingbaseES", "金仓数据库", "V8"),
    
    /**
     * 神通数据库方言
     *
     * 官方文档: 参考项目内@shentong.md文档
     */
    SHENTONG("Shentong", "神通数据库", "7.0"),

    /**
     * 未知数据库方言
     *
     * 用于表示不支持或未识别的数据库类型
     */
    UNKNOWN("Unknown", "未知数据库", "N/A");
    
    private final String name;
    private final String displayName;
    private final String version;
    
    /**
     * 构造函数
     * 
     * @param name 方言名称
     * @param displayName 显示名称
     * @param version 版本号
     */
    SqlDialectType(String name, String displayName, String version) {
        this.name = name;
        this.displayName = displayName;
        this.version = version;
    }
    
    /**
     * 获取方言名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取版本号
     */
    public String getVersion() {
        return version;
    }
    
    /**
     * 检查是否为源数据库（MySQL）
     */
    public boolean isSource() {
        return this == MYSQL;
    }
    
    /**
     * 检查是否为目标数据库
     */
    public boolean isTarget() {
        return this != MYSQL;
    }
    
    /**
     * 获取官方文档URL
     */
    public String getOfficialDocumentationUrl() {
        switch (this) {
            case MYSQL:
                return "https://dev.mysql.com/doc/refman/8.4/en/";
            case DAMENG:
                return "https://eco.dameng.com/document/dm/zh-cn/sql-dev/";
            case KINGBASE:
                return "https://help.kingbase.com.cn/v8/development/sql-plsql/sql/";
            case SHENTONG:
                return "@shentong.md";
            default:
                return "";
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s %s)", displayName, name, version);
    }
}
