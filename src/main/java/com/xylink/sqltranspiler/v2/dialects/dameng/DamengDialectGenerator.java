package com.xylink.sqltranspiler.v2.dialects.dameng;

import java.util.ArrayList;
import java.util.List;

import com.xylink.sqltranspiler.v2.dialects.SqlDialectGenerator;
import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.constraint.IRCheckConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRForeignKeyConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRPrimaryKeyConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRUniqueConstraint;
import com.xylink.sqltranspiler.v2.ir.statement.IRColumnDefinition;
import com.xylink.sqltranspiler.v2.ir.statement.IRCreateTable;

/**
 * 达梦数据库方言生成器
 * 
 * 根据达梦官方文档实现：
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 负责将IR节点转换为达梦数据库兼容的SQL语句
 */
public class DamengDialectGenerator implements SqlDialectGenerator {
    
    private final DamengDataTypeMapper dataTypeMapper;
    private final DamengFunctionMapper functionMapper;

    /**
     * 构造函数
     */
    public DamengDialectGenerator() {
        this.dataTypeMapper = new DamengDataTypeMapper();
        this.functionMapper = new DamengFunctionMapper();
    }
    
    @Override
    public String generate(IRNode node) {
        if (node == null) {
            return "";
        }

        // 根据官方文档实现节点类型分发逻辑
        // MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
        // 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/

        try {
            switch (node.getNodeType()) {
                case SELECT:
                    return generateSelect(node);
                case INSERT:
                    return generateInsert(node);
                case UPDATE:
                    return generateUpdate(node);
                case DELETE:
                    return generateDelete(node);
                case CREATE_TABLE:
                    return generateCreateTable(node);
                case ALTER_TABLE:
                    return generateAlterTable(node);
                case DROP_TABLE:
                    return generateDropTable(node);
                case CREATE_INDEX:
                    return generateCreateIndex(node);
                case DROP_INDEX:
                    return generateDropIndex(node);
                default:
                    // 对于不支持的节点类型，返回带注释的原始SQL
                    return generateUnsupportedNode(node);
            }
        } catch (Exception e) {
            // 错误处理：返回带错误信息的注释和原始SQL
            return generateErrorComment(node, e);
        }
    }

    /**
     * 生成不支持节点的注释
     */
    private String generateUnsupportedNode(IRNode node) {
        String originalSql = getOriginalSql(node);
        return "-- TODO: 实现从 MySQL Community Server (MySQL 8.4) 到 达梦数据库 (DM 8.0) 的转换\n" +
               originalSql;
    }

    /**
     * 生成错误注释
     */
    private String generateErrorComment(IRNode node, Exception e) {
        String originalSql = getOriginalSql(node);
        return "-- 转换错误: " + e.getMessage() + "\n" +
               "-- TODO: 实现从 MySQL Community Server (MySQL 8.4) 到 达梦数据库 (DM 8.0) 的转换\n" +
               originalSql;
    }

    /**
     * 获取原始SQL
     */
    private String getOriginalSql(IRNode node) {
        if (node.getMetadata() != null && node.getMetadata().get("originalSql") != null) {
            return node.getMetadata().get("originalSql").toString();
        }
        return "-- 无法获取原始SQL";
    }
    
    /**
     * 生成SELECT语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateSelect(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRSelect)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRSelect select =
            (com.xylink.sqltranspiler.v2.ir.statement.IRSelect) node;

        // 对于SELECT语句，达梦数据库与MySQL语法基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. DUAL表：达梦支持DUAL表，与MySQL兼容
        // 2. LIMIT语句：达梦支持LIMIT语法
        // 3. 函数：大部分MySQL函数在达梦中有对应实现
        // 4. JOIN：达梦支持各种JOIN语法
        // 5. 子查询：达梦支持子查询

        // 暂时直接返回原始SQL，因为达梦与MySQL高度兼容
        String originalSql = (String) select.getMetadata().get("originalSql");
        if (originalSql != null) {
            // 处理LIMIT子句的特殊情况（如果需要）
            return processDamengSelectSql(originalSql, select);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦SELECT SQL的特殊情况
     */
    private String processDamengSelectSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRSelect select) {
        StringBuilder sql = new StringBuilder(originalSql);

        // 达梦数据库支持LIMIT语法，与MySQL兼容
        // 根据达梦官方文档，LIMIT语法完全兼容MySQL
        // 因此大部分情况下可以直接使用原始SQL

        // 如果有特殊的函数映射需求，可以在这里处理
        String result = sql.toString();

        // 处理MySQL特有函数到达梦函数的映射（如果需要）
        result = mapMysqlFunctionsToDameng(result);

        return result;
    }

    /**
     * 映射MySQL函数到达梦函数
     */
    private String mapMysqlFunctionsToDameng(String sql) {
        // 使用专门的函数映射器处理MySQL函数到达梦函数的转换
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/

        return functionMapper.mapFunctions(sql);
    }
    
    /**
     * 生成INSERT语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateInsert(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRInsert)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRInsert insert =
            (com.xylink.sqltranspiler.v2.ir.statement.IRInsert) node;

        // 达梦数据库INSERT语句与MySQL基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. 基础INSERT INTO ... VALUES语法
        // 2. 多行INSERT语法
        // 3. INSERT INTO ... SELECT语法
        //
        // 达梦不支持的MySQL特有语法：
        // 1. INSERT IGNORE -> 需要转换为其他方式
        // 2. ON DUPLICATE KEY UPDATE -> 需要转换为MERGE语句
        // 3. REPLACE INTO -> 需要转换为MERGE语句

        // 检查是否是REPLACE INTO语句
        Boolean isReplace = (Boolean) insert.getMetadata().get("isReplace");
        if (isReplace != null && isReplace) {
            // 处理REPLACE INTO语句
            String originalSql = (String) insert.getMetadata().get("originalSql");
            return processDamengReplaceIntoSql(originalSql, insert);
        }

        // 暂时直接返回原始SQL，因为基础INSERT语法兼容
        String originalSql = (String) insert.getMetadata().get("originalSql");
        if (originalSql != null) {
            return processDamengInsertSql(originalSql, insert);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦INSERT SQL的特殊情况
     */
    private String processDamengInsertSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRInsert insert) {
        String upperSql = originalSql.toUpperCase();

        // 检查MySQL特有语法并进行转换
        if (upperSql.contains("INSERT IGNORE")) {
            // 达梦不支持INSERT IGNORE，转换为注释说明
            return "-- 注意：达梦数据库不支持INSERT IGNORE语法\n" +
                   "-- 原MySQL语句：" + originalSql + "\n" +
                   "-- 建议使用MERGE语句或先检查记录是否存在\n" +
                   originalSql.replaceFirst("(?i)INSERT\\s+IGNORE", "INSERT");
        }

        if (upperSql.contains("ON DUPLICATE KEY UPDATE")) {
            // 达梦不支持ON DUPLICATE KEY UPDATE，需要转换为MERGE语句
            return "-- 注意：达梦数据库不支持ON DUPLICATE KEY UPDATE语法\n" +
                   "-- 原MySQL语句：" + originalSql + "\n" +
                   "-- 建议转换为MERGE语句\n" +
                   "-- TODO: 实现MERGE语句转换\n" +
                   originalSql.replaceFirst("(?i)\\s+ON\\s+DUPLICATE\\s+KEY\\s+UPDATE.*", "");
        }

        if (upperSql.startsWith("REPLACE INTO")) {
            // 达梦不支持REPLACE INTO，需要转换为MERGE语句
            return "-- 注意：达梦数据库不支持REPLACE INTO语法\n" +
                   "-- 原MySQL语句：" + originalSql + "\n" +
                   "-- 建议转换为MERGE语句\n" +
                   "-- TODO: 实现MERGE语句转换\n" +
                   originalSql.replaceFirst("(?i)REPLACE\\s+INTO", "INSERT INTO");
        }

        // 基础INSERT语法直接兼容
        return originalSql;
    }

    /**
     * 处理达梦REPLACE INTO SQL的转换
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     * 达梦不支持REPLACE INTO，需要转换为MERGE语句或其他等价操作
     */
    private String processDamengReplaceIntoSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRInsert insert) {
        // 达梦数据库不支持REPLACE INTO语法
        // 根据达梦官方文档，需要使用MERGE语句或DELETE + INSERT的组合来实现相同功能

        return "-- 注意：达梦数据库不支持REPLACE INTO语法\n" +
               "-- 原MySQL语句：" + originalSql + "\n" +
               "-- 建议使用MERGE语句实现相同功能：\n" +
               "-- MERGE INTO target_table USING source_table ON condition\n" +
               "-- WHEN MATCHED THEN UPDATE SET ...\n" +
               "-- WHEN NOT MATCHED THEN INSERT ...;\n" +
               "-- 或者使用DELETE + INSERT的组合\n" +
               originalSql.replaceFirst("(?i)REPLACE\\s+INTO", "INSERT INTO");
    }

    /**
     * 生成UPDATE语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateUpdate(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRUpdate)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRUpdate update =
            (com.xylink.sqltranspiler.v2.ir.statement.IRUpdate) node;

        // 达梦数据库UPDATE语句与MySQL基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. 基础UPDATE ... SET ... WHERE语法
        // 2. 多表UPDATE语法（语法可能略有差异）
        // 3. 子查询在UPDATE中的使用
        //
        // 需要注意的差异：
        // 1. LIMIT子句在UPDATE中的支持情况
        // 2. ORDER BY子句在UPDATE中的支持情况

        // 暂时直接返回原始SQL，因为基础UPDATE语法兼容
        String originalSql = (String) update.getMetadata().get("originalSql");
        if (originalSql != null) {
            return processDamengUpdateSql(originalSql, update);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦UPDATE SQL的特殊情况
     */
    private String processDamengUpdateSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRUpdate update) {
        String upperSql = originalSql.toUpperCase();

        // 检查LIMIT子句
        if (upperSql.contains("LIMIT")) {
            // 达梦在UPDATE中支持LIMIT，但语法可能略有差异
            // 根据达梦官方文档，基本语法兼容
            return originalSql;
        }

        // 检查ORDER BY子句
        if (upperSql.contains("ORDER BY")) {
            // 达梦在UPDATE中支持ORDER BY
            return originalSql;
        }

        // 基础UPDATE语法直接兼容
        return originalSql;
    }

    /**
     * 生成DELETE语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateDelete(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRDelete)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRDelete delete =
            (com.xylink.sqltranspiler.v2.ir.statement.IRDelete) node;

        // 达梦数据库DELETE语句与MySQL基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. 基础DELETE FROM ... WHERE语法
        // 2. 多表DELETE语法（语法可能略有差异）
        // 3. 子查询在DELETE中的使用
        //
        // 需要注意的差异：
        // 1. LIMIT子句在DELETE中的支持情况
        // 2. ORDER BY子句在DELETE中的支持情况

        // 暂时直接返回原始SQL，因为基础DELETE语法兼容
        String originalSql = (String) delete.getMetadata().get("originalSql");
        if (originalSql != null) {
            return processDamengDeleteSql(originalSql, delete);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦DELETE SQL的特殊情况
     */
    private String processDamengDeleteSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRDelete delete) {
        String upperSql = originalSql.toUpperCase();

        // 检查LIMIT子句
        if (upperSql.contains("LIMIT")) {
            // 达梦在DELETE中支持LIMIT，但语法可能略有差异
            // 根据达梦官方文档，基本语法兼容
            return originalSql;
        }

        // 检查ORDER BY子句
        if (upperSql.contains("ORDER BY")) {
            // 达梦在DELETE中支持ORDER BY
            return originalSql;
        }

        // 基础DELETE语法直接兼容
        return originalSql;
    }
    
    /**
     * 生成CREATE TABLE语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateCreateTable(IRNode node) {
        if (!(node instanceof IRCreateTable)) {
            return generateUnsupportedNode(node);
        }

        IRCreateTable createTable = (IRCreateTable) node;
        StringBuilder sql = new StringBuilder();

        // 基本CREATE TABLE语句
        sql.append("CREATE TABLE ");
        sql.append(createTable.getTableName());
        sql.append(" (\n");

        // 生成列定义
        List<String> columnDefinitions = new ArrayList<>();
        for (IRColumnDefinition column : createTable.getColumns()) {
            columnDefinitions.add(generateColumnDefinition(column));
        }

        // 生成约束定义
        List<String> constraintDefinitions = new ArrayList<>();
        for (IRConstraint constraint : createTable.getConstraints()) {
            String constraintSql = generateConstraint(constraint);
            if (constraintSql != null && !constraintSql.trim().isEmpty()) {
                constraintDefinitions.add(constraintSql);
            }
        }

        // 合并列定义和约束定义
        List<String> allDefinitions = new ArrayList<>();
        allDefinitions.addAll(columnDefinitions);
        allDefinitions.addAll(constraintDefinitions);

        // 拼接定义
        sql.append("    ");
        sql.append(String.join(",\n    ", allDefinitions));
        sql.append("\n)");

        // 添加表选项（如果有）
        if (createTable.getEngine() != null) {
            // 达梦数据库不使用ENGINE，忽略或转换为注释
            sql.append("\n-- 原MySQL引擎: ").append(createTable.getEngine());
        }

        if (createTable.getCharset() != null) {
            // 达梦数据库字符集设置方式不同，转换为注释
            sql.append("\n-- 原MySQL字符集: ").append(createTable.getCharset());
        }

        if (createTable.getComment() != null) {
            // 达梦支持表注释
            sql.append("\nCOMMENT '").append(createTable.getComment()).append("'");
        }

        return sql.toString();
    }

    /**
     * 生成列定义
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateColumnDefinition(IRColumnDefinition column) {
        StringBuilder sql = new StringBuilder();

        // 列名
        sql.append(column.getColumnName());
        sql.append(" ");

        // 数据类型映射
        String damengType = dataTypeMapper.mapDataType(column.getDataType().getTypeName());
        sql.append(damengType);

        // 处理精度和标度 - 只有当映射后的类型需要精度时才添加
        if (column.getDataType().hasPrecision() && needsPrecision(damengType)) {
            sql.append("(").append(column.getDataType().getPrecision());
            if (column.getDataType().hasScale()) {
                sql.append(",").append(column.getDataType().getScale());
            }
            sql.append(")");
        }


        // NULL/NOT NULL
        if (!column.isNullable()) {
            sql.append(" NOT NULL");
        }

        // 默认值
        if (column.getDefaultValue() != null && !column.getDefaultValue().trim().isEmpty()) {
            sql.append(" DEFAULT ").append(column.getDefaultValue());
        }

        // AUTO_INCREMENT转换为IDENTITY
        if (column.isAutoIncrement()) {
            // 达梦数据库使用IDENTITY代替AUTO_INCREMENT
            sql.append(" IDENTITY(1,1)");
        }

        // 列注释
        if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
            sql.append(" COMMENT '").append(column.getComment()).append("'");
        }

        return sql.toString();
    }

    /**
     * 判断数据类型是否需要精度参数
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
     */
    private boolean needsPrecision(String dataType) {
        String upperType = dataType.toUpperCase();
        // 检查是否包含注释（如ENUM和SET类型的映射结果）
        if (upperType.contains("--")) {
            upperType = upperType.split("--")[0].trim();
        }

        return upperType.equals("DECIMAL") ||
               upperType.equals("NUMERIC") ||
               upperType.equals("CHAR") ||
               upperType.equals("VARCHAR") ||
               upperType.equals("BINARY") ||
               upperType.equals("VARBINARY") ||
               upperType.equals("FLOAT") ||
               upperType.equals("DOUBLE");
    }

    /**
     * 生成约束定义
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateConstraint(IRConstraint constraint) {
        switch (constraint.getConstraintType()) {
            case PRIMARY_KEY:
                return generatePrimaryKeyConstraint((IRPrimaryKeyConstraint) constraint);
            case FOREIGN_KEY:
                return generateForeignKeyConstraint((IRForeignKeyConstraint) constraint);
            case UNIQUE:
                return generateUniqueConstraint((IRUniqueConstraint) constraint);
            case CHECK:
                return generateCheckConstraint((IRCheckConstraint) constraint);
            case NOT_NULL:
                // NOT NULL约束在列定义中处理，这里返回空
                return "";
            default:
                return "-- 不支持的约束类型: " + constraint.getConstraintType();
        }
    }

    /**
     * 生成主键约束
     */
    private String generatePrimaryKeyConstraint(IRPrimaryKeyConstraint constraint) {
        StringBuilder sql = new StringBuilder();

        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT ").append(constraint.getConstraintName()).append(" ");
        }

        sql.append("PRIMARY KEY (");
        sql.append(String.join(", ", constraint.getColumns()));
        sql.append(")");

        return sql.toString();
    }

    /**
     * 生成外键约束
     */
    private String generateForeignKeyConstraint(IRForeignKeyConstraint constraint) {
        StringBuilder sql = new StringBuilder();

        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT ").append(constraint.getConstraintName()).append(" ");
        }

        sql.append("FOREIGN KEY (");
        sql.append(String.join(", ", constraint.getColumns()));
        sql.append(") REFERENCES ");
        sql.append(constraint.getReferencedTable());
        sql.append("(");
        sql.append(String.join(", ", constraint.getReferencedColumns()));
        sql.append(")");

        return sql.toString();
    }

    /**
     * 生成唯一约束
     */
    private String generateUniqueConstraint(IRUniqueConstraint constraint) {
        StringBuilder sql = new StringBuilder();

        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT ").append(constraint.getConstraintName()).append(" ");
        }

        sql.append("UNIQUE (");
        sql.append(String.join(", ", constraint.getColumnNames()));
        sql.append(")");

        return sql.toString();
    }

    /**
     * 生成检查约束
     */
    private String generateCheckConstraint(IRCheckConstraint constraint) {
        StringBuilder sql = new StringBuilder();

        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT ").append(constraint.getConstraintName()).append(" ");
        }

        sql.append("CHECK (");
        // 简化的表达式生成：如果是字面量表达式，直接使用其值
        if (constraint.getCheckExpression() instanceof com.xylink.sqltranspiler.v2.ir.expression.IRLiteral) {
            com.xylink.sqltranspiler.v2.ir.expression.IRLiteral literal =
                (com.xylink.sqltranspiler.v2.ir.expression.IRLiteral) constraint.getCheckExpression();
            sql.append(literal.getValue().toString());
        } else {
            // 对于复杂表达式，暂时使用占位符
            sql.append("/* 复杂检查表达式 - 需要表达式生成器支持 */");
        }
        sql.append(")");

        return sql.toString();
    }

    /**
     * 生成ALTER TABLE语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateAlterTable(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRAlterTable)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRAlterTable alterTable =
            (com.xylink.sqltranspiler.v2.ir.statement.IRAlterTable) node;

        // 达梦数据库ALTER TABLE语句与MySQL基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. ADD COLUMN
        // 2. DROP COLUMN
        // 3. MODIFY COLUMN（语法可能略有差异）
        // 4. RENAME COLUMN
        // 5. ADD CONSTRAINT
        // 6. DROP CONSTRAINT

        // 暂时直接返回原始SQL，但需要处理数据类型映射
        String originalSql = (String) alterTable.getMetadata().get("originalSql");
        if (originalSql != null) {
            return processDamengAlterTableSql(originalSql, alterTable);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦ALTER TABLE SQL的特殊情况
     */
    private String processDamengAlterTableSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRAlterTable alterTable) {
        String result = originalSql;

        // 处理数据类型映射
        result = mapDataTypesInAlterTable(result);

        return result;
    }

    /**
     * 在ALTER TABLE语句中映射数据类型
     */
    private String mapDataTypesInAlterTable(String sql) {
        // 映射MySQL数据类型到达梦数据类型
        String result = sql;

        // 基础数据类型映射
        result = result.replaceAll("(?i)\\bINT\\b", "INTEGER");
        result = result.replaceAll("(?i)\\bTINYINT\\b", "SMALLINT");
        result = result.replaceAll("(?i)\\bMEDIUMINT\\b", "INTEGER");
        result = result.replaceAll("(?i)\\bBIGINT\\b", "BIGINT");
        result = result.replaceAll("(?i)\\bTEXT\\b", "CLOB");
        result = result.replaceAll("(?i)\\bLONGTEXT\\b", "CLOB");
        result = result.replaceAll("(?i)\\bMEDIUMTEXT\\b", "CLOB");
        result = result.replaceAll("(?i)\\bTINYTEXT\\b", "VARCHAR(255)");
        result = result.replaceAll("(?i)\\bJSON\\b", "CLOB");

        return result;
    }

    /**
     * 生成DROP TABLE语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateDropTable(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRDropTable)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRDropTable dropTable =
            (com.xylink.sqltranspiler.v2.ir.statement.IRDropTable) node;

        // 达梦数据库DROP TABLE语句与MySQL基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. DROP TABLE table_name
        // 2. DROP TABLE IF EXISTS table_name

        // 暂时直接返回原始SQL，因为DROP TABLE语法兼容
        String originalSql = (String) dropTable.getMetadata().get("originalSql");
        if (originalSql != null) {
            return processDamengDropTableSql(originalSql, dropTable);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦DROP TABLE SQL的特殊情况
     */
    private String processDamengDropTableSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRDropTable dropTable) {
        // 达梦数据库DROP TABLE语法与MySQL基本兼容
        // 根据达梦官方文档，支持IF EXISTS语法
        // 因此可以直接使用原始SQL

        return originalSql;
    }

    /**
     * 生成CREATE INDEX语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateCreateIndex(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRCreateIndex)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRCreateIndex createIndex =
            (com.xylink.sqltranspiler.v2.ir.statement.IRCreateIndex) node;

        // 达梦数据库CREATE INDEX语句与MySQL基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. 基础CREATE INDEX语法
        // 2. UNIQUE INDEX
        // 3. 复合索引
        // 4. 函数索引（语法可能略有差异）
        //
        // 索引类型支持：
        // 1. BTREE索引（默认）
        // 2. HASH索引
        // 3. 位图索引

        // 暂时直接返回原始SQL，因为基础CREATE INDEX语法兼容
        String originalSql = (String) createIndex.getMetadata().get("originalSql");
        if (originalSql != null) {
            return processDamengCreateIndexSql(originalSql, createIndex);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦CREATE INDEX SQL的特殊情况
     */
    private String processDamengCreateIndexSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRCreateIndex createIndex) {
        // 达梦数据库CREATE INDEX语法与MySQL基本兼容
        // 根据达梦官方文档，基本语法完全兼容
        // 因此大部分情况下可以直接使用原始SQL

        return originalSql;
    }

    /**
     * 生成DROP INDEX语句
     * 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
     */
    private String generateDropIndex(IRNode node) {
        if (!(node instanceof com.xylink.sqltranspiler.v2.ir.statement.IRDropIndex)) {
            return generateUnsupportedNode(node);
        }

        com.xylink.sqltranspiler.v2.ir.statement.IRDropIndex dropIndex =
            (com.xylink.sqltranspiler.v2.ir.statement.IRDropIndex) node;

        // 达梦数据库DROP INDEX语句与MySQL基本兼容
        // 根据达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        // 达梦支持：
        // 1. DROP INDEX index_name ON table_name（MySQL语法）
        // 2. DROP INDEX index_name（标准SQL语法）

        // 暂时直接返回原始SQL，因为DROP INDEX语法兼容
        String originalSql = (String) dropIndex.getMetadata().get("originalSql");
        if (originalSql != null) {
            return processDamengDropIndexSql(originalSql, dropIndex);
        }

        return generateUnsupportedNode(node);
    }

    /**
     * 处理达梦DROP INDEX SQL的特殊情况
     */
    private String processDamengDropIndexSql(String originalSql, com.xylink.sqltranspiler.v2.ir.statement.IRDropIndex dropIndex) {
        // 达梦数据库DROP INDEX语法与MySQL基本兼容
        // 根据达梦官方文档，支持MySQL的DROP INDEX语法
        // 因此可以直接使用原始SQL

        return originalSql;
    }
}