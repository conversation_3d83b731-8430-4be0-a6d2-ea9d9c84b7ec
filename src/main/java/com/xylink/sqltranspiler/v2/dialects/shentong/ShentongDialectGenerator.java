package com.xylink.sqltranspiler.v2.dialects.shentong;

import java.util.ArrayList;
import java.util.List;

import com.xylink.sqltranspiler.v2.dialects.SqlDialectGenerator;
import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.statement.IRColumnDefinition;
import com.xylink.sqltranspiler.v2.ir.statement.IRCreateTable;

/**
 * 神通数据库方言生成器
 *
 * 根据神通官方文档实现：
 * - 神通官方文档：参考项目内@shentong.md文档
 *
 * 负责将IR节点转换为神通数据库兼容的SQL语句
 */
public class ShentongDialectGenerator implements SqlDialectGenerator {

    private final ShentongDataTypeMapper dataTypeMapper;

    /**
     * 构造函数
     */
    public ShentongDialectGenerator() {
        this.dataTypeMapper = new ShentongDataTypeMapper();
    }

    @Override
    public String generate(IRNode node) {
        if (node == null) {
            return "";
        }

        // 根据官方文档实现节点类型分发逻辑
        // MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
        // 神通官方文档：参考项目内docs/shentong_mapping.md文档

        try {
            switch (node.getNodeType()) {
                case SELECT:
                    return generateSelect(node);
                case INSERT:
                    return generateInsert(node);
                case UPDATE:
                    return generateUpdate(node);
                case DELETE:
                    return generateDelete(node);
                case CREATE_TABLE:
                    return generateCreateTable(node);
                case ALTER_TABLE:
                    return generateAlterTable(node);
                case DROP_TABLE:
                    return generateDropTable(node);
                case CREATE_INDEX:
                    return generateCreateIndex(node);
                case DROP_INDEX:
                    return generateDropIndex(node);
                default:
                    // 对于不支持的节点类型，返回带注释的原始SQL
                    return generateUnsupportedNode(node);
            }
        } catch (Exception e) {
            // 错误处理：返回带错误信息的注释和原始SQL
            return generateErrorComment(node, e);
        }
    }

    /**
     * 生成不支持节点的注释
     */
    private String generateUnsupportedNode(IRNode node) {
        String originalSql = getOriginalSql(node);
        return "-- TODO: 实现从 MySQL Community Server (MySQL 8.4) 到 神通数据库 (Shentong 7.0) 的转换\n" +
               originalSql;
    }

    /**
     * 生成错误注释
     */
    private String generateErrorComment(IRNode node, Exception e) {
        String originalSql = getOriginalSql(node);
        return "-- 转换错误: " + e.getMessage() + "\n" +
               "-- TODO: 实现从 MySQL Community Server (MySQL 8.4) 到 神通数据库 (Shentong 7.0) 的转换\n" +
               originalSql;
    }

    /**
     * 获取原始SQL
     */
    private String getOriginalSql(IRNode node) {
        if (node.getMetadata() != null && node.getMetadata().get("originalSql") != null) {
            return node.getMetadata().get("originalSql").toString();
        }
        return "-- 无法获取原始SQL";
    }

    /**
     * 生成SELECT语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateSelect(IRNode node) {
        // 神通数据库SELECT语句与Oracle基本兼容
        // 特殊处理：
        // 1. DUAL表：神通支持DUAL表，与Oracle兼容
        // 2. LIMIT语句：神通不支持LIMIT，需要转换为ROWNUM
        // 3. 函数：大部分Oracle函数在神通中有对应实现

        return generateUnsupportedNode(node);
    }

    /**
     * 生成INSERT语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateInsert(IRNode node) {
        // 神通数据库INSERT语句与Oracle基本兼容
        // 特殊处理：
        // 1. INSERT IGNORE -> 神通不支持，需要转换为其他方式
        // 2. ON DUPLICATE KEY UPDATE -> 神通不支持，需要转换为MERGE语句
        // 3. REPLACE INTO -> 神通不支持，需要转换为MERGE语句

        return generateUnsupportedNode(node);
    }

    /**
     * 生成UPDATE语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateUpdate(IRNode node) {
        // 神通数据库UPDATE语句与Oracle基本兼容
        // 特殊处理：
        // 1. 多表UPDATE语法可能有差异
        // 2. LIMIT子句在UPDATE中不支持

        return generateUnsupportedNode(node);
    }

    /**
     * 生成DELETE语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateDelete(IRNode node) {
        // 神通数据库DELETE语句与Oracle基本兼容
        // 特殊处理：
        // 1. 多表DELETE语法可能有差异
        // 2. LIMIT子句在DELETE中不支持

        return generateUnsupportedNode(node);
    }

    /**
     * 生成CREATE TABLE语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateCreateTable(IRNode node) {
        // 神通数据库CREATE TABLE语句高度兼容MySQL
        // 根据docs/shentong_mapping.md：
        // 1. AUTO_INCREMENT：神通完全支持，与MySQL语法完全兼容
        // 2. 数据类型：大部分直接映射，少数需要转换
        // 3. 字符集：utf8mb4 -> UTF8
        // 4. 存储引擎：移除ENGINE子句
        // 5. 注释：支持MySQL COMMENT语法

        if (!(node instanceof IRCreateTable)) {
            return generateUnsupportedNode(node);
        }

        IRCreateTable createTable = (IRCreateTable) node;

        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ");

        // 表名 - 根据文档，神通支持标识符双引号括起来
        sql.append("\"").append(createTable.getTableName()).append("\"");
        sql.append(" (\n");

        // 列定义
        List<String> columnDefinitions = new ArrayList<>();
        for (IRColumnDefinition column : createTable.getColumns()) {
            columnDefinitions.add("    " + generateColumnDefinition(column));
        }

        // 约束定义
        List<String> constraintDefinitions = new ArrayList<>();
        for (com.xylink.sqltranspiler.v2.ir.constraint.IRConstraint constraint : createTable.getConstraints()) {
            String constraintSql = generateConstraint(constraint);
            if (!constraintSql.trim().isEmpty()) {
                constraintDefinitions.add("    " + constraintSql);
            }
        }

        // 合并列定义和约束定义
        List<String> allDefinitions = new ArrayList<>();
        allDefinitions.addAll(columnDefinitions);
        allDefinitions.addAll(constraintDefinitions);

        sql.append(String.join(",\n", allDefinitions));
        sql.append("\n)");

        // 表注释 - 神通支持MySQL COMMENT语法
        if (createTable.getComment() != null && !createTable.getComment().trim().isEmpty()) {
            sql.append(" COMMENT='").append(createTable.getComment()).append("'");
        }

        return sql.toString();
    }

    /**
     * 生成列定义
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateColumnDefinition(IRColumnDefinition column) {
        StringBuilder sql = new StringBuilder();

        // 列名 - 神通支持双引号标识符
        sql.append("\"").append(column.getColumnName()).append("\"");
        sql.append(" ");

        // 数据类型映射 - 根据文档，大部分类型直接映射
        String shentongType = dataTypeMapper.mapDataType(column.getDataType().getTypeName());
        sql.append(shentongType);

        // 处理精度和标度
        if (column.getDataType().hasPrecision() && needsPrecision(shentongType)) {
            sql.append("(").append(column.getDataType().getPrecision());
            if (column.getDataType().hasScale()) {
                sql.append(",").append(column.getDataType().getScale());
            }
            sql.append(")");
        }

        // NULL/NOT NULL
        if (!column.isNullable()) {
            sql.append(" NOT NULL");
        }

        // 默认值
        if (column.getDefaultValue() != null && !column.getDefaultValue().trim().isEmpty()) {
            sql.append(" DEFAULT ").append(column.getDefaultValue());
        }

        // AUTO_INCREMENT - 根据文档，神通完全支持MySQL语法
        if (column.isAutoIncrement()) {
            sql.append(" AUTO_INCREMENT");
        }

        // 列注释 - 神通支持MySQL COMMENT语法
        if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
            sql.append(" COMMENT '").append(column.getComment()).append("'");
        }

        return sql.toString();
    }

    /**
     * 判断数据类型是否需要精度参数
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private boolean needsPrecision(String dataType) {
        String upperType = dataType.toUpperCase();
        return upperType.equals("DECIMAL") ||
               upperType.equals("NUMERIC") ||
               upperType.equals("CHAR") ||
               upperType.equals("VARCHAR") ||
               upperType.equals("FLOAT") ||
               upperType.equals("DOUBLE");
    }

    /**
     * 生成约束定义
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateConstraint(com.xylink.sqltranspiler.v2.ir.constraint.IRConstraint constraint) {
        // 根据文档，神通数据库高度兼容MySQL约束语法
        if (constraint instanceof com.xylink.sqltranspiler.v2.ir.constraint.IRPrimaryKeyConstraint) {
            return generatePrimaryKeyConstraint((com.xylink.sqltranspiler.v2.ir.constraint.IRPrimaryKeyConstraint) constraint);
        } else if (constraint instanceof com.xylink.sqltranspiler.v2.ir.constraint.IRUniqueConstraint) {
            return generateUniqueConstraint((com.xylink.sqltranspiler.v2.ir.constraint.IRUniqueConstraint) constraint);
        } else if (constraint instanceof com.xylink.sqltranspiler.v2.ir.constraint.IRForeignKeyConstraint) {
            return generateForeignKeyConstraint((com.xylink.sqltranspiler.v2.ir.constraint.IRForeignKeyConstraint) constraint);
        } else if (constraint instanceof com.xylink.sqltranspiler.v2.ir.constraint.IRCheckConstraint) {
            return generateCheckConstraint((com.xylink.sqltranspiler.v2.ir.constraint.IRCheckConstraint) constraint);
        }
        return "";
    }

    /**
     * 生成主键约束
     */
    private String generatePrimaryKeyConstraint(com.xylink.sqltranspiler.v2.ir.constraint.IRPrimaryKeyConstraint constraint) {
        StringBuilder sql = new StringBuilder();
        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT \"").append(constraint.getConstraintName()).append("\" ");
        }
        sql.append("PRIMARY KEY (");
        sql.append(String.join(", ", constraint.getColumns().stream()
            .map(col -> "\"" + col + "\"").toArray(String[]::new)));
        sql.append(")");
        return sql.toString();
    }

    /**
     * 生成唯一约束
     */
    private String generateUniqueConstraint(com.xylink.sqltranspiler.v2.ir.constraint.IRUniqueConstraint constraint) {
        StringBuilder sql = new StringBuilder();
        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT \"").append(constraint.getConstraintName()).append("\" ");
        }
        sql.append("UNIQUE (");
        sql.append(String.join(", ", constraint.getColumnNames().stream()
            .map(col -> "\"" + col + "\"").toArray(String[]::new)));
        sql.append(")");
        return sql.toString();
    }

    /**
     * 生成外键约束
     */
    private String generateForeignKeyConstraint(com.xylink.sqltranspiler.v2.ir.constraint.IRForeignKeyConstraint constraint) {
        StringBuilder sql = new StringBuilder();
        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT \"").append(constraint.getConstraintName()).append("\" ");
        }
        sql.append("FOREIGN KEY (");
        sql.append(String.join(", ", constraint.getColumns()));
        sql.append(") REFERENCES ");
        sql.append(constraint.getReferencedTable());
        sql.append("(");
        sql.append(String.join(", ", constraint.getReferencedColumns()));
        sql.append(")");
        return sql.toString();
    }

    /**
     * 生成检查约束
     */
    private String generateCheckConstraint(com.xylink.sqltranspiler.v2.ir.constraint.IRCheckConstraint constraint) {
        StringBuilder sql = new StringBuilder();
        if (constraint.getConstraintName() != null) {
            sql.append("CONSTRAINT \"").append(constraint.getConstraintName()).append("\" ");
        }
        sql.append("CHECK (");
        // 简化的表达式生成：如果是字面量表达式，直接使用其值
        if (constraint.getCheckExpression() instanceof com.xylink.sqltranspiler.v2.ir.expression.IRLiteral) {
            com.xylink.sqltranspiler.v2.ir.expression.IRLiteral literal =
                (com.xylink.sqltranspiler.v2.ir.expression.IRLiteral) constraint.getCheckExpression();
            sql.append(literal.getValue().toString());
        } else {
            // 对于复杂表达式，暂时使用占位符
            sql.append("/* 复杂检查表达式 - 需要表达式生成器支持 */");
        }
        sql.append(")");
        return sql.toString();
    }

    /**
     * 生成ALTER TABLE语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateAlterTable(IRNode node) {
        // 神通数据库ALTER TABLE语句与Oracle基本兼容
        // 特殊处理：
        // 1. 某些ALTER操作语法可能有差异
        // 2. 数据类型修改需要使用神通的数据类型

        return generateUnsupportedNode(node);
    }

    /**
     * 生成DROP TABLE语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateDropTable(IRNode node) {
        // 神通数据库DROP TABLE语句与Oracle基本兼容
        // 语法：DROP TABLE table_name

        return generateUnsupportedNode(node);
    }

    /**
     * 生成CREATE INDEX语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateCreateIndex(IRNode node) {
        // 神通数据库CREATE INDEX语句与Oracle基本兼容
        // 特殊处理：
        // 1. 索引类型可能有差异
        // 2. 函数索引语法可能不同

        return generateUnsupportedNode(node);
    }

    /**
     * 生成DROP INDEX语句
     * 根据神通官方文档：参考项目内docs/shentong_mapping.md文档
     */
    private String generateDropIndex(IRNode node) {
        // 神通数据库DROP INDEX语句与Oracle基本兼容
        // 语法：DROP INDEX index_name

        return generateUnsupportedNode(node);
    }
}