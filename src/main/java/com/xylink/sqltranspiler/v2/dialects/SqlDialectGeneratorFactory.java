package com.xylink.sqltranspiler.v2.dialects;

import com.xylink.sqltranspiler.v2.dialects.dameng.DamengDialectGenerator;
import com.xylink.sqltranspiler.v2.dialects.kingbase.KingbaseDialectGenerator;
import com.xylink.sqltranspiler.v2.dialects.shentong.ShentongDialectGenerator;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * SQL方言生成器工厂
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 * 
 * 负责创建和管理各种数据库方言的生成器实例。
 */
public class SqlDialectGeneratorFactory {
    
    private static final Map<SqlDialectType, Supplier<SqlDialectGenerator>> GENERATOR_SUPPLIERS = new HashMap<>();
    
    static {
        // 注册各种数据库方言生成器
        GENERATOR_SUPPLIERS.put(SqlDialectType.DAMENG, DamengDialectGenerator::new);
        GENERATOR_SUPPLIERS.put(SqlDialectType.KINGBASE, KingbaseDialectGenerator::new);
        GENERATOR_SUPPLIERS.put(SqlDialectType.SHENTONG, ShentongDialectGenerator::new);
    }
    
    /**
     * 创建方言生成器
     * 
     * @param dialectType 方言类型
     * @return 方言生成器实例
     * @throws IllegalArgumentException 如果不支持指定的方言类型
     */
    public static SqlDialectGenerator createGenerator(SqlDialectType dialectType) {
        if (dialectType == null) {
            throw new IllegalArgumentException("方言类型不能为空");
        }
        
        Supplier<SqlDialectGenerator> supplier = GENERATOR_SUPPLIERS.get(dialectType);
        if (supplier == null) {
            throw new IllegalArgumentException("不支持的方言类型: " + dialectType);
        }
        
        return supplier.get();
    }
    
    /**
     * 检查是否支持指定的方言类型
     * 
     * @param dialectType 方言类型
     * @return 是否支持
     */
    public static boolean isSupported(SqlDialectType dialectType) {
        return dialectType != null && GENERATOR_SUPPLIERS.containsKey(dialectType);
    }
    
    /**
     * 获取所有支持的方言类型
     * 
     * @return 支持的方言类型数组
     */
    public static SqlDialectType[] getSupportedDialects() {
        return GENERATOR_SUPPLIERS.keySet().toArray(new SqlDialectType[0]);
    }
    
    /**
     * 注册新的方言生成器
     * 
     * @param dialectType 方言类型
     * @param generatorSupplier 生成器供应商
     */
    public static void registerGenerator(SqlDialectType dialectType, Supplier<SqlDialectGenerator> generatorSupplier) {
        if (dialectType == null) {
            throw new IllegalArgumentException("方言类型不能为空");
        }
        if (generatorSupplier == null) {
            throw new IllegalArgumentException("生成器供应商不能为空");
        }
        
        GENERATOR_SUPPLIERS.put(dialectType, generatorSupplier);
    }
    
    /**
     * 移除方言生成器注册
     * 
     * @param dialectType 方言类型
     * @return 是否成功移除
     */
    public static boolean unregisterGenerator(SqlDialectType dialectType) {
        if (dialectType == null) {
            return false;
        }
        
        return GENERATOR_SUPPLIERS.remove(dialectType) != null;
    }
}
