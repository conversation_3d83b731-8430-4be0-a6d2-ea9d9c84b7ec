package com.xylink.sqltranspiler.v2.diagnostics;

/**
 * 诊断问题类
 * 
 * 根据官方文档实现
 */
public class DiagnosticIssue {
    
    private final String message;
    private final String severity;
    private final int line;
    private final int column;
    
    /**
     * 构造函数
     */
    public DiagnosticIssue(String message, String severity, int line, int column) {
        this.message = message;
        this.severity = severity;
        this.line = line;
        this.column = column;
    }
    
    // Getters
    public String getMessage() { return message; }
    public String getSeverity() { return severity; }
    public int getLine() { return line; }
    public int getColumn() { return column; }
}