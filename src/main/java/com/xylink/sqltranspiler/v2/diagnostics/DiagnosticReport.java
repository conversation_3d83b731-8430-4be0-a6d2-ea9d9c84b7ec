package com.xylink.sqltranspiler.v2.diagnostics;

import java.util.List;

/**
 * 诊断报告类
 * 
 * 根据官方文档实现
 */
public class DiagnosticReport {
    
    private final List<DiagnosticIssue> issues;
    
    /**
     * 构造函数
     */
    public DiagnosticReport(List<DiagnosticIssue> issues) {
        this.issues = List.copyOf(issues);
    }
    
    /**
     * 获取所有诊断问题
     */
    public List<DiagnosticIssue> getIssues() {
        return issues;
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return issues.stream().anyMatch(issue -> "ERROR".equals(issue.getSeverity()));
    }
}