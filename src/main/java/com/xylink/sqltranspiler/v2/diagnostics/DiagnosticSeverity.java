package com.xylink.sqltranspiler.v2.diagnostics;

/**
 * 诊断严重程度枚举
 * 
 * 定义诊断问题的严重程度级别。
 * 
 * 验证日期: 2024-01-15
 */
public enum DiagnosticSeverity {
    
    /**
     * 错误：阻止转换继续进行的严重问题
     */
    ERROR("错误", 3),
    
    /**
     * 警告：可能导致问题但不阻止转换的问题
     */
    WARNING("警告", 2),
    
    /**
     * 信息：提供额外信息的消息
     */
    INFO("信息", 1);
    
    private final String displayName;
    private final int level;
    
    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param level 严重程度级别（数值越大越严重）
     */
    DiagnosticSeverity(String displayName, int level) {
        this.displayName = displayName;
        this.level = level;
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取严重程度级别
     * 
     * @return 级别（数值越大越严重）
     */
    public int getLevel() {
        return level;
    }
    
    /**
     * 判断是否比另一个严重程度更严重
     * 
     * @param other 另一个严重程度
     * @return 是否更严重
     */
    public boolean isMoreSevereThan(DiagnosticSeverity other) {
        return this.level > other.level;
    }
    
    /**
     * 判断是否比另一个严重程度更轻微
     * 
     * @param other 另一个严重程度
     * @return 是否更轻微
     */
    public boolean isLessSevereThan(DiagnosticSeverity other) {
        return this.level < other.level;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
