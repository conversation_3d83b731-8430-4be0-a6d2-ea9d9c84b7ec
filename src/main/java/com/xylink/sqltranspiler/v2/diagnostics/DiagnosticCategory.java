package com.xylink.sqltranspiler.v2.diagnostics;

/**
 * 诊断问题分类枚举
 * 
 * 将诊断问题按照类型进行分类，便于用户理解和处理。
 * 
 * 验证日期: 2024-01-15
 */
public enum DiagnosticCategory {
    
    /**
     * 一般问题
     */
    GENERAL("一般问题", "通用的转换问题"),
    
    /**
     * 语法问题
     */
    SYNTAX("语法问题", "SQL语法相关的问题"),
    
    /**
     * 语义问题
     */
    SEMANTIC("语义问题", "SQL语义相关的问题"),
    
    /**
     * 兼容性问题
     */
    COMPATIBILITY("兼容性问题", "数据库方言兼容性问题"),
    
    /**
     * 类型映射问题
     */
    TYPE_MAPPING("类型映射", "数据类型映射相关的问题"),
    
    /**
     * 性能问题
     */
    PERFORMANCE("性能问题", "可能影响性能的问题"),
    
    /**
     * 功能限制
     */
    FEATURE_LIMITATION("功能限制", "目标数据库功能限制"),
    
    /**
     * 配置问题
     */
    CONFIGURATION("配置问题", "转换配置相关的问题"),
    
    /**
     * 验证问题
     */
    VALIDATION("验证问题", "数据验证相关的问题"),
    
    /**
     * 优化建议
     */
    OPTIMIZATION("优化建议", "SQL优化相关的建议");
    
    private final String displayName;
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param description 描述
     */
    DiagnosticCategory(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取描述
     * 
     * @return 描述
     */
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
