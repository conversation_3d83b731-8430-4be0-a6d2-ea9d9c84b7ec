package com.xylink.sqltranspiler.v2.diagnostics;

import java.util.List;
import java.util.ArrayList;

/**
 * 诊断收集器类
 * 
 * 根据官方文档实现
 */
public class DiagnosticCollector {
    
    private final List<DiagnosticIssue> issues = new ArrayList<>();
    
    /**
     * 添加诊断问题
     */
    public void addIssue(DiagnosticIssue issue) {
        issues.add(issue);
    }
    
    /**
     * 获取所有诊断问题
     */
    public List<DiagnosticIssue> getIssues() {
        return List.copyOf(issues);
    }
    
    /**
     * 清空诊断问题
     */
    public void clear() {
        issues.clear();
    }
}