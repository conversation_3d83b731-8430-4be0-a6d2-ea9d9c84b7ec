package com.xylink.sqltranspiler.v2.plugin;

import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认插件上下文实现
 * 
 * 根据官方文档实现：
 * - 提供插件运行时的上下文环境
 */
public class DefaultPluginContext implements PluginContext {
    
    private final Map<String, Object> attributes;
    private final Map<String, Object> sharedData;
    private final PluginInfo pluginInfo;
    
    /**
     * 构造函数
     */
    public DefaultPluginContext(PluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
        this.attributes = new ConcurrentHashMap<>();
        this.sharedData = new ConcurrentHashMap<>();
    }
    
    @Override
    public PluginInfo getPluginInfo() {
        return pluginInfo;
    }
    
    @Override
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    @Override
    public Object getAttribute(String key) {
        return attributes.get(key);
    }
    
    @Override
    public void removeAttribute(String key) {
        attributes.remove(key);
    }
    
    @Override
    public Map<String, Object> getAllAttributes() {
        return new HashMap<>(attributes);
    }
    
    @Override
    public void setSharedData(String key, Object value) {
        sharedData.put(key, value);
    }
    
    @Override
    public Object getSharedData(String key) {
        return sharedData.get(key);
    }
    
    @Override
    public void removeSharedData(String key) {
        sharedData.remove(key);
    }
    
    @Override
    public Map<String, Object> getAllSharedData() {
        return new HashMap<>(sharedData);
    }
    
    @Override
    public void log(String level, String message) {
        // TODO: 实现日志记录
        System.out.printf("[%s] %s: %s%n", level, pluginInfo.getName(), message);
    }
    
    @Override
    public void logInfo(String message) {
        log("INFO", message);
    }
    
    @Override
    public void logWarning(String message) {
        log("WARN", message);
    }
    
    @Override
    public void logError(String message) {
        log("ERROR", message);
    }
}