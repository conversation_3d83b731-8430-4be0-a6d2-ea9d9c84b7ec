package com.xylink.sqltranspiler.v2.plugin.dialect;

import com.xylink.sqltranspiler.v2.plugin.PluginContext;
import com.xylink.sqltranspiler.v2.plugin.PluginInfo;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;

/**
 * 金仓数据库方言插件
 * 
 * 根据金仓官方文档实现：
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * 
 * 提供金仓数据库的SQL方言支持
 * 金仓数据库在MySQL兼容模式下支持大部分MySQL语法
 */
public class KingbaseDialectPlugin implements DialectPlugin {
    
    private PluginInfo pluginInfo;
    private PluginContext context;
    private SqlDialect dialect;
    
    @Override
    public PluginInfo getInfo() {
        return pluginInfo;
    }
    
    @Override
    public void initialize(PluginContext context) {
        this.context = context;
        // TODO: 创建金仓方言实例
        // this.dialect = new KingbaseDialect();
        
        context.logInfo("金仓数据库方言插件初始化完成");
    }
    
    @Override
    public void shutdown() {
        if (context != null) {
            context.logInfo("金仓数据库方言插件关闭");
        }
        this.dialect = null;
        this.context = null;
    }
    
    @Override
    public SqlDialect getDialect() {
        return dialect;
    }
    
    @Override
    public String getDialectName() {
        return "KingBase";
    }
    
    @Override
    public String getDialectVersion() {
        return "V8";
    }
    
    @Override
    public boolean supportsVersion(String version) {
        // TODO: 实现版本兼容性检查
        // 金仓数据库V8及以上版本
        return version != null && (version.startsWith("V8") || version.startsWith("8."));
    }
}