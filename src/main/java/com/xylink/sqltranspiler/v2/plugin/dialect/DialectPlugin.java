package com.xylink.sqltranspiler.v2.plugin.dialect;

import com.xylink.sqltranspiler.v2.plugin.PluginManager;
import com.xylink.sqltranspiler.v2.plugin.PluginContext;
import com.xylink.sqltranspiler.v2.plugin.PluginInfo;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;

/**
 * 方言插件接口
 * 
 * 根据官方文档实现：
 * - 用于扩展SQL方言支持
 */
public interface DialectPlugin extends PluginManager.Plugin {
    
    /**
     * 获取支持的方言
     */
    SqlDialect getDialect();
    
    /**
     * 获取方言名称
     */
    String getDialectName();
    
    /**
     * 获取方言版本
     */
    String getDialectVersion();
    
    /**
     * 检查是否支持指定的数据库版本
     */
    boolean supportsVersion(String version);
}