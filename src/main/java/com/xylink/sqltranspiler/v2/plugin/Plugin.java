package com.xylink.sqltranspiler.v2.plugin;

/**
 * 插件接口
 * 
 * 定义SQL转换器插件的标准接口，支持可扩展的插件架构。
 * 
 * 官方文档依据：
 * - Apache Maven官方文档: https://maven.apache.org/guides/plugin/guide-java-plugin-development.html
 *   Maven插件开发指南，提供了插件架构的标准设计
 * - Eclipse Platform官方文档: https://help.eclipse.org/latest/index.jsp?topic=%2Forg.eclipse.platform.doc.isv%2Fguide%2Fruntime_overview.htm
 *   Eclipse插件系统的设计理念
 * 
 * 验证日期: 2024-01-15
 * 
 * 设计原则：
 * 1. 插件生命周期管理
 * 2. 依赖注入和服务发现
 * 3. 版本兼容性检查
 * 4. 安全的插件隔离
 */
public interface Plugin {
    
    /**
     * 获取插件信息
     * 
     * @return 插件信息
     */
    PluginInfo getPluginInfo();
    
    /**
     * 初始化插件
     * 
     * 在插件加载时调用，用于初始化插件的资源和配置。
     * 
     * @param context 插件上下文
     * @throws PluginException 插件初始化异常
     */
    void initialize(PluginContext context) throws PluginException;
    
    /**
     * 启动插件
     * 
     * 在插件初始化完成后调用，开始提供服务。
     * 
     * @throws PluginException 插件启动异常
     */
    void start() throws PluginException;
    
    /**
     * 停止插件
     * 
     * 在插件卸载前调用，清理资源。
     * 
     * @throws PluginException 插件停止异常
     */
    void stop() throws PluginException;
    
    /**
     * 销毁插件
     * 
     * 在插件卸载时调用，释放所有资源。
     * 
     * @throws PluginException 插件销毁异常
     */
    void destroy() throws PluginException;
    
    /**
     * 获取插件状态
     * 
     * @return 插件状态
     */
    PluginState getState();
    
    /**
     * 检查插件是否兼容指定版本
     * 
     * @param coreVersion 核心系统版本
     * @return 是否兼容
     */
    boolean isCompatible(String coreVersion);
    
    /**
     * 获取插件依赖
     * 
     * @return 插件依赖列表
     */
    PluginDependency[] getDependencies();
    
    /**
     * 插件状态枚举
     */
    enum PluginState {
        /**
         * 未初始化
         */
        UNINITIALIZED("未初始化"),
        
        /**
         * 已初始化
         */
        INITIALIZED("已初始化"),
        
        /**
         * 已启动
         */
        STARTED("已启动"),
        
        /**
         * 已停止
         */
        STOPPED("已停止"),
        
        /**
         * 已销毁
         */
        DESTROYED("已销毁"),
        
        /**
         * 错误状态
         */
        ERROR("错误状态");
        
        private final String displayName;
        
        PluginState(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
}
