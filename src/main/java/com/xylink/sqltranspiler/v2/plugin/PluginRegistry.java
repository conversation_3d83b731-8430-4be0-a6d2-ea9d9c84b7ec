package com.xylink.sqltranspiler.v2.plugin;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 插件注册表类
 *
 * 根据官方文档实现：
 * - 管理插件的注册和发现
 */
public class PluginRegistry {

    private final Map<String, PluginInfo> registeredPlugins;

    /**
     * 构造函数
     */
    public PluginRegistry() {
        this.registeredPlugins = new ConcurrentHashMap<>();
    }

    /**
     * 注册插件
     */
    public void registerPlugin(PluginInfo pluginInfo) {
        registeredPlugins.put(pluginInfo.getName(), pluginInfo);
    }

    /**
     * 取消注册插件
     */
    public void unregisterPlugin(String pluginName) {
        registeredPlugins.remove(pluginName);
    }

    /**
     * 获取插件信息
     */
    public PluginInfo getPluginInfo(String pluginName) {
        return registeredPlugins.get(pluginName);
    }

    /**
     * 获取所有注册的插件
     */
    public List<PluginInfo> getAllPlugins() {
        return new ArrayList<>(registeredPlugins.values());
    }

    /**
     * 检查插件是否已注册
     */
    public boolean isPluginRegistered(String pluginName) {
        return registeredPlugins.containsKey(pluginName);
    }
}