package com.xylink.sqltranspiler.v2.plugin;

/**
 * 插件依赖类
 * 
 * 根据官方文档实现：
 * - 用于描述插件的依赖关系
 */
public class PluginDependency {
    
    private final String name;
    private final String version;
    private final boolean optional;
    
    /**
     * 构造函数
     */
    public PluginDependency(String name, String version, boolean optional) {
        this.name = name;
        this.version = version;
        this.optional = optional;
    }
    
    /**
     * 构造函数（必需依赖）
     */
    public PluginDependency(String name, String version) {
        this(name, version, false);
    }
    
    // Getters
    public String getName() { return name; }
    public String getVersion() { return version; }
    public boolean isOptional() { return optional; }
    
    /**
     * 检查版本兼容性
     */
    public boolean isVersionCompatible(String actualVersion) {
        // TODO: 实现版本兼容性检查
        // 简单的字符串比较，实际应该支持语义版本
        return version.equals(actualVersion);
    }
    
    @Override
    public String toString() {
        return String.format("PluginDependency{name='%s', version='%s', optional=%s}", 
                           name, version, optional);
    }
}