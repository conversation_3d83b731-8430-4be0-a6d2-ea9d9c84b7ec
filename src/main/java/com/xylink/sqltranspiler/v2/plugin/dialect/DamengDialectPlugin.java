package com.xylink.sqltranspiler.v2.plugin.dialect;

import com.xylink.sqltranspiler.v2.plugin.PluginContext;
import com.xylink.sqltranspiler.v2.plugin.PluginInfo;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;
import com.xylink.sqltranspiler.v2.dialects.DamengDialect;

/**
 * 达梦数据库方言插件
 * 
 * 根据达梦官方文档实现：
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 提供达梦数据库的SQL方言支持
 */
public class DamengDialectPlugin implements DialectPlugin {
    
    private PluginInfo pluginInfo;
    private PluginContext context;
    private DamengDialect dialect;
    
    @Override
    public PluginInfo getInfo() {
        return pluginInfo;
    }
    
    @Override
    public void initialize(PluginContext context) {
        this.context = context;
        this.dialect = new DamengDialect();
        
        context.logInfo("达梦数据库方言插件初始化完成");
    }
    
    @Override
    public void shutdown() {
        if (context != null) {
            context.logInfo("达梦数据库方言插件关闭");
        }
        this.dialect = null;
        this.context = null;
    }
    
    @Override
    public SqlDialect getDialect() {
        return dialect;
    }
    
    @Override
    public String getDialectName() {
        return "DaMeng";
    }
    
    @Override
    public String getDialectVersion() {
        return "8.0";
    }
    
    @Override
    public boolean supportsVersion(String version) {
        // TODO: 实现版本兼容性检查
        // 达梦数据库8.0及以上版本
        return version != null && version.startsWith("8.");
    }
}