package com.xylink.sqltranspiler.v2.plugin;

import java.util.Map;

/**
 * 插件上下文接口
 * 
 * 根据官方文档实现：
 * - 提供插件运行时的上下文环境
 */
public interface PluginContext {
    
    /**
     * 获取插件信息
     */
    PluginInfo getPluginInfo();
    
    /**
     * 设置属性
     */
    void setAttribute(String key, Object value);
    
    /**
     * 获取属性
     */
    Object getAttribute(String key);
    
    /**
     * 移除属性
     */
    void removeAttribute(String key);
    
    /**
     * 获取所有属性
     */
    Map<String, Object> getAllAttributes();
    
    /**
     * 设置共享数据
     */
    void setSharedData(String key, Object value);
    
    /**
     * 获取共享数据
     */
    Object getSharedData(String key);
    
    /**
     * 移除共享数据
     */
    void removeSharedData(String key);
    
    /**
     * 获取所有共享数据
     */
    Map<String, Object> getAllSharedData();
    
    /**
     * 记录日志
     */
    void log(String level, String message);
    
    /**
     * 记录信息日志
     */
    void logInfo(String message);
    
    /**
     * 记录警告日志
     */
    void logWarning(String message);
    
    /**
     * 记录错误日志
     */
    void logError(String message);
}