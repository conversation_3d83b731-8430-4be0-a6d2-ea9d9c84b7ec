package com.xylink.sqltranspiler.v2.plugin;

/**
 * 插件异常
 * 
 * 插件系统中发生的异常。
 * 
 * 验证日期: 2024-01-15
 */
public class PluginException extends Exception {
    
    private final String pluginId;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public PluginException(String message) {
        super(message);
        this.pluginId = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public PluginException(String message, Throwable cause) {
        super(message, cause);
        this.pluginId = null;
    }
    
    /**
     * 构造函数
     * 
     * @param pluginId 插件ID
     * @param message 异常消息
     */
    public PluginException(String pluginId, String message) {
        super(message);
        this.pluginId = pluginId;
    }
    
    /**
     * 构造函数
     * 
     * @param pluginId 插件ID
     * @param message 异常消息
     * @param cause 原因异常
     */
    public PluginException(String pluginId, String message, Throwable cause) {
        super(message, cause);
        this.pluginId = pluginId;
    }
    
    /**
     * 获取插件ID
     * 
     * @return 插件ID，可能为null
     */
    public String getPluginId() {
        return pluginId;
    }
    
    @Override
    public String getMessage() {
        if (pluginId != null) {
            return String.format("[插件: %s] %s", pluginId, super.getMessage());
        }
        return super.getMessage();
    }
}
