package com.xylink.sqltranspiler.v2.plugin;

import java.util.List;
import java.util.Map;

/**
 * 插件信息类
 * 
 * 根据官方文档实现：
 * - 用于描述插件的基本信息
 */
public class PluginInfo {
    
    private final String name;
    private final String version;
    private final String description;
    private final String author;
    private final String mainClass;
    private final List<PluginDependency> dependencies;
    private final Map<String, String> properties;
    
    /**
     * 构造函数
     */
    public PluginInfo(String name, String version, String description, String author,
                     String mainClass, List<PluginDependency> dependencies, 
                     Map<String, String> properties) {
        this.name = name;
        this.version = version;
        this.description = description;
        this.author = author;
        this.mainClass = mainClass;
        this.dependencies = dependencies != null ? List.copyOf(dependencies) : List.of();
        this.properties = properties != null ? Map.copyOf(properties) : Map.of();
    }
    
    // Getters
    public String getName() { return name; }
    public String getVersion() { return version; }
    public String getDescription() { return description; }
    public String getAuthor() { return author; }
    public String getMainClass() { return mainClass; }
    public List<PluginDependency> getDependencies() { return dependencies; }
    public Map<String, String> getProperties() { return properties; }
    
    @Override
    public String toString() {
        return String.format("PluginInfo{name='%s', version='%s', author='%s'}", 
                           name, version, author);
    }
}