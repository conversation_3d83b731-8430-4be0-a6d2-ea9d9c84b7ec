package com.xylink.sqltranspiler.v2.parser;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.xylink.sqltranspiler.v2.ir.IRMetadata;
import com.xylink.sqltranspiler.v2.ir.IRNode;
import com.xylink.sqltranspiler.v2.ir.SourceLocation;
import com.xylink.sqltranspiler.v2.ir.clause.IRLimitClause;
import com.xylink.sqltranspiler.v2.ir.constraint.IRConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRPrimaryKeyConstraint;
import com.xylink.sqltranspiler.v2.ir.constraint.IRUniqueConstraint;
import com.xylink.sqltranspiler.v2.ir.statement.IRColumnDefinition;
import com.xylink.sqltranspiler.v2.ir.statement.IRCreateTable;
import com.xylink.sqltranspiler.v2.ir.statement.IRDelete;
import com.xylink.sqltranspiler.v2.ir.statement.IRInsert;
import com.xylink.sqltranspiler.v2.ir.statement.IRSelect;
import com.xylink.sqltranspiler.v2.ir.statement.IRUpdate;
import com.xylink.sqltranspiler.v2.ir.type.IRDataType;

/**
 * SQL解析器
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * 
 * 这是一个简化的解析器实现，用于将MySQL SQL语句解析成IR节点。
 * 在生产环境中，应该使用完整的ANTLR4解析器。
 */
public class SqlParser {
    
    /**
     * 解析SQL语句
     * 
     * @param sql SQL语句
     * @return IR节点
     */
    public IRNode parse(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return null;
        }
        
        String trimmedSql = sql.trim();
        String upperSql = trimmedSql.toUpperCase();
        
        // 根据SQL类型分发到相应的解析方法
        if (upperSql.startsWith("CREATE TABLE")) {
            return parseCreateTable(trimmedSql);
        } else if (upperSql.startsWith("SELECT")) {
            return parseSelect(trimmedSql);
        } else if (upperSql.startsWith("REPLACE INTO")) {
            return parseReplaceInto(trimmedSql);
        } else if (upperSql.startsWith("INSERT")) {
            return parseInsert(trimmedSql);
        } else if (upperSql.startsWith("UPDATE")) {
            return parseUpdate(trimmedSql);
        } else if (upperSql.startsWith("DELETE")) {
            return parseDelete(trimmedSql);
        } else if (upperSql.startsWith("CREATE UNIQUE INDEX") || upperSql.startsWith("CREATE INDEX")) {
            return parseCreateIndex(trimmedSql);
        } else if (upperSql.startsWith("DROP INDEX")) {
            return parseDropIndex(trimmedSql);
        } else if (upperSql.startsWith("DROP TABLE")) {
            return parseDropTable(trimmedSql);
        } else if (upperSql.startsWith("ALTER TABLE")) {
            return parseAlterTable(trimmedSql);
        }
        
        // 对于不支持的SQL类型，创建一个通用的IR节点
        return createUnsupportedNode(trimmedSql);
    }
    
    /**
     * 解析CREATE TABLE语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html
     */
    private IRNode parseCreateTable(String sql) {
        try {
            // 提取表名
            Pattern tableNamePattern = Pattern.compile("CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?([`\\w]+)", Pattern.CASE_INSENSITIVE);
            Matcher tableNameMatcher = tableNamePattern.matcher(sql);
            
            if (!tableNameMatcher.find()) {
                return createUnsupportedNode(sql);
            }
            
            String tableName = tableNameMatcher.group(1).replaceAll("`", "");
            
            // 提取列定义部分
            Pattern columnsPattern = Pattern.compile("\\((.+)\\)(?:\\s*ENGINE|\\s*DEFAULT|\\s*AUTO_INCREMENT|\\s*COMMENT|\\s*$)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher columnsMatcher = columnsPattern.matcher(sql);
            
            if (!columnsMatcher.find()) {
                return createUnsupportedNode(sql);
            }
            
            String columnsSection = columnsMatcher.group(1);
            
            // 解析列定义和约束
            List<IRColumnDefinition> columns = new ArrayList<>();
            List<IRConstraint> constraints = new ArrayList<>();
            
            parseColumnsAndConstraints(columnsSection, columns, constraints);
            
            // 创建IRCreateTable节点
            IRCreateTable createTable = new IRCreateTable(tableName, columns, constraints, null, null, null);

            // 添加原始SQL到元数据
            createTable.getMetadata().set("originalSql", sql);
            
            return createTable;
            
        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 解析列定义和约束
     */
    private void parseColumnsAndConstraints(String columnsSection, List<IRColumnDefinition> columns, List<IRConstraint> constraints) {
        // 简化的解析逻辑，分割列定义
        String[] parts = columnsSection.split(",");
        
        for (String part : parts) {
            String trimmedPart = part.trim();
            
            if (trimmedPart.toUpperCase().contains("PRIMARY KEY")) {
                // 解析主键约束
                if (trimmedPart.toUpperCase().startsWith("PRIMARY KEY")) {
                    // 表级主键约束
                    Pattern pkPattern = Pattern.compile("PRIMARY\\s+KEY\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
                    Matcher pkMatcher = pkPattern.matcher(trimmedPart);
                    if (pkMatcher.find()) {
                        String[] pkColumns = pkMatcher.group(1).split(",");
                        List<String> columnNames = new ArrayList<>();
                        for (String col : pkColumns) {
                            columnNames.add(col.trim().replaceAll("`", ""));
                        }
                        constraints.add(new IRPrimaryKeyConstraint(null, columnNames));
                    }
                } else {
                    // 列级主键约束，在解析列时处理
                    parseColumnDefinition(trimmedPart, columns, constraints);
                }
            } else if (trimmedPart.toUpperCase().contains("FOREIGN KEY")) {
                // 解析外键约束
                parseForeignKeyConstraint(trimmedPart, constraints);
            } else if (trimmedPart.toUpperCase().contains("UNIQUE")) {
                // 解析唯一约束
                parseUniqueConstraint(trimmedPart, constraints);
            } else if (trimmedPart.toUpperCase().contains("CHECK")) {
                // 解析检查约束
                parseCheckConstraint(trimmedPart, constraints);
            } else {
                // 解析列定义
                parseColumnDefinition(trimmedPart, columns, constraints);
            }
        }
    }
    
    /**
     * 解析列定义
     */
    private void parseColumnDefinition(String columnDef, List<IRColumnDefinition> columns, List<IRConstraint> constraints) {
        // 简化的列定义解析
        String[] tokens = columnDef.trim().split("\\s+");
        
        if (tokens.length < 2) {
            return; // 无效的列定义
        }
        
        String columnName = tokens[0].replaceAll("`", "");
        String dataTypePart = tokens[1];
        
        // 解析数据类型
        IRDataType dataType = parseDataType(dataTypePart);
        
        // 解析列属性
        boolean nullable = true;
        boolean autoIncrement = false;
        String defaultValue = null;
        String comment = null;
        
        String upperColumnDef = columnDef.toUpperCase();
        
        if (upperColumnDef.contains("NOT NULL")) {
            nullable = false;
        }
        
        if (upperColumnDef.contains("AUTO_INCREMENT")) {
            autoIncrement = true;
        }
        
        // 解析默认值
        // 支持带引号的字符串值和不带引号的值
        Pattern defaultPattern = Pattern.compile("DEFAULT\\s+('([^']*)'|\"([^\"]*)\"|([^\\s,)]+))", Pattern.CASE_INSENSITIVE);
        Matcher defaultMatcher = defaultPattern.matcher(columnDef);
        if (defaultMatcher.find()) {
            // 获取完整的默认值，包括引号
            defaultValue = defaultMatcher.group(1);
        }
        
        // 解析注释
        Pattern commentPattern = Pattern.compile("COMMENT\\s+'([^']*)'", Pattern.CASE_INSENSITIVE);
        Matcher commentMatcher = commentPattern.matcher(columnDef);
        if (commentMatcher.find()) {
            comment = commentMatcher.group(1);
        }
        
        // 创建列定义
        IRColumnDefinition column = new IRColumnDefinition(columnName, dataType, nullable, defaultValue, autoIncrement, comment);
        columns.add(column);
        
        // 处理列级约束
        if (upperColumnDef.contains("PRIMARY KEY")) {
            List<String> pkColumns = new ArrayList<>();
            pkColumns.add(columnName);
            constraints.add(new IRPrimaryKeyConstraint(null, pkColumns));
        }
        
        if (upperColumnDef.contains("UNIQUE")) {
            List<String> uniqueColumns = new ArrayList<>();
            uniqueColumns.add(columnName);
            constraints.add(new IRUniqueConstraint(null, uniqueColumns));
        }
    }
    
    /**
     * 解析数据类型
     */
    private IRDataType parseDataType(String dataTypePart) {
        // 简化的数据类型解析
        String upperType = dataTypePart.toUpperCase();
        
        // 提取类型名称和参数
        Pattern typePattern = Pattern.compile("([A-Z]+)(?:\\(([^)]+)\\))?");
        Matcher typeMatcher = typePattern.matcher(upperType);
        
        if (typeMatcher.find()) {
            String typeName = typeMatcher.group(1);
            String params = typeMatcher.group(2);
            
            if (params != null) {
                String[] paramArray = params.split(",");
                if (paramArray.length == 1) {
                    // 单参数类型，如VARCHAR(100)
                    try {
                        int precision = Integer.parseInt(paramArray[0].trim());
                        return new IRDataType(typeName, precision, null, null, null);
                    } catch (NumberFormatException e) {
                        // 非数字参数，直接使用类型名
                        return new IRDataType(typeName);
                    }
                } else if (paramArray.length == 2) {
                    // 双参数类型，如DECIMAL(10,2)
                    try {
                        int precision = Integer.parseInt(paramArray[0].trim());
                        int scale = Integer.parseInt(paramArray[1].trim());
                        return new IRDataType(typeName, precision, scale, null, null);
                    } catch (NumberFormatException e) {
                        return new IRDataType(typeName);
                    }
                }
            }

            return new IRDataType(typeName);
        }

        return new IRDataType("VARCHAR"); // 默认类型
    }
    
    /**
     * 解析外键约束
     */
    private void parseForeignKeyConstraint(String constraintDef, List<IRConstraint> constraints) {
        // TODO: 实现外键约束解析
        // 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table-foreign-keys.html
    }
    
    /**
     * 解析唯一约束
     */
    private void parseUniqueConstraint(String constraintDef, List<IRConstraint> constraints) {
        // 简化的唯一约束解析
        // 支持格式：UNIQUE (column1, column2, ...)
        Pattern uniquePattern = Pattern.compile("UNIQUE\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
        Matcher uniqueMatcher = uniquePattern.matcher(constraintDef);
        if (uniqueMatcher.find()) {
            String[] uniqueColumns = uniqueMatcher.group(1).split(",");
            List<String> columnList = new ArrayList<>();
            for (String col : uniqueColumns) {
                columnList.add(col.trim().replaceAll("`", ""));
            }
            constraints.add(new IRUniqueConstraint(null, columnList));
        }
    }

    /**
     * 解析检查约束
     */
    private void parseCheckConstraint(String constraintDef, List<IRConstraint> constraints) {
        // 简化的检查约束解析
        // 支持格式：CHECK (condition)
        Pattern checkPattern = Pattern.compile("CHECK\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
        Matcher checkMatcher = checkPattern.matcher(constraintDef);
        if (checkMatcher.find()) {
            String condition = checkMatcher.group(1);
            // 创建一个简单的字面量表达式来表示CHECK条件
            com.xylink.sqltranspiler.v2.ir.expression.IRLiteral conditionExpr =
                new com.xylink.sqltranspiler.v2.ir.expression.IRLiteral(condition,
                    com.xylink.sqltranspiler.v2.ir.expression.IRLiteral.LiteralType.STRING);
            constraints.add(com.xylink.sqltranspiler.v2.ir.constraint.IRCheckConstraint.of(conditionExpr));
        }
    }
    
    /**
     * 解析SELECT语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
     */
    private IRNode parseSelect(String sql) {
        try {
            // 创建一个简化的IRSelect节点
            // 在完整实现中，应该解析SELECT的各个部分：
            // - SELECT子句（列列表）
            // - FROM子句（表和JOIN）
            // - WHERE子句（条件）
            // - GROUP BY子句
            // - HAVING子句
            // - ORDER BY子句
            // - LIMIT子句

            // 解析LIMIT子句（如果存在）
            IRLimitClause limitClause = parseLimitClause(sql);

            // 暂时创建一个包含原始SQL的IRSelect节点
            // 在完整实现中，应该解析所有子句
            IRSelect select = new IRSelect(
                null, // selectItems - TODO: 解析SELECT列表
                null, // fromClause - TODO: 解析FROM子句
                null, // whereClause - TODO: 解析WHERE子句
                null, // groupByClause - TODO: 解析GROUP BY子句
                null, // havingClause - TODO: 解析HAVING子句
                null, // orderByClause - TODO: 解析ORDER BY子句
                limitClause // limitClause
            );
            select.getMetadata().set("originalSql", sql);

            return select;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }

    /**
     * 解析LIMIT子句
     */
    private IRLimitClause parseLimitClause(String sql) {
        // 解析 LIMIT n 或 LIMIT n OFFSET m
        Pattern limitPattern = Pattern.compile("LIMIT\\s+(\\d+)(?:\\s+OFFSET\\s+(\\d+))?", Pattern.CASE_INSENSITIVE);
        Matcher limitMatcher = limitPattern.matcher(sql);

        if (limitMatcher.find()) {
            int limit = Integer.parseInt(limitMatcher.group(1));
            int offset = 0;

            if (limitMatcher.group(2) != null) {
                offset = Integer.parseInt(limitMatcher.group(2));
            }

            // 创建LIMIT子句
            return new IRLimitClause(limit, offset);
        }

        return null; // 没有LIMIT子句
    }
    
    /**
     * 解析INSERT语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/insert.html
     */
    private IRNode parseInsert(String sql) {
        try {
            // 创建一个简化的IRInsert节点
            // 在完整实现中，应该解析INSERT的各个部分：
            // - INSERT [IGNORE] INTO table_name
            // - 列列表 (column1, column2, ...)
            // - VALUES子句或SELECT子句
            // - ON DUPLICATE KEY UPDATE子句

            // 暂时创建一个包含原始SQL的IRInsert节点
            IRInsert insert = new IRInsert(
                null, // tableName - TODO: 解析表名
                null, // columns - TODO: 解析列列表
                null  // values - TODO: 解析VALUES子句
            );
            insert.getMetadata().set("originalSql", sql);

            // TODO: 解析IGNORE关键字
            // TODO: 解析ON DUPLICATE KEY UPDATE子句
            // TODO: 解析INSERT ... SELECT语句

            return insert;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 解析UPDATE语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/update.html
     */
    private IRNode parseUpdate(String sql) {
        try {
            // 创建一个简化的IRUpdate节点
            // 在完整实现中，应该解析UPDATE的各个部分：
            // - UPDATE table_name
            // - SET column1 = value1, column2 = value2, ...
            // - WHERE条件
            // - ORDER BY子句
            // - LIMIT子句

            // 暂时创建一个包含原始SQL的IRUpdate节点
            IRUpdate update = new IRUpdate(
                null, // tableName - TODO: 解析表名
                null, // setClause - TODO: 解析SET子句
                null  // whereClause - TODO: 解析WHERE子句
            );
            update.getMetadata().set("originalSql", sql);

            return update;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 解析DELETE语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/delete.html
     */
    private IRNode parseDelete(String sql) {
        try {
            // 创建一个简化的IRDelete节点
            // 在完整实现中，应该解析DELETE的各个部分：
            // - DELETE FROM table_name
            // - WHERE条件
            // - ORDER BY子句
            // - LIMIT子句
            // - 多表DELETE语法

            // 暂时创建一个包含原始SQL的IRDelete节点
            IRDelete delete = new IRDelete(
                null, // tableName - TODO: 解析表名
                null  // whereClause - TODO: 解析WHERE子句
            );
            delete.getMetadata().set("originalSql", sql);

            return delete;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 解析CREATE INDEX语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-index.html
     */
    private IRNode parseCreateIndex(String sql) {
        try {
            // 创建一个简化的IRCreateIndex节点
            // 在完整实现中，应该解析CREATE INDEX的各个部分：
            // - CREATE [UNIQUE] INDEX index_name
            // - ON table_name (column_list)
            // - 索引类型和选项

            // 暂时创建一个包含原始SQL的IRCreateIndex节点
            com.xylink.sqltranspiler.v2.ir.statement.IRCreateIndex createIndex =
                new com.xylink.sqltranspiler.v2.ir.statement.IRCreateIndex(
                    null, // indexName - TODO: 解析索引名
                    null, // tableName - TODO: 解析表名
                    java.util.List.of(), // columns - TODO: 解析列列表
                    false // unique - TODO: 解析UNIQUE关键字
                );
            createIndex.getMetadata().set("originalSql", sql);

            return createIndex;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 解析DROP INDEX语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/drop-index.html
     */
    private IRNode parseDropIndex(String sql) {
        try {
            // 创建一个简化的IRDropIndex节点
            // 在完整实现中，应该解析DROP INDEX的各个部分：
            // - DROP INDEX index_name ON table_name

            // 暂时创建一个包含原始SQL的IRDropIndex节点
            com.xylink.sqltranspiler.v2.ir.statement.IRDropIndex dropIndex =
                new com.xylink.sqltranspiler.v2.ir.statement.IRDropIndex(
                    null, // indexName - TODO: 解析索引名
                    null  // tableName - TODO: 解析表名
                );
            dropIndex.getMetadata().set("originalSql", sql);

            return dropIndex;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 解析DROP TABLE语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/drop-table.html
     */
    private IRNode parseDropTable(String sql) {
        try {
            // 创建一个简化的IRDropTable节点
            // 在完整实现中，应该解析DROP TABLE的各个部分：
            // - DROP TABLE [IF EXISTS] table_name

            // 暂时创建一个包含原始SQL的IRDropTable节点
            com.xylink.sqltranspiler.v2.ir.statement.IRDropTable dropTable =
                new com.xylink.sqltranspiler.v2.ir.statement.IRDropTable(
                    null, // tableName - TODO: 解析表名
                    false // ifExists - TODO: 解析IF EXISTS
                );
            dropTable.getMetadata().set("originalSql", sql);

            return dropTable;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 解析ALTER TABLE语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/alter-table.html
     */
    private IRNode parseAlterTable(String sql) {
        try {
            // 创建一个简化的IRAlterTable节点
            // 在完整实现中，应该解析ALTER TABLE的各个部分：
            // - ALTER TABLE table_name
            // - ADD COLUMN, DROP COLUMN, MODIFY COLUMN等操作

            // 暂时创建一个包含原始SQL的IRAlterTable节点
            com.xylink.sqltranspiler.v2.ir.statement.IRAlterTable alterTable =
                new com.xylink.sqltranspiler.v2.ir.statement.IRAlterTable(
                    null, // tableName - TODO: 解析表名
                    null  // alterActions - TODO: 解析ALTER操作
                );
            alterTable.getMetadata().set("originalSql", sql);

            return alterTable;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }

    /**
     * 解析REPLACE INTO语句
     * 根据MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/replace.html
     */
    private IRNode parseReplaceInto(String sql) {
        try {
            // REPLACE INTO是MySQL特有的语法，等价于DELETE + INSERT
            // 创建一个特殊的IRReplaceInto节点，或者复用IRInsert节点

            // 暂时创建一个包含原始SQL的IRInsert节点，并标记为REPLACE
            IRInsert replaceInsert = new IRInsert(
                null, // tableName - TODO: 解析表名
                null, // columns - TODO: 解析列列表
                null  // values - TODO: 解析VALUES子句
            );
            replaceInsert.getMetadata().set("originalSql", sql);
            replaceInsert.getMetadata().set("isReplace", true); // 标记为REPLACE语句

            return replaceInsert;

        } catch (Exception e) {
            // 解析失败时返回不支持的节点
            return createUnsupportedNode(sql);
        }
    }
    
    /**
     * 创建不支持的节点
     */
    private IRNode createUnsupportedNode(String sql) {
        return new UnsupportedIRNode(sql);
    }
    
    /**
     * 不支持的IR节点实现
     */
    private static class UnsupportedIRNode implements IRNode {
        private final String originalSql;
        private final IRMetadata metadata;
        
        public UnsupportedIRNode(String originalSql) {
            this.originalSql = originalSql;
            this.metadata = new IRMetadata();
            this.metadata.set("originalSql", originalSql);
        }
        
        @Override
        public NodeType getNodeType() {
            return NodeType.UNKNOWN;
        }
        
        @Override
        public IRMetadata getMetadata() {
            return metadata;
        }
        
        @Override
        public SourceLocation getSourceLocation() {
            return null;
        }
        
        public String getOriginalSql() {
            return originalSql;
        }
    }
}
