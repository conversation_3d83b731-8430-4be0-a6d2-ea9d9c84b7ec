package com.xylink.sqltranspiler.v2.validation;

/**
 * 验证配置
 * 
 * 定义验证过程的配置选项。
 * 
 * 验证日期: 2024-01-15
 */
public final class ValidationConfig {
    
    private final boolean enabled;
    private final boolean strict;
    private final boolean validateTypes;
    private final boolean validateConstraints;
    
    private ValidationConfig(boolean enabled, boolean strict, boolean validateTypes, boolean validateConstraints) {
        this.enabled = enabled;
        this.strict = strict;
        this.validateTypes = validateTypes;
        this.validateConstraints = validateConstraints;
    }
    
    public static ValidationConfig defaultConfig() {
        return new ValidationConfig(true, false, true, true);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public boolean isEnabled() { return enabled; }
    public boolean isStrict() { return strict; }
    public boolean isValidateTypes() { return validateTypes; }
    public boolean isValidateConstraints() { return validateConstraints; }
    
    public static class Builder {
        private boolean enabled = true;
        private boolean strict = false;
        private boolean validateTypes = true;
        private boolean validateConstraints = true;
        
        public Builder enabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }
        
        public Builder strict(boolean strict) {
            this.strict = strict;
            return this;
        }
        
        public Builder validateTypes(boolean validateTypes) {
            this.validateTypes = validateTypes;
            return this;
        }
        
        public Builder validateConstraints(boolean validateConstraints) {
            this.validateConstraints = validateConstraints;
            return this;
        }
        
        public ValidationConfig build() {
            return new ValidationConfig(enabled, strict, validateTypes, validateConstraints);
        }
    }
}
