package com.xylink.sqltranspiler.v2.validation;

import java.util.List;
import java.util.ArrayList;

/**
 * 验证结果类
 * 
 * 根据官方文档实现：
 * - 用于表示验证操作的结果
 */
public class ValidationResult {
    
    private final boolean valid;
    private final List<ValidationMessage> messages;
    private final List<ValidationIssue> issues;
    
    /**
     * 构造函数
     */
    private ValidationResult(boolean valid, List<ValidationMessage> messages, List<ValidationIssue> issues) {
        this.valid = valid;
        this.messages = messages != null ? List.copyOf(messages) : List.of();
        this.issues = issues != null ? List.copyOf(issues) : List.of();
    }
    
    /**
     * 创建成功的验证结果
     */
    public static ValidationResult success() {
        return new ValidationResult(true, null, null);
    }
    
    /**
     * 创建成功的验证结果（带消息）
     */
    public static ValidationResult success(List<ValidationMessage> messages) {
        return new ValidationResult(true, messages, null);
    }
    
    /**
     * 创建失败的验证结果
     */
    public static ValidationResult failure(List<ValidationIssue> issues) {
        return new ValidationResult(false, null, issues);
    }
    
    /**
     * 创建失败的验证结果（带消息）
     */
    public static ValidationResult failure(List<ValidationMessage> messages, List<ValidationIssue> issues) {
        return new ValidationResult(false, messages, issues);
    }
    
    /**
     * 创建单个错误的验证结果
     */
    public static ValidationResult error(String message) {
        List<ValidationIssue> issues = new ArrayList<>();
        issues.add(new ValidationIssue(ValidationIssue.Severity.ERROR, message));
        return new ValidationResult(false, null, issues);
    }
    
    /**
     * 创建单个警告的验证结果
     */
    public static ValidationResult warning(String message) {
        List<ValidationMessage> messages = new ArrayList<>();
        messages.add(new ValidationMessage(ValidationMessage.MessageType.WARNING, message));
        return new ValidationResult(true, messages, null);
    }
    
    // Getters
    public boolean isValid() { return valid; }
    public boolean isInvalid() { return !valid; }
    public List<ValidationMessage> getMessages() { return messages; }
    public List<ValidationIssue> getIssues() { return issues; }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return (int) issues.stream()
            .filter(issue -> issue.getSeverity() == ValidationIssue.Severity.ERROR)
            .count();
    }
    
    /**
     * 获取警告数量
     */
    public int getWarningCount() {
        return (int) issues.stream()
            .filter(issue -> issue.getSeverity() == ValidationIssue.Severity.WARNING)
            .count();
    }
}