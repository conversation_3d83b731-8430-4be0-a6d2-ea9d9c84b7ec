package com.xylink.sqltranspiler.v2.validation;

import java.util.Map;
import java.util.HashMap;

/**
 * 验证上下文类
 * 
 * 根据官方文档实现：
 * - 提供验证过程中的上下文信息
 */
public class ValidationContext {
    
    private final Map<String, Object> properties;
    private final Map<String, Object> metadata;
    
    /**
     * 构造函数
     */
    public ValidationContext() {
        this.properties = new HashMap<>();
        this.metadata = new HashMap<>();
    }
    
    /**
     * 设置属性
     */
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    /**
     * 获取属性（带默认值）
     */
    public Object getProperty(String key, Object defaultValue) {
        return properties.getOrDefault(key, defaultValue);
    }
    
    /**
     * 设置元数据
     */
    public void setMetadata(String key, Object value) {
        metadata.put(key, value);
    }
    
    /**
     * 获取元数据
     */
    public Object getMetadata(String key) {
        return metadata.get(key);
    }
    
    /**
     * 获取所有属性
     */
    public Map<String, Object> getProperties() {
        return new HashMap<>(properties);
    }
    
    /**
     * 获取所有元数据
     */
    public Map<String, Object> getAllMetadata() {
        return new HashMap<>(metadata);
    }
}