package com.xylink.sqltranspiler.v2.validation;

/**
 * 验证问题类
 * 
 * 根据官方文档实现：
 * - 用于表示验证过程中发现的问题
 */
public class ValidationIssue {
    
    private final Severity severity;
    private final String message;
    private final String code;
    private final int line;
    private final int column;
    
    /**
     * 构造函数
     */
    public ValidationIssue(Severity severity, String message, String code, int line, int column) {
        this.severity = severity;
        this.message = message;
        this.code = code;
        this.line = line;
        this.column = column;
    }
    
    /**
     * 构造函数（简化版）
     */
    public ValidationIssue(Severity severity, String message) {
        this(severity, message, null, -1, -1);
    }
    
    // Getters
    public Severity getSeverity() { return severity; }
    public String getMessage() { return message; }
    public String getCode() { return code; }
    public int getLine() { return line; }
    public int getColumn() { return column; }
    
    /**
     * 严重程度枚举
     */
    public enum Severity {
        ERROR("ERROR"),
        WARNING("WARNING"),
        INFO("INFO");
        
        private final String name;
        
        Severity(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s (line: %d, column: %d)", 
                           severity.getName(), message, line, column);
    }
}