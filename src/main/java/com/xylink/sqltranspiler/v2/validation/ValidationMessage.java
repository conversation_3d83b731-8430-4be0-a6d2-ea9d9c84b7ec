package com.xylink.sqltranspiler.v2.validation;

/**
 * 验证消息类
 * 
 * 根据官方文档实现：
 * - 用于表示验证过程中的消息
 */
public class ValidationMessage {
    
    private final MessageType type;
    private final String message;
    private final String code;
    private final int line;
    private final int column;
    
    /**
     * 构造函数
     */
    public ValidationMessage(MessageType type, String message, String code, int line, int column) {
        this.type = type;
        this.message = message;
        this.code = code;
        this.line = line;
        this.column = column;
    }
    
    /**
     * 构造函数（简化版）
     */
    public ValidationMessage(MessageType type, String message) {
        this(type, message, null, -1, -1);
    }
    
    // Getters
    public MessageType getType() { return type; }
    public String getMessage() { return message; }
    public String getCode() { return code; }
    public int getLine() { return line; }
    public int getColumn() { return column; }
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        ERROR("ERROR"),
        WARNING("WARNING"),
        INFO("INFO");
        
        private final String name;
        
        MessageType(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s (line: %d, column: %d)", 
                           type.getName(), message, line, column);
    }
}