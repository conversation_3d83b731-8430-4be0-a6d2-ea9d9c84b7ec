package com.xylink.sqltranspiler.v2.rules;

import java.util.List;

/**
 * 规则验证结果类
 * 
 * 根据官方文档实现：
 * - 用于表示规则验证的结果
 */
public class RuleValidationResult {
    
    private final boolean valid;
    private final List<String> messages;
    private final String errorMessage;
    
    /**
     * 构造函数
     */
    private RuleValidationResult(boolean valid, List<String> messages, String errorMessage) {
        this.valid = valid;
        this.messages = messages != null ? List.copyOf(messages) : List.of();
        this.errorMessage = errorMessage;
    }
    
    /**
     * 创建成功的验证结果
     */
    public static RuleValidationResult success() {
        return new RuleValidationResult(true, null, null);
    }
    
    /**
     * 创建成功的验证结果（带消息）
     */
    public static RuleValidationResult success(List<String> messages) {
        return new RuleValidationResult(true, messages, null);
    }
    
    /**
     * 创建失败的验证结果
     */
    public static RuleValidationResult failure(String errorMessage) {
        return new RuleValidationResult(false, null, errorMessage);
    }
    
    /**
     * 创建失败的验证结果（带消息）
     */
    public static RuleValidationResult failure(String errorMessage, List<String> messages) {
        return new RuleValidationResult(false, messages, errorMessage);
    }
    
    // Getters
    public boolean isValid() { return valid; }
    public boolean isInvalid() { return !valid; }
    public List<String> getMessages() { return messages; }
    public String getErrorMessage() { return errorMessage; }
}