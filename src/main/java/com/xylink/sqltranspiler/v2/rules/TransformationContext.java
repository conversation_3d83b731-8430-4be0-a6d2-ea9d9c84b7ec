package com.xylink.sqltranspiler.v2.rules;

import java.util.Map;
import java.util.HashMap;

/**
 * 转换上下文类
 * 
 * 根据官方文档实现：
 * - 提供转换过程中的上下文信息
 */
public class TransformationContext {
    
    private final Map<String, Object> properties;
    private final Map<String, Object> statistics;
    private final Map<String, Object> details;
    
    /**
     * 构造函数
     */
    public TransformationContext() {
        this.properties = new HashMap<>();
        this.statistics = new HashMap<>();
        this.details = new HashMap<>();
    }
    
    /**
     * 设置属性
     */
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    /**
     * 获取属性（带默认值）
     */
    public Object getProperty(String key, Object defaultValue) {
        return properties.getOrDefault(key, defaultValue);
    }
    
    /**
     * 设置统计信息
     */
    public void setStatistic(String key, Object value) {
        statistics.put(key, value);
    }
    
    /**
     * 获取统计信息
     */
    public Object getStatistic(String key) {
        return statistics.get(key);
    }
    
    /**
     * 获取所有统计信息
     */
    public Map<String, Object> getStatistics() {
        return new HashMap<>(statistics);
    }
    
    /**
     * 设置详细信息
     */
    public void setDetail(String key, Object value) {
        details.put(key, value);
    }
    
    /**
     * 获取详细信息
     */
    public Object getDetail(String key) {
        return details.get(key);
    }
    
    /**
     * 获取所有详细信息
     */
    public Map<String, Object> getDetails() {
        return new HashMap<>(details);
    }
}