package com.xylink.sqltranspiler.v2.rules;

/**
 * 转换规则类别枚举
 * 
 * 官方文档依据：
 * - Apache Calcite官方文档: https://calcite.apache.org/docs/volcano.html
 *   Volcano优化器规则分类设计理念
 * - Apache Calcite官方文档: https://calcite.apache.org/javadocAggregate/org/apache/calcite/plan/RelOptRule.html
 *   RelOptRule类的规则分类标准
 * 
 * 验证日期: 2024-01-15
 * 
 * 设计原则：
 * 1. 清晰分类：每个规则都有明确的类别归属
 * 2. 优先级支持：不同类别有不同的执行优先级
 * 3. 可扩展性：支持新的规则类别
 * 4. 官方文档对齐：与Apache Calcite规则分类保持一致
 */
public enum RuleCategory {
    
    /**
     * DDL语句转换规则
     *
     * 处理DDL语句的转换，如：
     * - CREATE TABLE语句转换
     * - ALTER TABLE语句转换
     * - CREATE INDEX语句转换
     */
    DDL("DDL转换", 110),

    /**
     * 语法转换规则
     *
     * 处理不同数据库间的语法差异，如：
     * - AUTO_INCREMENT -> IDENTITY/SERIAL
     * - LIMIT -> ROWNUM
     * - 数据类型转换
     */
    SYNTAX_TRANSFORMATION("语法转换", 100),

    /**
     * 数据类型转换规则
     *
     * 处理数据类型的映射和转换，如：
     * - MySQL LONGTEXT -> 达梦 CLOB
     * - MySQL DATETIME -> 金仓 TIMESTAMP
     */
    DATA_TYPE_CONVERSION("数据类型转换", 90),

    /**
     * 数据类型规则（简化别名）
     *
     * DATA_TYPE_CONVERSION的简化别名，用于向后兼容
     */
    DATA_TYPE("数据类型", 90),
    
    /**
     * 函数转换规则
     * 
     * 处理函数名称和参数的转换，如：
     * - NOW() -> SYSDATE
     * - CONCAT() -> ||
     */
    FUNCTION_TRANSFORMATION("函数转换", 80),
    
    /**
     * 约束转换规则
     * 
     * 处理约束定义的转换，如：
     * - 外键约束语法差异
     * - 检查约束转换
     */
    CONSTRAINT_TRANSFORMATION("约束转换", 70),
    
    /**
     * 索引转换规则
     * 
     * 处理索引定义的转换，如：
     * - 索引类型转换
     * - 索引选项转换
     */
    INDEX_TRANSFORMATION("索引转换", 60),
    
    /**
     * 查询优化规则
     * 
     * 处理查询性能优化，如：
     * - 谓词下推
     * - JOIN重排序
     * - 子查询优化
     */
    QUERY_OPTIMIZATION("查询优化", 50),
    
    /**
     * 表达式重写规则
     * 
     * 处理表达式的重写和简化，如：
     * - 常量折叠
     * - 表达式简化
     */
    EXPRESSION_REWRITE("表达式重写", 40),
    
    /**
     * 兼容性处理规则
     * 
     * 处理数据库兼容性问题，如：
     * - 不支持特性的替代方案
     * - 兼容性警告
     */
    COMPATIBILITY_HANDLING("兼容性处理", 30),
    
    /**
     * 性能优化规则
     * 
     * 处理性能相关的优化，如：
     * - 批量操作优化
     * - 索引提示
     */
    PERFORMANCE_OPTIMIZATION("性能优化", 20),
    
    /**
     * 后处理规则
     * 
     * 处理最终的清理和格式化，如：
     * - SQL格式化
     * - 注释处理
     */
    POST_PROCESSING("后处理", 10);
    
    private final String displayName;
    private final int priority;
    
    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param priority 优先级（数值越大优先级越高）
     */
    RuleCategory(String displayName, int priority) {
        this.displayName = displayName;
        this.priority = priority;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取优先级
     */
    public int getPriority() {
        return priority;
    }
    
    /**
     * 检查是否为核心转换规则
     */
    public boolean isCoreTransformation() {
        switch (this) {
            case SYNTAX_TRANSFORMATION:
            case DATA_TYPE_CONVERSION:
            case FUNCTION_TRANSFORMATION:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 检查是否为优化规则
     */
    public boolean isOptimization() {
        switch (this) {
            case QUERY_OPTIMIZATION:
            case EXPRESSION_REWRITE:
            case PERFORMANCE_OPTIMIZATION:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 检查是否为后处理规则
     */
    public boolean isPostProcessing() {
        return this == POST_PROCESSING;
    }
    
    /**
     * 获取规则类别的描述
     */
    public String getDescription() {
        switch (this) {
            case DDL:
                return "处理DDL语句的转换，确保DDL语句在目标数据库中正确执行";
            case SYNTAX_TRANSFORMATION:
                return "处理不同数据库间的语法差异，确保SQL语法在目标数据库中正确";
            case DATA_TYPE_CONVERSION:
            case DATA_TYPE:
                return "处理数据类型的映射和转换，确保数据类型兼容性";
            case FUNCTION_TRANSFORMATION:
                return "处理函数名称和参数的转换，确保函数调用正确";
            case CONSTRAINT_TRANSFORMATION:
                return "处理约束定义的转换，确保约束在目标数据库中有效";
            case INDEX_TRANSFORMATION:
                return "处理索引定义的转换，确保索引在目标数据库中正确创建";
            case QUERY_OPTIMIZATION:
                return "处理查询性能优化，提升SQL执行效率";
            case EXPRESSION_REWRITE:
                return "处理表达式的重写和简化，优化表达式计算";
            case COMPATIBILITY_HANDLING:
                return "处理数据库兼容性问题，提供替代方案或警告";
            case PERFORMANCE_OPTIMIZATION:
                return "处理性能相关的优化，提升整体执行性能";
            case POST_PROCESSING:
                return "处理最终的清理和格式化，确保输出质量";
            default:
                return "未知规则类别";
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s (优先级: %d)", displayName, priority);
    }
}
