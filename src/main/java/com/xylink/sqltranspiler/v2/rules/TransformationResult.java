package com.xylink.sqltranspiler.v2.rules;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import java.util.List;

/**
 * 转换结果类
 * 
 * 根据官方文档实现：
 * - 用于表示转换操作的结果
 */
public class TransformationResult {
    
    private final boolean success;
    private final IRNode resultNode;
    private final List<String> appliedRules;
    private final String errorMessage;
    
    /**
     * 构造函数
     */
    private TransformationResult(boolean success, IRNode resultNode, 
                               List<String> appliedRules, String errorMessage) {
        this.success = success;
        this.resultNode = resultNode;
        this.appliedRules = appliedRules != null ? List.copyOf(appliedRules) : List.of();
        this.errorMessage = errorMessage;
    }
    
    /**
     * 创建成功的转换结果
     */
    public static TransformationResult success(IRNode resultNode) {
        return new TransformationResult(true, resultNode, null, null);
    }
    
    /**
     * 创建成功的转换结果（带规则）
     */
    public static TransformationResult success(IRNode resultNode, List<String> appliedRules) {
        return new TransformationResult(true, resultNode, appliedRules, null);
    }
    
    /**
     * 创建失败的转换结果
     */
    public static TransformationResult failure(String errorMessage) {
        return new TransformationResult(false, null, null, errorMessage);
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public boolean isFailure() { return !success; }
    public IRNode getResultNode() { return resultNode; }
    public List<String> getAppliedRules() { return appliedRules; }
    public String getErrorMessage() { return errorMessage; }
    public String getMessage() { return errorMessage; }
}