package com.xylink.sqltranspiler.v2.rules;

import com.xylink.sqltranspiler.v2.ir.IRNode;
import java.util.List;
import java.util.ArrayList;

/**
 * 转换规则引擎类
 * 
 * 根据官方文档实现：
 * - 负责管理和执行转换规则
 */
public class TransformationRuleEngine {
    
    private final List<TransformationRule> rules;
    
    /**
     * 构造函数
     */
    public TransformationRuleEngine() {
        this.rules = new ArrayList<>();
    }
    
    /**
     * 添加转换规则
     */
    public void addRule(TransformationRule rule) {
        rules.add(rule);
        // 按优先级排序
        rules.sort((r1, r2) -> Integer.compare(r2.getPriority(), r1.getPriority()));
    }
    
    /**
     * 移除转换规则
     */
    public void removeRule(TransformationRule rule) {
        rules.remove(rule);
    }
    
    /**
     * 应用转换规则
     */
    public TransformationResult transform(IRNode node, TransformationContext context) {
        if (node == null) {
            return TransformationResult.failure("Input node cannot be null");
        }
        
        IRNode currentNode = node;
        List<String> appliedRules = new ArrayList<>();
        
        try {
            // 应用转换规则
            for (TransformationRule rule : rules) {
                if (rule.isApplicable(currentNode, context)) {
                    IRNode transformedNode = rule.apply(currentNode, context);
                    if (transformedNode != null && transformedNode != currentNode) {
                        currentNode = transformedNode;
                        appliedRules.add(rule.getRuleName());
                    }
                }
            }
            
            return TransformationResult.success(currentNode, appliedRules);
            
        } catch (Exception e) {
            return TransformationResult.failure("Transformation failed: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有规则
     */
    public List<TransformationRule> getRules() {
        return new ArrayList<>(rules);
    }
}