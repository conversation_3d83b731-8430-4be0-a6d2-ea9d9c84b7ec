package com.xylink.sqltranspiler.v2.rules;

import java.util.Set;

/**
 * 转换配置
 * 
 * 定义转换过程的配置选项。
 * 
 * 验证日期: 2024-01-15
 */
public final class TransformationConfig {
    
    private final boolean enabled;
    private final boolean optimizationEnabled;
    private final boolean strictMode;
    private final Set<String> enabledRules;
    private final Set<String> disabledRules;
    private final int maxIterations;
    private final long timeoutMillis;
    
    /**
     * 构造函数
     */
    private TransformationConfig(boolean enabled,
                                boolean optimizationEnabled,
                                boolean strictMode,
                                Set<String> enabledRules,
                                Set<String> disabledRules,
                                int maxIterations,
                                long timeoutMillis) {
        this.enabled = enabled;
        this.optimizationEnabled = optimizationEnabled;
        this.strictMode = strictMode;
        this.enabledRules = enabledRules != null ? Set.copyOf(enabledRules) : Set.of();
        this.disabledRules = disabledRules != null ? Set.copyOf(disabledRules) : Set.of();
        this.maxIterations = maxIterations;
        this.timeoutMillis = timeoutMillis;
    }
    
    /**
     * 创建默认配置
     * 
     * @return 默认配置
     */
    public static TransformationConfig defaultConfig() {
        return new TransformationConfig(true, true, false, Set.of(), Set.of(), 100, 30000);
    }
    
    /**
     * 创建建造者
     * 
     * @return 配置建造者
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 是否启用转换
     * 
     * @return 是否启用
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * 是否启用优化
     * 
     * @return 是否启用优化
     */
    public boolean isOptimizationEnabled() {
        return optimizationEnabled;
    }
    
    /**
     * 是否严格模式
     * 
     * @return 是否严格模式
     */
    public boolean isStrictMode() {
        return strictMode;
    }
    
    /**
     * 获取启用的规则集合
     * 
     * @return 启用的规则集合
     */
    public Set<String> getEnabledRules() {
        return enabledRules;
    }
    
    /**
     * 获取禁用的规则集合
     * 
     * @return 禁用的规则集合
     */
    public Set<String> getDisabledRules() {
        return disabledRules;
    }
    
    /**
     * 获取最大迭代次数
     * 
     * @return 最大迭代次数
     */
    public int getMaxIterations() {
        return maxIterations;
    }
    
    /**
     * 获取超时时间（毫秒）
     * 
     * @return 超时时间
     */
    public long getTimeoutMillis() {
        return timeoutMillis;
    }
    
    /**
     * 判断规则是否启用
     * 
     * @param ruleName 规则名称
     * @return 是否启用
     */
    public boolean isRuleEnabled(String ruleName) {
        if (!enabledRules.isEmpty()) {
            return enabledRules.contains(ruleName);
        }
        return !disabledRules.contains(ruleName);
    }
    
    /**
     * 配置建造者
     */
    public static class Builder {
        private boolean enabled = true;
        private boolean optimizationEnabled = true;
        private boolean strictMode = false;
        private Set<String> enabledRules = Set.of();
        private Set<String> disabledRules = Set.of();
        private int maxIterations = 100;
        private long timeoutMillis = 30000;
        
        public Builder enabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }
        
        public Builder optimizationEnabled(boolean optimizationEnabled) {
            this.optimizationEnabled = optimizationEnabled;
            return this;
        }
        
        public Builder strictMode(boolean strictMode) {
            this.strictMode = strictMode;
            return this;
        }
        
        public Builder enabledRules(Set<String> enabledRules) {
            this.enabledRules = enabledRules;
            return this;
        }
        
        public Builder disabledRules(Set<String> disabledRules) {
            this.disabledRules = disabledRules;
            return this;
        }
        
        public Builder maxIterations(int maxIterations) {
            this.maxIterations = maxIterations;
            return this;
        }
        
        public Builder timeoutMillis(long timeoutMillis) {
            this.timeoutMillis = timeoutMillis;
            return this;
        }
        
        public TransformationConfig build() {
            return new TransformationConfig(enabled, optimizationEnabled, strictMode,
                                          enabledRules, disabledRules, maxIterations, timeoutMillis);
        }
    }
}
