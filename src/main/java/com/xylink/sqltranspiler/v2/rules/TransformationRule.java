package com.xylink.sqltranspiler.v2.rules;

import com.xylink.sqltranspiler.v2.ir.IRNode;

/**
 * 转换规则接口
 * 
 * 根据官方文档实现：
 * - 用于定义IR节点的转换规则
 */
public interface TransformationRule {
    
    /**
     * 获取规则名称
     */
    String getRuleName();
    
    /**
     * 检查是否适用于指定节点
     */
    boolean isApplicable(IRNode node, TransformationContext context);
    
    /**
     * 应用转换规则
     */
    IRNode apply(IRNode node, TransformationContext context);
    
    /**
     * 获取规则优先级
     */
    int getPriority();
    
    /**
     * 获取规则描述
     */
    String getDescription();
}