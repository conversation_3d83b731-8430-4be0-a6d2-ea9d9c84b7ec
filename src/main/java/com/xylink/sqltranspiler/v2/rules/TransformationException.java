package com.xylink.sqltranspiler.v2.rules;

/**
 * 转换异常
 * 
 * 表示转换规则应用过程中发生的异常。
 * 
 * 官方文档依据：
 * - Apache Calcite官方文档: https://calcite.apache.org/javadocAggregate/org/apache/calcite/plan/RelOptPlanner.html
 *   RelOptPlanner的异常处理机制
 * 
 * 验证日期: 2024-01-15
 */
public class TransformationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public TransformationException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public TransformationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param cause 原因异常
     */
    public TransformationException(Throwable cause) {
        super(cause);
    }
}
