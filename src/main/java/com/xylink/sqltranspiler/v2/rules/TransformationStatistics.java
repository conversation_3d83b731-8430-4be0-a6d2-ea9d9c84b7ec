package com.xylink.sqltranspiler.v2.rules;

import java.time.Duration;
import java.util.Map;
import java.util.HashMap;

/**
 * 转换统计信息类
 * 
 * 根据官方文档实现：
 * - 用于收集和报告转换过程的统计信息
 */
public class TransformationStatistics {
    
    private final long transformationTimeMs;
    private final int rulesApplied;
    private final int nodesProcessed;
    private final Map<String, Integer> ruleApplicationCounts;
    private final Map<String, Object> additionalMetrics;
    
    /**
     * 构造函数
     */
    public TransformationStatistics(long transformationTimeMs, int rulesApplied, int nodesProcessed,
                                  Map<String, Integer> ruleApplicationCounts, 
                                  Map<String, Object> additionalMetrics) {
        this.transformationTimeMs = transformationTimeMs;
        this.rulesApplied = rulesApplied;
        this.nodesProcessed = nodesProcessed;
        this.ruleApplicationCounts = ruleApplicationCounts != null ? 
            Map.copyOf(ruleApplicationCounts) : Map.of();
        this.additionalMetrics = additionalMetrics != null ? 
            Map.copyOf(additionalMetrics) : Map.of();
    }
    
    /**
     * 获取转换时间
     */
    public Duration getTransformationTime() {
        return Duration.ofMillis(transformationTimeMs);
    }
    
    // Getters
    public long getTransformationTimeMs() { return transformationTimeMs; }
    public int getRulesApplied() { return rulesApplied; }
    public int getNodesProcessed() { return nodesProcessed; }
    public Map<String, Integer> getRuleApplicationCounts() { return ruleApplicationCounts; }
    public Map<String, Object> getAdditionalMetrics() { return additionalMetrics; }
}