package com.xylink.sqltranspiler.integration.conversion;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * 基于文件的SQL转换集成测试
 * 
 * 严格遵循官方文档：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 * 
 * 根据rule-db.md规范：
 * - 所有转换规则基于官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. 基于文件的DDL语句批量转换
 * 2. 基于文件的DML语句批量转换
 * 3. 复杂SQL语句文件转换
 * 4. 大文件转换性能测试
 * 5. 错误SQL文件处理测试
 */
@DisplayName("基于文件的SQL转换集成测试")
public class FileBasedTranspilationTest {

    private SqlTranspiler damengTranspiler;
    private SqlTranspiler kingbaseTranspiler;
    private SqlTranspiler shentongTranspiler;

    private static final String TEST_RESOURCES_PATH = "src/test/resources/sql/input/mysql";

    @BeforeEach
    void setUp() {
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(60))
            .build();
        
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(60))
            .build();
        
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(60))
            .build();
    }

    @Nested
    @DisplayName("DDL语句文件转换测试")
    class DdlFileTranspilationTests {
        
        @Test
        @DisplayName("测试CREATE TABLE语句文件转换")
        void testCreateTableStatementsFileTranspilation() throws IOException {
            Path sqlFile = Paths.get(TEST_RESOURCES_PATH, "basic", "create_table_statements.sql");
            
            if (!Files.exists(sqlFile)) {
                System.out.println("跳过测试：SQL文件不存在 - " + sqlFile);
                return;
            }
            
            List<String> sqlStatements = parseSqlFile(sqlFile);
            assertTrue(sqlStatements.size() > 0, "SQL文件应包含语句");
            
            System.out.println("=== CREATE TABLE语句文件转换测试 ===");
            System.out.println("文件路径: " + sqlFile);
            System.out.println("语句数量: " + sqlStatements.size());
            
            // 测试达梦转换
            testFileTranspilation(damengTranspiler, sqlStatements, "达梦", "CREATE TABLE");
            
            // 测试金仓转换
            testFileTranspilation(kingbaseTranspiler, sqlStatements, "金仓", "CREATE TABLE");
            
            // 测试神通转换
            testFileTranspilation(shentongTranspiler, sqlStatements, "神通", "CREATE TABLE");
        }
    }

    @Nested
    @DisplayName("DML语句文件转换测试")
    class DmlFileTranspilationTests {
        
        @Test
        @DisplayName("测试DML语句文件转换")
        void testDmlStatementsFileTranspilation() throws IOException {
            Path sqlFile = Paths.get(TEST_RESOURCES_PATH, "basic", "dml_statements.sql");
            
            if (!Files.exists(sqlFile)) {
                System.out.println("跳过测试：SQL文件不存在 - " + sqlFile);
                return;
            }
            
            List<String> sqlStatements = parseSqlFile(sqlFile);
            assertTrue(sqlStatements.size() > 0, "SQL文件应包含语句");
            
            System.out.println("=== DML语句文件转换测试 ===");
            System.out.println("文件路径: " + sqlFile);
            System.out.println("语句数量: " + sqlStatements.size());
            
            // 测试达梦转换
            testFileTranspilation(damengTranspiler, sqlStatements, "达梦", "DML");
            
            // 测试金仓转换
            testFileTranspilation(kingbaseTranspiler, sqlStatements, "金仓", "DML");
            
            // 测试神通转换
            testFileTranspilation(shentongTranspiler, sqlStatements, "神通", "DML");
        }
    }

    @Nested
    @DisplayName("文件转换性能测试")
    class FileTranspilationPerformanceTests {
        
        @Test
        @DisplayName("测试大文件转换性能")
        void testLargeFileTranspilationPerformance() throws IOException {
            // 生成大量SQL语句进行性能测试
            List<String> largeSqlBatch = generateLargeSqlBatch(200);
            
            System.out.println("=== 大文件转换性能测试 ===");
            System.out.println("语句数量: " + largeSqlBatch.size());
            
            // 测试达梦转换性能
            testTranspilationPerformance(damengTranspiler, largeSqlBatch, "达梦");
            
            // 测试金仓转换性能
            testTranspilationPerformance(kingbaseTranspiler, largeSqlBatch, "金仓");
            
            // 测试神通转换性能
            testTranspilationPerformance(shentongTranspiler, largeSqlBatch, "神通");
        }

        private List<String> generateLargeSqlBatch(int count) {
            List<String> templates = Arrays.asList(
                "CREATE TABLE test_table_%d (id INT PRIMARY KEY, name VARCHAR(100))",
                "INSERT INTO test_table_%d (id, name) VALUES (%d, 'Test Name %d')",
                "SELECT * FROM test_table_%d WHERE id = %d",
                "UPDATE test_table_%d SET name = 'Updated Name %d' WHERE id = %d",
                "DELETE FROM test_table_%d WHERE id = %d",
                "CREATE INDEX idx_test_%d ON test_table_%d (name)",
                "DROP INDEX idx_test_%d ON test_table_%d",
                "DROP TABLE test_table_%d"
            );
            
            return templates.stream()
                .flatMap(template -> 
                    java.util.stream.IntStream.range(1, count / templates.size() + 1)
                        .mapToObj(i -> String.format(template, i, i, i, i, i, i, i, i, i, i, i, i, i))
                )
                .limit(count)
                .collect(Collectors.toList());
        }

        private void testTranspilationPerformance(SqlTranspiler transpiler, List<String> statements, String dialectName) {
            long startTime = System.currentTimeMillis();
            List<TranspilationResult> results = transpiler.transpileAll(statements);
            long endTime = System.currentTimeMillis();
            
            long duration = endTime - startTime;
            double avgTimePerStatement = (double) duration / statements.size();
            
            System.out.println("\n" + dialectName + "转换性能:");
            System.out.println("总耗时: " + duration + "ms");
            System.out.println("平均每条语句耗时: " + String.format("%.2f", avgTimePerStatement) + "ms");
            
            // 验证结果
            assertEquals(statements.size(), results.size(), dialectName + "结果数量应与输入一致");
            
            // 验证性能要求（每条语句平均不超过50ms）
            assertTrue(avgTimePerStatement < 50, 
                      dialectName + "平均转换时间应小于50ms，实际: " + avgTimePerStatement + "ms");
            
            // 统计成功率
            long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
            double successRate = (double) successCount / results.size() * 100;
            
            System.out.println("成功转换: " + successCount + "/" + results.size() + 
                             " (" + String.format("%.1f", successRate) + "%)");
            
            // 验证成功率（应该大于90%）
            assertTrue(successRate > 90, dialectName + "转换成功率应大于90%，实际: " + successRate + "%");
        }
    }

    @Nested
    @DisplayName("错误处理文件测试")
    class ErrorHandlingFileTests {
        
        @Test
        @DisplayName("测试包含错误SQL的文件处理")
        void testErrorSqlFileHandling() {
            // 创建包含错误SQL的测试数据
            List<String> mixedSqlStatements = Arrays.asList(
                "CREATE TABLE valid_table (id INT PRIMARY KEY)",  // 有效
                "INVALID SQL STATEMENT HERE",  // 无效
                "SELECT * FROM valid_table",  // 有效
                "",  // 空语句
                "INSERT INTO valid_table VALUES (1)",  // 有效
                "SELECT * FROM non_existent_function(invalid_syntax)",  // 可能无效
                "UPDATE valid_table SET id = 2 WHERE id = 1",  // 有效
                "DROP TABLE valid_table"  // 有效
            );
            
            System.out.println("=== 错误SQL文件处理测试 ===");
            System.out.println("混合语句数量: " + mixedSqlStatements.size());
            
            // 测试达梦错误处理
            testErrorHandling(damengTranspiler, mixedSqlStatements, "达梦");
            
            // 测试金仓错误处理
            testErrorHandling(kingbaseTranspiler, mixedSqlStatements, "金仓");
            
            // 测试神通错误处理
            testErrorHandling(shentongTranspiler, mixedSqlStatements, "神通");
        }

        private void testErrorHandling(SqlTranspiler transpiler, List<String> statements, String dialectName) {
            List<TranspilationResult> results = transpiler.transpileAll(statements);
            
            System.out.println("\n" + dialectName + "错误处理测试:");
            
            assertEquals(statements.size(), results.size(), dialectName + "结果数量应与输入一致");
            
            int successCount = 0;
            int failureCount = 0;
            
            for (int i = 0; i < results.size(); i++) {
                TranspilationResult result = results.get(i);
                String originalSql = statements.get(i);
                
                if (result.isSuccess()) {
                    successCount++;
                } else {
                    failureCount++;
                    System.out.println("失败语句 " + (i + 1) + ": " + originalSql);
                    System.out.println("错误信息: " + result.getErrorMessage());
                }
                
                // 验证结果一致性
                assertNotNull(result, dialectName + "转换结果不应为空");
                assertEquals(originalSql, result.getOriginalSql(), "原始SQL应正确保存");
            }
            
            System.out.println("成功: " + successCount + ", 失败: " + failureCount);
            
            // 验证至少有一些成功的转换
            assertTrue(successCount > 0, dialectName + "应该有一些成功的转换");
        }
    }

    /**
     * 解析SQL文件，提取单独的SQL语句
     */
    private List<String> parseSqlFile(Path sqlFile) throws IOException {
        String content = Files.readString(sqlFile);

        // 移除注释行
        String cleanContent = Arrays.stream(content.split("\n"))
            .filter(line -> !line.trim().startsWith("--"))
            .collect(Collectors.joining("\n"));

        return Arrays.stream(cleanContent.split(";"))
            .map(String::trim)
            .filter(sql -> !sql.isEmpty())
            .filter(sql -> sql.toUpperCase().contains("CREATE") ||
                          sql.toUpperCase().contains("SELECT") ||
                          sql.toUpperCase().contains("INSERT") ||
                          sql.toUpperCase().contains("UPDATE") ||
                          sql.toUpperCase().contains("DELETE") ||
                          sql.toUpperCase().contains("DROP") ||
                          sql.toUpperCase().contains("ALTER"))  // 确保是有效的SQL语句
            .collect(Collectors.toList());
    }

    /**
     * 测试文件转换的通用方法
     */
    private void testFileTranspilation(SqlTranspiler transpiler, List<String> statements, 
                                     String dialectName, String statementType) {
        System.out.println("\n" + dialectName + " " + statementType + "转换测试:");
        
        List<TranspilationResult> results = transpiler.transpileAll(statements);
        
        assertEquals(statements.size(), results.size(), 
                    dialectName + "转换结果数量应与输入语句数量一致");
        
        int successCount = 0;
        int failureCount = 0;
        
        for (int i = 0; i < Math.min(5, results.size()); i++) {  // 只显示前5个结果
            TranspilationResult result = results.get(i);
            String originalSql = statements.get(i);
            
            System.out.println("\n语句 " + (i + 1) + ":");
            System.out.println("输入: " + (originalSql.length() > 100 ? 
                              originalSql.substring(0, 100) + "..." : originalSql));
            
            if (result.isSuccess()) {
                successCount++;
                String targetSql = result.getTargetSql();
                System.out.println("输出: " + (targetSql.length() > 100 ? 
                                  targetSql.substring(0, 100) + "..." : targetSql));
            } else {
                failureCount++;
                System.out.println("错误: " + result.getErrorMessage());
            }
        }
        
        // 重新统计所有结果（之前在循环中已经统计了前5个）
        successCount = 0;
        failureCount = 0;
        for (TranspilationResult result : results) {
            if (result.isSuccess()) {
                successCount++;
            } else {
                failureCount++;
            }
        }
        
        System.out.println("\n" + dialectName + " " + statementType + "转换统计:");
        System.out.println("总语句数: " + statements.size());
        System.out.println("成功: " + successCount + ", 失败: " + failureCount);
        System.out.println("成功率: " + String.format("%.1f", (double) successCount / statements.size() * 100) + "%");
        
        // 验证至少有一些成功的转换
        assertTrue(successCount > 0, dialectName + "应该有一些成功的转换");
    }
}
