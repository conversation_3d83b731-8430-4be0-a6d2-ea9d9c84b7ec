package com.xylink.sqltranspiler.integration.conversion;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilerStatistics;

/**
 * SQL转换器集成测试
 * 
 * 严格遵循官方文档：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 * 
 * 根据rule-db.md规范：
 * - 所有转换规则基于官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. 转换管道集成测试
 * 2. 批量转换性能测试
 * 3. 统计信息验证测试
 * 4. 错误处理集成测试
 * 5. 配置影响测试
 */
@DisplayName("SQL转换器集成测试")
public class TranspilerIntegrationTest {

    private SqlTranspiler damengTranspiler;
    private SqlTranspiler kingbaseTranspiler;
    private SqlTranspiler shentongTranspiler;

    @BeforeEach
    void setUp() {
        // 创建达梦转换器
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(30))
            .build();
        
        // 创建金仓转换器
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(30))
            .build();
        
        // 创建神通转换器
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(30))
            .build();
    }

    @Nested
    @DisplayName("转换管道集成测试")
    class TranspilationPipelineTests {
        
        @Test
        @DisplayName("测试完整的DDL转换管道")
        void testCompleteDdlTranspilationPipeline() {
            // 根据官方文档，测试完整的DDL转换管道
            List<String> ddlStatements = Arrays.asList(
                "CREATE TABLE users (" +
                "  id INT AUTO_INCREMENT PRIMARY KEY," +
                "  username VARCHAR(50) NOT NULL UNIQUE," +
                "  email VARCHAR(100) NOT NULL," +
                "  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                ")",
                
                "CREATE TABLE orders (" +
                "  id INT AUTO_INCREMENT PRIMARY KEY," +
                "  user_id INT NOT NULL," +
                "  order_date DATE NOT NULL," +
                "  total_amount DECIMAL(10,2) NOT NULL," +
                "  status VARCHAR(20) DEFAULT 'pending'," +
                "  FOREIGN KEY (user_id) REFERENCES users(id)" +
                ")",
                
                "CREATE INDEX idx_users_email ON users(email)",
                "CREATE INDEX idx_orders_user_id ON orders(user_id)",
                "CREATE INDEX idx_orders_date ON orders(order_date)"
            );

            // 测试达梦转换管道
            testTranspilationPipeline(damengTranspiler, ddlStatements, "达梦");
            
            // 测试金仓转换管道
            testTranspilationPipeline(kingbaseTranspiler, ddlStatements, "金仓");
            
            // 测试神通转换管道
            testTranspilationPipeline(shentongTranspiler, ddlStatements, "神通");
        }

        @Test
        @DisplayName("测试完整的DML转换管道")
        void testCompleteDmlTranspilationPipeline() {
            // 根据官方文档，测试完整的DML转换管道
            List<String> dmlStatements = Arrays.asList(
                "INSERT INTO users (username, email) VALUES ('john_doe', '<EMAIL>')",
                "INSERT INTO users (username, email) VALUES ('jane_smith', '<EMAIL>')",
                
                "INSERT INTO orders (user_id, order_date, total_amount, status) " +
                "VALUES (1, '2024-01-15', 99.99, 'completed')",
                
                "UPDATE users SET email = '<EMAIL>' WHERE username = 'john_doe'",
                
                "SELECT u.username, u.email, COUNT(o.id) as order_count " +
                "FROM users u LEFT JOIN orders o ON u.id = o.user_id " +
                "GROUP BY u.id, u.username, u.email " +
                "ORDER BY order_count DESC " +
                "LIMIT 10",
                
                "DELETE FROM orders WHERE status = 'cancelled' AND order_date < '2024-01-01'"
            );

            // 测试达梦转换管道
            testTranspilationPipeline(damengTranspiler, dmlStatements, "达梦");
            
            // 测试金仓转换管道
            testTranspilationPipeline(kingbaseTranspiler, dmlStatements, "金仓");
            
            // 测试神通转换管道
            testTranspilationPipeline(shentongTranspiler, dmlStatements, "神通");
        }

        private void testTranspilationPipeline(SqlTranspiler transpiler, List<String> statements, String dialectName) {
            System.out.println("\n=== " + dialectName + "转换管道测试 ===");
            
            List<TranspilationResult> results = transpiler.transpileAll(statements);
            
            // 验证结果数量
            assertEquals(statements.size(), results.size(), 
                        dialectName + "转换结果数量应与输入语句数量一致");
            
            // 验证每个转换结果
            for (int i = 0; i < results.size(); i++) {
                TranspilationResult result = results.get(i);
                String originalSql = statements.get(i);
                
                System.out.println("\n语句 " + (i + 1) + ":");
                System.out.println("输入: " + originalSql);
                System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
                
                // 基本验证
                assertNotNull(result, dialectName + "转换结果不应为空");
                assertEquals(originalSql, result.getOriginalSql(), "原始SQL应正确保存");
                
                if (result.isSuccess()) {
                    assertNotNull(result.getTargetSql(), dialectName + "转换成功时目标SQL不应为空");
                    assertFalse(result.getTargetSql().trim().isEmpty(), dialectName + "目标SQL不应为空字符串");
                } else {
                    assertNotNull(result.getErrorMessage(), dialectName + "转换失败时错误消息不应为空");
                    System.out.println("转换失败: " + result.getErrorMessage());
                }
            }
            
            // 验证统计信息
            TranspilerStatistics stats = transpiler.getStatistics();
            assertNotNull(stats, dialectName + "统计信息不应为空");
            assertTrue(stats.getTotalTranspilations() >= statements.size(), 
                      dialectName + "总转换次数应大于等于语句数量");
            
            System.out.println("\n" + dialectName + "统计信息: " + stats);
        }
    }

    @Nested
    @DisplayName("批量转换性能测试")
    class BatchTranspilationPerformanceTests {
        
        @Test
        @DisplayName("测试大批量转换性能")
        void testLargeBatchTranspilationPerformance() {
            // 生成大量测试SQL语句
            List<String> largeBatch = generateLargeBatchSql(100);
            
            // 测试达梦批量转换性能
            testBatchPerformance(damengTranspiler, largeBatch, "达梦");
            
            // 测试金仓批量转换性能
            testBatchPerformance(kingbaseTranspiler, largeBatch, "金仓");
            
            // 测试神通批量转换性能
            testBatchPerformance(shentongTranspiler, largeBatch, "神通");
        }

        private List<String> generateLargeBatchSql(int count) {
            List<String> sqls = Arrays.asList(
                "SELECT * FROM users WHERE id = %d",
                "INSERT INTO logs (user_id, action, timestamp) VALUES (%d, 'login', NOW())",
                "UPDATE users SET last_login = NOW() WHERE id = %d",
                "SELECT COUNT(*) FROM orders WHERE user_id = %d",
                "DELETE FROM temp_data WHERE id = %d"
            );
            
            return sqls.stream()
                .flatMap(template -> 
                    java.util.stream.IntStream.range(1, count / sqls.size() + 1)
                        .mapToObj(i -> String.format(template, i))
                )
                .limit(count)
                .collect(java.util.stream.Collectors.toList());
        }

        private void testBatchPerformance(SqlTranspiler transpiler, List<String> statements, String dialectName) {
            System.out.println("\n=== " + dialectName + "批量转换性能测试 ===");
            
            long startTime = System.currentTimeMillis();
            List<TranspilationResult> results = transpiler.transpileAll(statements);
            long endTime = System.currentTimeMillis();
            
            long duration = endTime - startTime;
            double avgTimePerStatement = (double) duration / statements.size();
            
            System.out.println("批量大小: " + statements.size());
            System.out.println("总耗时: " + duration + "ms");
            System.out.println("平均每条语句耗时: " + String.format("%.2f", avgTimePerStatement) + "ms");
            
            // 验证结果
            assertEquals(statements.size(), results.size(), 
                        dialectName + "批量转换结果数量应与输入一致");
            
            // 验证性能要求（每条语句平均不超过100ms）
            assertTrue(avgTimePerStatement < 100, 
                      dialectName + "平均转换时间应小于100ms，实际: " + avgTimePerStatement + "ms");
            
            // 验证统计信息
            TranspilerStatistics stats = transpiler.getStatistics();
            assertNotNull(stats, dialectName + "统计信息不应为空");
            assertTrue(stats.getTotalTranspilations() >= statements.size(), 
                      dialectName + "总转换次数应大于等于批量大小");
            
            System.out.println(dialectName + "统计信息: " + stats);
        }
    }

    @Nested
    @DisplayName("错误处理集成测试")
    class ErrorHandlingIntegrationTests {
        
        @Test
        @DisplayName("测试混合有效和无效SQL的批量转换")
        void testMixedValidInvalidSqlBatch() {
            List<String> mixedStatements = Arrays.asList(
                "SELECT * FROM users",  // 有效
                "INVALID SQL STATEMENT",  // 无效
                "CREATE TABLE test (id INT)",  // 有效
                "",  // 空SQL
                "INSERT INTO test VALUES (1)",  // 有效
                "SELECT * FROM non_existent_function()",  // 可能无效
                "UPDATE test SET id = 2 WHERE id = 1"  // 有效
            );
            
            // 测试达梦错误处理
            testErrorHandling(damengTranspiler, mixedStatements, "达梦");
            
            // 测试金仓错误处理
            testErrorHandling(kingbaseTranspiler, mixedStatements, "金仓");
            
            // 测试神通错误处理
            testErrorHandling(shentongTranspiler, mixedStatements, "神通");
        }

        private void testErrorHandling(SqlTranspiler transpiler, List<String> statements, String dialectName) {
            System.out.println("\n=== " + dialectName + "错误处理集成测试 ===");
            
            List<TranspilationResult> results = transpiler.transpileAll(statements);
            
            // 验证结果数量
            assertEquals(statements.size(), results.size(), 
                        dialectName + "错误处理时结果数量应与输入一致");
            
            int successCount = 0;
            int failureCount = 0;
            
            for (int i = 0; i < results.size(); i++) {
                TranspilationResult result = results.get(i);
                String originalSql = statements.get(i);
                
                System.out.println("\n语句 " + (i + 1) + ": " + originalSql);
                System.out.println("结果: " + (result.isSuccess() ? "成功" : "失败"));
                
                if (result.isSuccess()) {
                    successCount++;
                    System.out.println("输出: " + result.getTargetSql());
                } else {
                    failureCount++;
                    System.out.println("错误: " + result.getErrorMessage());
                }
                
                // 验证结果一致性
                assertNotNull(result, dialectName + "转换结果不应为空");
                assertEquals(originalSql, result.getOriginalSql(), "原始SQL应正确保存");
                
                if (result.isSuccess()) {
                    assertNotNull(result.getTargetSql(), "成功转换应有目标SQL");
                    assertNull(result.getErrorMessage(), "成功转换不应有错误消息");
                } else {
                    assertNull(result.getTargetSql(), "失败转换不应有目标SQL");
                    assertNotNull(result.getErrorMessage(), "失败转换应有错误消息");
                }
            }
            
            System.out.println("\n" + dialectName + "错误处理统计:");
            System.out.println("成功: " + successCount + ", 失败: " + failureCount);
            
            // 验证至少有一些成功的转换
            assertTrue(successCount > 0, dialectName + "应该有一些成功的转换");
            
            // 验证统计信息
            TranspilerStatistics stats = transpiler.getStatistics();
            assertNotNull(stats, dialectName + "统计信息不应为空");
            // 注意：统计信息是累积的，包含之前的转换
            // 我们只验证统计信息不为空，不验证具体数值，因为统计是累积的
            assertTrue(stats.getTotalTranspilations() > 0,
                      dialectName + "总转换次数应大于0");
        }
    }
}
