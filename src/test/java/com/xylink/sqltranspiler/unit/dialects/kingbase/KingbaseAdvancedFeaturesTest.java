package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.*;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * 金仓数据库高级特性单元测试
 * 
 * 严格遵循金仓官方文档：
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 金仓SQL参考手册：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * 
 * 根据rule-db.md规范：
 * - 所有转换规则基于金仓官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. PostgreSQL兼容性高级特性
 * 2. MySQL兼容模式特性
 * 3. 数组和JSON数据类型
 * 4. 窗口函数和CTE
 * 5. 存储过程和函数
 * 6. 分区表特性
 */
@DisplayName("金仓数据库高级特性单元测试")
public class KingbaseAdvancedFeaturesTest {

    private SqlTranspiler kingbaseTranspiler;

    @BeforeEach
    void setUp() {
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("PostgreSQL兼容性高级特性测试")
    class PostgreSQLCompatibilityAdvancedTests {
        
        @Test
        @DisplayName("测试数组数据类型转换")
        void testArrayDataTypeConversion() {
            // 根据金仓官方文档，金仓支持PostgreSQL兼容的数组类型
            String mysqlSql = "CREATE TABLE array_test (" +
                    "id INT PRIMARY KEY, " +
                    "tags TEXT, " +  // MySQL中可能用TEXT存储逗号分隔的值
                    "numbers TEXT" +  // MySQL中可能用TEXT存储数字列表
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("数组数据类型转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证数组类型转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("array_test"), "应保留表名");
            // 根据金仓官方文档，TEXT类型与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        }

        @Test
        @DisplayName("测试JSON数据类型转换")
        void testJsonDataTypeConversion() {
            // 根据金仓官方文档，金仓支持JSON数据类型
            String mysqlSql = "CREATE TABLE json_test (" +
                    "id INT PRIMARY KEY, " +
                    "data JSON, " +
                    "metadata JSON" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("JSON数据类型转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证JSON类型转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("json_test"), "应保留表名");
            // 根据金仓官方文档，JSON类型与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("JSON") || kingbaseSql.contains("JSONB"), 
                      "应保留JSON类型或转换为JSONB");
        }

        @Test
        @DisplayName("测试窗口函数转换")
        void testWindowFunctionConversion() {
            // 根据金仓官方文档，金仓支持PostgreSQL兼容的窗口函数
            String mysqlSql = "SELECT " +
                    "name, " +
                    "salary, " +
                    "ROW_NUMBER() OVER (ORDER BY salary DESC) as rank, " +
                    "LAG(salary) OVER (ORDER BY salary DESC) as prev_salary " +
                    "FROM employees";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("窗口函数转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证窗口函数转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
            assertTrue(kingbaseSql.contains("ROW_NUMBER()"), "应保留ROW_NUMBER函数");
            assertTrue(kingbaseSql.contains("OVER"), "应保留OVER子句");
            assertTrue(kingbaseSql.contains("LAG"), "应保留LAG函数");
        }

        @Test
        @DisplayName("测试CTE转换")
        void testCteConversion() {
            // 根据金仓官方文档，金仓支持PostgreSQL兼容的CTE
            String mysqlSql = "WITH sales_summary AS (" +
                    "  SELECT department, SUM(amount) as total_sales " +
                    "  FROM sales " +
                    "  GROUP BY department" +
                    ") " +
                    "SELECT * FROM sales_summary WHERE total_sales > 10000";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("CTE转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证CTE转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("WITH"), "应保留WITH关键字");
            assertTrue(kingbaseSql.contains("sales_summary"), "应保留CTE名称");
            assertTrue(kingbaseSql.contains("AS"), "应保留AS关键字");
        }
    }

    @Nested
    @DisplayName("MySQL兼容模式特性测试")
    class MySQLCompatibilityModeTests {
        
        @Test
        @DisplayName("测试MySQL函数转换")
        void testMySQLFunctionConversion() {
            // 根据金仓官方文档，金仓在MySQL兼容模式下支持MySQL函数
            String mysqlSql = "SELECT " +
                    "CONCAT(first_name, ' ', last_name) as full_name, " +
                    "DATE_FORMAT(created_at, '%Y-%m-%d') as formatted_date, " +
                    "IFNULL(phone, 'N/A') as phone_display " +
                    "FROM users";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("MySQL函数转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证MySQL函数转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
            // 根据金仓官方文档，CONCAT函数与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("CONCAT") || kingbaseSql.contains("||"), 
                      "应保留CONCAT函数或转换为||操作符");
            // 根据金仓官方文档，可能需要转换MySQL特有函数
            assertTrue(kingbaseSql.contains("DATE_FORMAT") || kingbaseSql.contains("TO_CHAR"), 
                      "应保留DATE_FORMAT或转换为TO_CHAR");
        }

        @Test
        @DisplayName("测试MySQL特有语法转换")
        void testMySQLSpecificSyntaxConversion() {
            // 根据金仓官方文档，测试MySQL特有语法的转换
            String mysqlSql = "SELECT * FROM users " +
                    "WHERE name REGEXP '^[A-Z]' " +
                    "ORDER BY RAND() " +
                    "LIMIT 10";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("MySQL特有语法转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证MySQL特有语法转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
            // 根据金仓官方文档，REGEXP可能转换为PostgreSQL的正则表达式操作符
            assertTrue(kingbaseSql.contains("REGEXP") || kingbaseSql.contains("~"), 
                      "应保留REGEXP或转换为~操作符");
            // 根据金仓官方文档，RAND()可能转换为RANDOM()
            assertTrue(kingbaseSql.contains("RAND") || kingbaseSql.contains("RANDOM"), 
                      "应保留RAND或转换为RANDOM");
            assertTrue(kingbaseSql.contains("LIMIT"), "应保留LIMIT");
        }
    }

    @Nested
    @DisplayName("分区表特性测试")
    class PartitioningTests {
        
        @Test
        @DisplayName("测试分区表转换")
        void testPartitionTableConversion() {
            // 根据金仓官方文档，金仓支持PostgreSQL兼容的分区表
            String mysqlSql = "CREATE TABLE orders_partitioned (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "order_date DATE NOT NULL, " +
                    "customer_id INT, " +
                    "amount DECIMAL(10,2)" +
                    ") PARTITION BY RANGE (YEAR(order_date)) (" +
                    "PARTITION p2020 VALUES LESS THAN (2021), " +
                    "PARTITION p2021 VALUES LESS THAN (2022), " +
                    "PARTITION p2022 VALUES LESS THAN (2023)" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("分区表转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证分区表转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("orders_partitioned"), "应保留表名");
            // 根据金仓官方文档，分区语法可能需要转换为PostgreSQL格式
            assertTrue(kingbaseSql.contains("PARTITION") || kingbaseSql.contains("RANGE"), 
                      "应处理分区相关语法");
        }
    }

    @Nested
    @DisplayName("存储过程和函数测试")
    class StoredProcedureAndFunctionTests {
        
        @Test
        @DisplayName("测试存储过程转换")
        void testStoredProcedureConversion() {
            // 根据金仓官方文档，金仓支持PostgreSQL兼容的存储过程
            String mysqlSql = "CREATE PROCEDURE GetUserOrders(IN user_id INT) " +
                    "BEGIN " +
                    "  SELECT * FROM orders WHERE customer_id = user_id; " +
                    "END";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("存储过程转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证存储过程转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            // 根据金仓官方文档，存储过程语法可能需要转换为PostgreSQL格式
            assertTrue(kingbaseSql.contains("CREATE") && 
                      (kingbaseSql.contains("PROCEDURE") || kingbaseSql.contains("FUNCTION")), 
                      "应处理存储过程创建语法");
        }

        @Test
        @DisplayName("测试用户定义函数转换")
        void testUserDefinedFunctionConversion() {
            // 根据金仓官方文档，金仓支持PostgreSQL兼容的用户定义函数
            String mysqlSql = "CREATE FUNCTION CalculateDiscount(amount DECIMAL(10,2)) " +
                    "RETURNS DECIMAL(10,2) " +
                    "DETERMINISTIC " +
                    "BEGIN " +
                    "  RETURN amount * 0.1; " +
                    "END";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("用户定义函数转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证用户定义函数转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            // 根据金仓官方文档，函数语法可能需要转换为PostgreSQL格式
            assertTrue(kingbaseSql.contains("CREATE FUNCTION"), "应保留CREATE FUNCTION");
            assertTrue(kingbaseSql.contains("RETURNS"), "应保留RETURNS");
        }
    }

    @Nested
    @DisplayName("事务和并发控制测试")
    class TransactionAndConcurrencyTests {
        
        @Test
        @DisplayName("测试事务隔离级别转换")
        void testTransactionIsolationLevelConversion() {
            // 根据金仓官方文档，金仓支持PostgreSQL兼容的事务隔离级别
            String mysqlSql = "SET TRANSACTION ISOLATION LEVEL READ COMMITTED";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("事务隔离级别转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证事务隔离级别转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            // 根据金仓官方文档，事务隔离级别语法与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("SET TRANSACTION"), "应保留SET TRANSACTION");
            assertTrue(kingbaseSql.contains("ISOLATION LEVEL"), "应保留ISOLATION LEVEL");
            assertTrue(kingbaseSql.contains("READ COMMITTED"), "应保留READ COMMITTED");
        }
    }
}
