package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * 金仓数据库核心语句单元测试
 * 
 * 严格遵循金仓官方文档：
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 金仓SQL参考手册：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
 * 
 * 根据rule-db.md规范：
 * - 所有转换规则基于金仓官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. CREATE TABLE语句的核心功能
 * 2. 数据类型转换的核心支持
 * 3. 约束定义的核心转换
 * 4. 索引定义的核心转换
 * 5. DML语句的核心转换
 * 6. PostgreSQL兼容性特性
 */
@DisplayName("金仓数据库核心语句单元测试")
public class KingbaseCoreStatementsTest {

    private SqlTranspiler kingbaseTranspiler;

    @BeforeEach
    void setUp() {
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("CREATE TABLE语句测试")
    class CreateTableTests {
        
        @Test
        @DisplayName("测试基础CREATE TABLE语句转换")
        void testBasicCreateTableStatement() {
            // 根据金仓官方文档，CREATE TABLE语句基本语法与PostgreSQL兼容
            String mysqlSql = "CREATE TABLE basic_table (" +
                    "id INT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "age INT" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("基础CREATE TABLE语句转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证基础CREATE TABLE语句转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE关键字");
            assertTrue(kingbaseSql.contains("basic_table"), "应保留表名");
            assertTrue(kingbaseSql.contains("id"), "应保留id列");
            assertTrue(kingbaseSql.contains("name"), "应保留name列");
            assertTrue(kingbaseSql.contains("age"), "应保留age列");
            assertTrue(kingbaseSql.contains("INT") || kingbaseSql.contains("INTEGER"), "应保留或转换INT数据类型");
            assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR数据类型");
            assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键约束");
            assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        }

        @Test
        @DisplayName("测试AUTO_INCREMENT转换")
        void testAutoIncrementConversion() {
            // 根据金仓官方文档，AUTO_INCREMENT应转换为SERIAL或IDENTITY
            String mysqlSql = "CREATE TABLE auto_increment_test (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("AUTO_INCREMENT转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，AUTO_INCREMENT应转换为SERIAL或IDENTITY
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("auto_increment_test"), "应保留表名");
            // 根据金仓官方文档，AUTO_INCREMENT转换为SERIAL或IDENTITY
            assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("IDENTITY") || 
                      kingbaseSql.contains("AUTO_INCREMENT"), 
                      "应转换AUTO_INCREMENT为SERIAL、IDENTITY或保留");
            assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键约束");
            assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        }

        @Test
        @DisplayName("测试复合主键转换")
        void testCompositeKeyConversion() {
            // 根据金仓官方文档，复合主键语法与PostgreSQL兼容
            String mysqlSql = "CREATE TABLE composite_key (" +
                    "user_id INT NOT NULL, " +
                    "role_id INT NOT NULL, " +
                    "assigned_date DATE NOT NULL, " +
                    "status VARCHAR(20) DEFAULT 'active', " +
                    "PRIMARY KEY (user_id, role_id)" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("复合主键转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证复合主键转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("composite_key"), "应保留表名");
            assertTrue(kingbaseSql.contains("user_id"), "应保留user_id列");
            assertTrue(kingbaseSql.contains("role_id"), "应保留role_id列");
            assertTrue(kingbaseSql.contains("assigned_date"), "应保留assigned_date列");
            assertTrue(kingbaseSql.contains("status"), "应保留status列");
            assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
            assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留复合主键");
            assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
            assertTrue(kingbaseSql.contains("'active'"), "应保留字符串默认值");
        }
    }

    @Nested
    @DisplayName("数据类型转换测试")
    class DataTypeConversionTests {
        
        @Test
        @DisplayName("测试核心数据类型转换")
        void testCoreDataTypeConversion() {
            // 根据金仓官方文档，测试核心数据类型转换
            String mysqlSql = "CREATE TABLE core_types (" +
                    "int_col INT, " +
                    "varchar_col VARCHAR(255), " +
                    "text_col TEXT, " +
                    "decimal_col DECIMAL(10,2), " +
                    "date_col DATE, " +
                    "timestamp_col TIMESTAMP" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("核心数据类型转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证核心数据类型转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("core_types"), "应保留表名");
            assertTrue(kingbaseSql.contains("INT") || kingbaseSql.contains("INTEGER"), "应保留或转换INT类型");
            assertTrue(kingbaseSql.contains("VARCHAR(255)"), "应保留VARCHAR类型");
            // 根据金仓官方文档，TEXT类型与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
            assertTrue(kingbaseSql.contains("DECIMAL(10,2)") || kingbaseSql.contains("NUMERIC(10,2)"), 
                      "应保留DECIMAL或转换为NUMERIC类型");
            assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
            assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        }

        @Test
        @DisplayName("测试时间戳和默认值转换")
        void testTimestampAndDefaultValueConversion() {
            // 根据金仓官方文档，测试时间戳和默认值转换
            String mysqlSql = "CREATE TABLE timestamp_defaults (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "status VARCHAR(20) DEFAULT 'pending', " +
                    "count_value INT DEFAULT 0" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("时间戳和默认值转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证时间戳和默认值转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("timestamp_defaults"), "应保留表名");
            assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("IDENTITY") || 
                      kingbaseSql.contains("AUTO_INCREMENT"), 
                      "应转换AUTO_INCREMENT为SERIAL、IDENTITY或保留");
            assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
            assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
            assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
            
            // 根据金仓官方文档，CURRENT_TIMESTAMP与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP") || kingbaseSql.contains("NOW()"), 
                      "应保留CURRENT_TIMESTAMP或转换为NOW()");
            assertTrue(kingbaseSql.contains("'pending'"), "应保留字符串默认值");
            assertTrue(kingbaseSql.contains("DEFAULT 0"), "应保留数值默认值");
        }
    }

    @Nested
    @DisplayName("DML语句转换测试")
    class DmlStatementTests {
        
        @Test
        @DisplayName("测试LIMIT语句转换")
        void testLimitStatementConversion() {
            // 根据金仓官方文档，LIMIT语法与PostgreSQL兼容
            String mysqlSql = "SELECT * FROM users WHERE age > 18 LIMIT 10";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("LIMIT语句转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证LIMIT转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
            assertTrue(kingbaseSql.contains("users"), "应保留表名");
            assertTrue(kingbaseSql.contains("age"), "应保留列名");
            // 根据金仓官方文档，LIMIT语法与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("LIMIT"), "应保留LIMIT语法");
        }

        @Test
        @DisplayName("测试INSERT语句转换")
        void testInsertStatementConversion() {
            // 根据金仓官方文档，INSERT语句基本语法与PostgreSQL兼容
            String mysqlSql = "INSERT INTO users (name, email, age) VALUES ('John', '<EMAIL>', 25)";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("INSERT语句转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证INSERT语句转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("INSERT INTO"), "应保留INSERT INTO");
            assertTrue(kingbaseSql.contains("users"), "应保留表名");
            assertTrue(kingbaseSql.contains("name"), "应保留列名");
            assertTrue(kingbaseSql.contains("email"), "应保留列名");
            assertTrue(kingbaseSql.contains("age"), "应保留列名");
            assertTrue(kingbaseSql.contains("VALUES"), "应保留VALUES");
            assertTrue(kingbaseSql.contains("'John'"), "应保留字符串值");
            assertTrue(kingbaseSql.contains("'<EMAIL>'"), "应保留邮箱值");
            assertTrue(kingbaseSql.contains("25"), "应保留数值");
        }
    }

    @Nested
    @DisplayName("PostgreSQL兼容性测试")
    class PostgreSQLCompatibilityTests {
        
        @Test
        @DisplayName("测试PostgreSQL兼容模式")
        void testPostgreSQLCompatibilityMode() {
            // 根据金仓官方文档，金仓数据库支持PostgreSQL兼容模式
            String mysqlSql = "SELECT * FROM users ORDER BY id LIMIT 10 OFFSET 5";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("PostgreSQL兼容模式转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证PostgreSQL兼容性
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
            assertTrue(kingbaseSql.contains("users"), "应保留表名");
            assertTrue(kingbaseSql.contains("ORDER BY"), "应保留ORDER BY");
            assertTrue(kingbaseSql.contains("LIMIT"), "应保留LIMIT");
            // 根据金仓官方文档，OFFSET语法与PostgreSQL兼容
            assertTrue(kingbaseSql.contains("OFFSET"), "应保留OFFSET语法");
        }
    }

    @Nested
    @DisplayName("约束转换测试")
    class ConstraintConversionTests {

        @Test
        @DisplayName("测试核心约束转换")
        void testCoreConstraintConversion() {
            // 根据金仓官方文档，测试核心约束转换
            String mysqlSql = "CREATE TABLE core_constraints (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "email VARCHAR(100) NOT NULL UNIQUE, " +
                    "age INT CHECK (age >= 0), " +
                    "status VARCHAR(20) DEFAULT 'active'" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("核心约束转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证核心约束转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("core_constraints"), "应保留表名");
            assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("IDENTITY") ||
                      kingbaseSql.contains("AUTO_INCREMENT"),
                      "应转换AUTO_INCREMENT为SERIAL、IDENTITY或保留");
            assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键约束");
            assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
            assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
            assertTrue(kingbaseSql.contains("CHECK") || kingbaseSql.contains("age"),
                      "应处理CHECK约束");
            assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
            assertTrue(kingbaseSql.contains("'active'"), "应保留字符串默认值");
        }

        @Test
        @DisplayName("测试外键约束转换")
        void testForeignKeyConstraintConversion() {
            // 根据金仓官方文档，外键约束语法与PostgreSQL兼容
            String mysqlSql = "CREATE TABLE orders (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "user_id INT NOT NULL, " +
                    "total DECIMAL(10,2), " +
                    "FOREIGN KEY (user_id) REFERENCES users(id)" +
                    ")";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("外键约束转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证外键约束转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(kingbaseSql.contains("orders"), "应保留表名");
            assertTrue(kingbaseSql.contains("FOREIGN KEY"), "应保留外键约束");
            assertTrue(kingbaseSql.contains("REFERENCES"), "应保留引用关系");
            assertTrue(kingbaseSql.contains("users(id)"), "应保留引用表和列");
        }
    }

    @Nested
    @DisplayName("索引转换测试")
    class IndexConversionTests {

        @Test
        @DisplayName("测试基础索引转换")
        void testBasicIndexConversion() {
            // 根据金仓官方文档，CREATE INDEX语法与PostgreSQL兼容
            String mysqlSql = "CREATE INDEX idx_users_email ON users(email)";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("基础索引转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证索引转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE INDEX"), "应保留CREATE INDEX");
            assertTrue(kingbaseSql.contains("idx_users_email"), "应保留索引名");
            assertTrue(kingbaseSql.contains("users"), "应保留表名");
            assertTrue(kingbaseSql.contains("email"), "应保留列名");
        }

        @Test
        @DisplayName("测试复合索引转换")
        void testCompositeIndexConversion() {
            // 根据金仓官方文档，复合索引语法与PostgreSQL兼容
            String mysqlSql = "CREATE INDEX idx_orders_user_date ON orders(user_id, order_date)";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("复合索引转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证复合索引转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("CREATE INDEX"), "应保留CREATE INDEX");
            assertTrue(kingbaseSql.contains("idx_orders_user_date"), "应保留索引名");
            assertTrue(kingbaseSql.contains("orders"), "应保留表名");
            assertTrue(kingbaseSql.contains("user_id"), "应保留第一个列名");
            assertTrue(kingbaseSql.contains("order_date"), "应保留第二个列名");
        }
    }

    @Nested
    @DisplayName("MySQL兼容性测试")
    class MySQLCompatibilityTests {

        @Test
        @DisplayName("测试MySQL兼容模式")
        void testMySQLCompatibilityMode() {
            // 根据金仓官方文档，金仓数据库支持MySQL兼容模式
            String mysqlSql = "SELECT * FROM users WHERE name LIKE '%john%' ORDER BY id DESC LIMIT 5";

            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);

            System.out.println("MySQL兼容模式转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据金仓官方文档，验证MySQL兼容性
            assertTrue(result.isSuccess(), "转换应该成功");
            String kingbaseSql = result.getTargetSql();
            assertNotNull(kingbaseSql, "转换结果不应为空");
            assertTrue(kingbaseSql.contains("SELECT"), "应保留SELECT");
            assertTrue(kingbaseSql.contains("users"), "应保留表名");
            assertTrue(kingbaseSql.contains("LIKE"), "应保留LIKE操作符");
            assertTrue(kingbaseSql.contains("ORDER BY"), "应保留ORDER BY");
            assertTrue(kingbaseSql.contains("LIMIT"), "应保留LIMIT语法");
        }
    }
}
