package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * 神通数据库核心语句单元测试
 * 
 * 严格遵循神通官方文档：
 * - 神通官方文档：参考项目内@shentong.md文档
 * - 神通数据库SQL参考手册
 * 
 * 根据rule-db.md规范：
 * - 所有转换规则基于神通官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. CREATE TABLE语句的核心功能
 * 2. 数据类型转换的核心支持
 * 3. 约束定义的核心转换
 * 4. 索引定义的核心转换
 * 5. DML语句的核心转换
 * 6. Oracle兼容性特性
 */
@DisplayName("神通数据库核心语句单元测试")
public class ShentongCoreStatementsTest {

    private SqlTranspiler shentongTranspiler;

    @BeforeEach
    void setUp() {
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("CREATE TABLE语句测试")
    class CreateTableTests {
        
        @Test
        @DisplayName("测试基础CREATE TABLE语句转换")
        void testBasicCreateTableStatement() {
            // 根据神通官方文档，CREATE TABLE语句基本语法与Oracle兼容
            String mysqlSql = "CREATE TABLE basic_table (" +
                    "id INT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "age INT" +
                    ")";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("基础CREATE TABLE语句转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证基础CREATE TABLE语句转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE关键字");
            assertTrue(shentongSql.contains("basic_table"), "应保留表名");
            assertTrue(shentongSql.contains("id"), "应保留id列");
            assertTrue(shentongSql.contains("name"), "应保留name列");
            assertTrue(shentongSql.contains("age"), "应保留age列");
            assertTrue(shentongSql.contains("INT") || shentongSql.contains("NUMBER"), "应保留INT或转换为NUMBER数据类型");
            assertTrue(shentongSql.contains("VARCHAR(100)") || shentongSql.contains("VARCHAR2(100)"), 
                      "应保留VARCHAR或转换为VARCHAR2数据类型");
            assertTrue(shentongSql.contains("PRIMARY KEY"), "应保留主键约束");
            assertTrue(shentongSql.contains("NOT NULL"), "应保留NOT NULL约束");
        }

        @Test
        @DisplayName("测试AUTO_INCREMENT转换")
        void testAutoIncrementConversion() {
            // 根据神通官方文档，AUTO_INCREMENT应转换为序列和触发器
            String mysqlSql = "CREATE TABLE auto_increment_test (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL" +
                    ")";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("AUTO_INCREMENT转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，AUTO_INCREMENT应转换为序列和触发器或保留
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(shentongSql.contains("auto_increment_test"), "应保留表名");
            // 根据神通官方文档，AUTO_INCREMENT可能转换为序列或保留
            assertTrue(shentongSql.contains("SEQUENCE") || shentongSql.contains("AUTO_INCREMENT") || 
                      shentongSql.contains("IDENTITY"), 
                      "应转换AUTO_INCREMENT为SEQUENCE、保留或转换为IDENTITY");
            assertTrue(shentongSql.contains("PRIMARY KEY"), "应保留主键约束");
            assertTrue(shentongSql.contains("NOT NULL"), "应保留NOT NULL约束");
        }

        @Test
        @DisplayName("测试复合主键转换")
        void testCompositeKeyConversion() {
            // 根据神通官方文档，复合主键语法与Oracle兼容
            String mysqlSql = "CREATE TABLE composite_key (" +
                    "user_id INT NOT NULL, " +
                    "role_id INT NOT NULL, " +
                    "assigned_date DATE NOT NULL, " +
                    "status VARCHAR(20) DEFAULT 'active', " +
                    "PRIMARY KEY (user_id, role_id)" +
                    ")";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("复合主键转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证复合主键转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(shentongSql.contains("composite_key"), "应保留表名");
            assertTrue(shentongSql.contains("user_id"), "应保留user_id列");
            assertTrue(shentongSql.contains("role_id"), "应保留role_id列");
            assertTrue(shentongSql.contains("assigned_date"), "应保留assigned_date列");
            assertTrue(shentongSql.contains("status"), "应保留status列");
            assertTrue(shentongSql.contains("NOT NULL"), "应保留NOT NULL约束");
            assertTrue(shentongSql.contains("PRIMARY KEY"), "应保留复合主键");
            assertTrue(shentongSql.contains("DEFAULT"), "应保留默认值");
            assertTrue(shentongSql.contains("'active'"), "应保留字符串默认值");
        }
    }

    @Nested
    @DisplayName("数据类型转换测试")
    class DataTypeConversionTests {
        
        @Test
        @DisplayName("测试核心数据类型转换")
        void testCoreDataTypeConversion() {
            // 根据神通官方文档，测试核心数据类型转换
            String mysqlSql = "CREATE TABLE core_types (" +
                    "int_col INT, " +
                    "varchar_col VARCHAR(255), " +
                    "text_col TEXT, " +
                    "decimal_col DECIMAL(10,2), " +
                    "date_col DATE, " +
                    "timestamp_col TIMESTAMP" +
                    ")";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("核心数据类型转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证核心数据类型转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(shentongSql.contains("core_types"), "应保留表名");
            assertTrue(shentongSql.contains("INT") || shentongSql.contains("NUMBER"), "应保留INT或转换为NUMBER类型");
            assertTrue(shentongSql.contains("VARCHAR(255)") || shentongSql.contains("VARCHAR2(255)"), 
                      "应保留VARCHAR或转换为VARCHAR2类型");
            // 根据神通官方文档，TEXT可能转换为CLOB
            assertTrue(shentongSql.contains("CLOB") || shentongSql.contains("TEXT"), 
                      "应转换TEXT为CLOB或保留TEXT");
            assertTrue(shentongSql.contains("DECIMAL(10,2)") || shentongSql.contains("NUMBER(10,2)"), 
                      "应保留DECIMAL或转换为NUMBER类型");
            assertTrue(shentongSql.contains("DATE"), "应保留DATE类型");
            assertTrue(shentongSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        }

        @Test
        @DisplayName("测试时间戳和默认值转换")
        void testTimestampAndDefaultValueConversion() {
            // 根据神通官方文档，测试时间戳和默认值转换
            String mysqlSql = "CREATE TABLE timestamp_defaults (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "status VARCHAR(20) DEFAULT 'pending', " +
                    "count_value INT DEFAULT 0" +
                    ")";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("时间戳和默认值转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证时间戳和默认值转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(shentongSql.contains("timestamp_defaults"), "应保留表名");
            assertTrue(shentongSql.contains("SEQUENCE") || shentongSql.contains("AUTO_INCREMENT") || 
                      shentongSql.contains("IDENTITY"), 
                      "应转换AUTO_INCREMENT为SEQUENCE、保留或转换为IDENTITY");
            assertTrue(shentongSql.contains("PRIMARY KEY"), "应保留主键");
            assertTrue(shentongSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
            assertTrue(shentongSql.contains("DEFAULT"), "应保留默认值");
            
            // 根据神通官方文档，CURRENT_TIMESTAMP可能转换为SYSDATE
            assertTrue(shentongSql.contains("SYSDATE") || shentongSql.contains("CURRENT_TIMESTAMP"), 
                      "应转换时间函数为SYSDATE或保留CURRENT_TIMESTAMP");
            assertTrue(shentongSql.contains("'pending'"), "应保留字符串默认值");
            assertTrue(shentongSql.contains("DEFAULT 0"), "应保留数值默认值");
        }
    }

    @Nested
    @DisplayName("DML语句转换测试")
    class DmlStatementTests {
        
        @Test
        @DisplayName("测试LIMIT语句转换")
        void testLimitStatementConversion() {
            // 根据神通官方文档，LIMIT可能需要转换为ROWNUM
            String mysqlSql = "SELECT * FROM users WHERE age > 18 LIMIT 10";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("LIMIT语句转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证LIMIT转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("SELECT"), "应保留SELECT");
            assertTrue(shentongSql.contains("users"), "应保留表名");
            assertTrue(shentongSql.contains("age"), "应保留列名");
            // 根据神通官方文档，LIMIT可能转换为ROWNUM或保留LIMIT
            assertTrue(shentongSql.contains("ROWNUM") || shentongSql.contains("LIMIT"), 
                      "应转换LIMIT为ROWNUM或保留LIMIT");
        }

        @Test
        @DisplayName("测试INSERT语句转换")
        void testInsertStatementConversion() {
            // 根据神通官方文档，INSERT语句基本语法与Oracle兼容
            String mysqlSql = "INSERT INTO users (name, email, age) VALUES ('John', '<EMAIL>', 25)";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("INSERT语句转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证INSERT语句转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("INSERT INTO"), "应保留INSERT INTO");
            assertTrue(shentongSql.contains("users"), "应保留表名");
            assertTrue(shentongSql.contains("name"), "应保留列名");
            assertTrue(shentongSql.contains("email"), "应保留列名");
            assertTrue(shentongSql.contains("age"), "应保留列名");
            assertTrue(shentongSql.contains("VALUES"), "应保留VALUES");
            assertTrue(shentongSql.contains("'John'"), "应保留字符串值");
            assertTrue(shentongSql.contains("'<EMAIL>'"), "应保留邮箱值");
            assertTrue(shentongSql.contains("25"), "应保留数值");
        }
    }

    @Nested
    @DisplayName("Oracle兼容性测试")
    class OracleCompatibilityTests {
        
        @Test
        @DisplayName("测试DUAL表兼容性")
        void testDualTableCompatibility() {
            // 根据神通官方文档，神通数据库支持Oracle的DUAL表
            String mysqlSql = "SELECT 1 AS test_value";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("DUAL表兼容性转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证DUAL表兼容性
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("SELECT"), "应保留SELECT");
            // 根据神通官方文档，可能需要添加FROM DUAL
            assertTrue(shentongSql.contains("1") || shentongSql.contains("test_value"), "应保留查询内容");
        }
    }

    @Nested
    @DisplayName("约束转换测试")
    class ConstraintConversionTests {

        @Test
        @DisplayName("测试核心约束转换")
        void testCoreConstraintConversion() {
            // 根据神通官方文档，测试核心约束转换
            String mysqlSql = "CREATE TABLE core_constraints (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "email VARCHAR(100) NOT NULL UNIQUE, " +
                    "age INT CHECK (age >= 0), " +
                    "status VARCHAR(20) DEFAULT 'active'" +
                    ")";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("核心约束转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证核心约束转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(shentongSql.contains("core_constraints"), "应保留表名");
            assertTrue(shentongSql.contains("SEQUENCE") || shentongSql.contains("AUTO_INCREMENT") ||
                      shentongSql.contains("IDENTITY"),
                      "应转换AUTO_INCREMENT为SEQUENCE、保留或转换为IDENTITY");
            assertTrue(shentongSql.contains("PRIMARY KEY"), "应保留主键约束");
            assertTrue(shentongSql.contains("NOT NULL"), "应保留NOT NULL约束");
            assertTrue(shentongSql.contains("UNIQUE"), "应保留UNIQUE约束");
            assertTrue(shentongSql.contains("CHECK") || shentongSql.contains("age"),
                      "应处理CHECK约束");
            assertTrue(shentongSql.contains("DEFAULT"), "应保留默认值");
            assertTrue(shentongSql.contains("'active'"), "应保留字符串默认值");
        }

        @Test
        @DisplayName("测试外键约束转换")
        void testForeignKeyConstraintConversion() {
            // 根据神通官方文档，外键约束语法与Oracle兼容
            String mysqlSql = "CREATE TABLE orders (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "user_id INT NOT NULL, " +
                    "total DECIMAL(10,2), " +
                    "FOREIGN KEY (user_id) REFERENCES users(id)" +
                    ")";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("外键约束转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证外键约束转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(shentongSql.contains("orders"), "应保留表名");
            assertTrue(shentongSql.contains("FOREIGN KEY"), "应保留外键约束");
            assertTrue(shentongSql.contains("REFERENCES"), "应保留引用关系");
            assertTrue(shentongSql.contains("users(id)"), "应保留引用表和列");
        }
    }

    @Nested
    @DisplayName("索引转换测试")
    class IndexConversionTests {

        @Test
        @DisplayName("测试基础索引转换")
        void testBasicIndexConversion() {
            // 根据神通官方文档，CREATE INDEX语法与Oracle兼容
            String mysqlSql = "CREATE INDEX idx_users_email ON users(email)";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("基础索引转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证索引转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE INDEX"), "应保留CREATE INDEX");
            assertTrue(shentongSql.contains("idx_users_email"), "应保留索引名");
            assertTrue(shentongSql.contains("users"), "应保留表名");
            assertTrue(shentongSql.contains("email"), "应保留列名");
        }

        @Test
        @DisplayName("测试复合索引转换")
        void testCompositeIndexConversion() {
            // 根据神通官方文档，复合索引语法与Oracle兼容
            String mysqlSql = "CREATE INDEX idx_orders_user_date ON orders(user_id, order_date)";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("复合索引转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证复合索引转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE INDEX"), "应保留CREATE INDEX");
            assertTrue(shentongSql.contains("idx_orders_user_date"), "应保留索引名");
            assertTrue(shentongSql.contains("orders"), "应保留表名");
            assertTrue(shentongSql.contains("user_id"), "应保留第一个列名");
            assertTrue(shentongSql.contains("order_date"), "应保留第二个列名");
        }
    }

    @Nested
    @DisplayName("序列和触发器测试")
    class SequenceAndTriggerTests {

        @Test
        @DisplayName("测试序列创建转换")
        void testSequenceCreationConversion() {
            // 根据神通官方文档，AUTO_INCREMENT可能需要转换为序列
            String mysqlSql = "CREATE TABLE seq_test (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))";

            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);

            System.out.println("序列创建转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

            // 根据神通官方文档，验证序列转换
            assertTrue(result.isSuccess(), "转换应该成功");
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "转换结果不应为空");
            assertTrue(shentongSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
            assertTrue(shentongSql.contains("seq_test"), "应保留表名");
            // 根据神通官方文档，可能需要创建序列或保留AUTO_INCREMENT
            assertTrue(shentongSql.contains("SEQUENCE") || shentongSql.contains("AUTO_INCREMENT") ||
                      shentongSql.contains("IDENTITY"),
                      "应转换为SEQUENCE、保留AUTO_INCREMENT或转换为IDENTITY");
        }
    }
}
