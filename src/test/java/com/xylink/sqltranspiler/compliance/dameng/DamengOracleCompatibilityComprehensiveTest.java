package com.xylink.sqltranspiler.compliance.dameng;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * 达梦Oracle兼容性深度测试
 * 
 * 严格遵循达梦数据库官方文档：
 * - 官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - SQL开发指南：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - Oracle兼容性：达梦数据库提供Oracle兼容模式
 * 
 * 根据rule-db.md规范：
 * - 所有测试基于达梦官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. DUAL表支持
 * 2. Oracle函数兼容性（SYSDATE、NVL、DECODE等）
 * 3. 序列和触发器
 * 4. 分页查询（ROWNUM）
 * 5. 层次查询（START WITH、CONNECT BY）
 * 6. 分析函数和窗口函数
 * 7. PL/SQL兼容性
 * 8. 数据类型映射
 */
@DisplayName("达梦Oracle兼容性深度测试")
public class DamengOracleCompatibilityComprehensiveTest {

    private SqlTranspiler damengTranspiler;

    @BeforeEach
    void setUp() {
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("DUAL表支持测试 - 基于达梦官方文档Oracle兼容性")
    class DualTableSupportTests {
        
        @Test
        @DisplayName("DUAL表基础查询测试")
        void testDualTableBasicQueries() {
            // 根据达梦官方文档，达梦支持Oracle的DUAL表
            String[] dualQueries = {
                "SELECT SYSDATE FROM DUAL",
                "SELECT 1 + 1 FROM DUAL",
                "SELECT 'Hello World' FROM DUAL",
                "SELECT USER FROM DUAL",
                "SELECT SYSTIMESTAMP FROM DUAL"
            };

            for (String mysqlSql : dualQueries) {
                testDamengTranspilation(mysqlSql, "DUAL表查询");
            }
        }

        @Test
        @DisplayName("DUAL表复杂表达式测试")
        void testDualTableComplexExpressions() {
            // 根据达梦官方文档，测试DUAL表的复杂表达式
            String mysqlSql = "SELECT " +
                    "SYSDATE AS current_date, " +
                    "SYSDATE + 1 AS tomorrow, " +
                    "SYSDATE - 1 AS yesterday, " +
                    "TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS formatted_date, " +
                    "DECODE(1, 1, 'One', 2, 'Two', 'Other') AS decode_result, " +
                    "NVL(NULL, 'Default Value') AS nvl_result, " +
                    "CASE WHEN 1=1 THEN 'True' ELSE 'False' END AS case_result " +
                    "FROM DUAL";

            testDamengTranspilation(mysqlSql, "DUAL表复杂表达式");
        }
    }

    @Nested
    @DisplayName("Oracle函数兼容性测试 - 基于达梦官方文档")
    class OracleFunctionCompatibilityTests {
        
        @Test
        @DisplayName("日期时间函数测试")
        void testDateTimeFunctions() {
            // 根据达梦官方文档，测试Oracle兼容的日期时间函数
            String mysqlSql = "SELECT " +
                    "SYSDATE AS system_date, " +
                    "SYSTIMESTAMP AS system_timestamp, " +
                    "ADD_MONTHS(SYSDATE, 3) AS add_months, " +
                    "MONTHS_BETWEEN(SYSDATE, SYSDATE - 30) AS months_between, " +
                    "NEXT_DAY(SYSDATE, 'MONDAY') AS next_monday, " +
                    "LAST_DAY(SYSDATE) AS last_day_of_month, " +
                    "TRUNC(SYSDATE) AS truncated_date, " +
                    "ROUND(SYSDATE, 'MM') AS rounded_date, " +
                    "EXTRACT(YEAR FROM SYSDATE) AS extract_year, " +
                    "TO_CHAR(SYSDATE, 'YYYY-MM-DD') AS to_char_date, " +
                    "TO_DATE('2024-01-01', 'YYYY-MM-DD') AS to_date " +
                    "FROM DUAL";

            testDamengTranspilation(mysqlSql, "Oracle日期时间函数");
        }

        @Test
        @DisplayName("字符串函数测试")
        void testStringFunctions() {
            // 根据达梦官方文档，测试Oracle兼容的字符串函数
            String mysqlSql = "SELECT " +
                    "SUBSTR('Hello World', 1, 5) AS substr_func, " +
                    "INSTR('Hello World', 'World') AS instr_func, " +
                    "LENGTH('Hello') AS length_func, " +
                    "UPPER('hello') AS upper_func, " +
                    "LOWER('HELLO') AS lower_func, " +
                    "INITCAP('hello world') AS initcap_func, " +
                    "LTRIM('  hello  ') AS ltrim_func, " +
                    "RTRIM('  hello  ') AS rtrim_func, " +
                    "TRIM('  hello  ') AS trim_func, " +
                    "LPAD('hello', 10, '*') AS lpad_func, " +
                    "RPAD('hello', 10, '*') AS rpad_func, " +
                    "REPLACE('Hello World', 'World', 'Oracle') AS replace_func, " +
                    "TRANSLATE('Hello', 'elo', '310') AS translate_func " +
                    "FROM DUAL";

            testDamengTranspilation(mysqlSql, "Oracle字符串函数");
        }

        @Test
        @DisplayName("数值函数测试")
        void testNumericFunctions() {
            // 根据达梦官方文档，测试Oracle兼容的数值函数
            String mysqlSql = "SELECT " +
                    "ABS(-10) AS abs_func, " +
                    "CEIL(4.3) AS ceil_func, " +
                    "FLOOR(4.7) AS floor_func, " +
                    "ROUND(4.567, 2) AS round_func, " +
                    "TRUNC(4.567, 2) AS trunc_func, " +
                    "MOD(10, 3) AS mod_func, " +
                    "POWER(2, 3) AS power_func, " +
                    "SQRT(16) AS sqrt_func, " +
                    "SIGN(-10) AS sign_func, " +
                    "GREATEST(1, 5, 3, 9, 2) AS greatest_func, " +
                    "LEAST(1, 5, 3, 9, 2) AS least_func " +
                    "FROM DUAL";

            testDamengTranspilation(mysqlSql, "Oracle数值函数");
        }

        @Test
        @DisplayName("条件函数测试")
        void testConditionalFunctions() {
            // 根据达梦官方文档，测试Oracle兼容的条件函数
            String mysqlSql = "SELECT " +
                    "NVL(NULL, 'Default') AS nvl_func, " +
                    "NVL2('Value', 'Not Null', 'Is Null') AS nvl2_func, " +
                    "NULLIF('A', 'B') AS nullif_func, " +
                    "COALESCE(NULL, NULL, 'First Non-Null') AS coalesce_func, " +
                    "DECODE(1, 1, 'One', 2, 'Two', 3, 'Three', 'Other') AS decode_func, " +
                    "CASE " +
                    "  WHEN 1 = 1 THEN 'True' " +
                    "  WHEN 1 = 2 THEN 'False' " +
                    "  ELSE 'Unknown' " +
                    "END AS case_func " +
                    "FROM DUAL";

            testDamengTranspilation(mysqlSql, "Oracle条件函数");
        }
    }

    @Nested
    @DisplayName("序列和触发器测试 - 基于达梦官方文档")
    class SequenceAndTriggerTests {
        
        @Test
        @DisplayName("序列创建和使用测试")
        void testSequenceCreationAndUsage() {
            // 根据达梦官方文档，测试Oracle兼容的序列
            String[] sequenceStatements = {
                "CREATE SEQUENCE emp_seq START WITH 1 INCREMENT BY 1 MAXVALUE 999999 NOCYCLE CACHE 20",
                "SELECT emp_seq.NEXTVAL FROM DUAL",
                "SELECT emp_seq.CURRVAL FROM DUAL",
                "INSERT INTO employees (id, name) VALUES (emp_seq.NEXTVAL, 'John Doe')",
                "DROP SEQUENCE emp_seq"
            };

            for (String mysqlSql : sequenceStatements) {
                testDamengTranspilation(mysqlSql, "序列操作");
            }
        }

        @Test
        @DisplayName("触发器创建测试")
        void testTriggerCreation() {
            // 根据达梦官方文档，测试Oracle兼容的触发器
            String mysqlSql = "CREATE OR REPLACE TRIGGER emp_audit_trigger " +
                    "BEFORE INSERT OR UPDATE ON employees " +
                    "FOR EACH ROW " +
                    "BEGIN " +
                    "  IF INSERTING THEN " +
                    "    :NEW.created_date := SYSDATE; " +
                    "    :NEW.created_by := USER; " +
                    "  END IF; " +
                    "  IF UPDATING THEN " +
                    "    :NEW.updated_date := SYSDATE; " +
                    "    :NEW.updated_by := USER; " +
                    "  END IF; " +
                    "END;";

            testDamengTranspilation(mysqlSql, "Oracle触发器");
        }
    }

    @Nested
    @DisplayName("分页查询测试 - 基于达梦官方文档")
    class PaginationQueryTests {
        
        @Test
        @DisplayName("ROWNUM分页测试")
        void testRownumPagination() {
            // 根据达梦官方文档，测试Oracle兼容的ROWNUM分页
            String mysqlSql = "SELECT * FROM (" +
                    "  SELECT emp.*, ROWNUM rn FROM (" +
                    "    SELECT employee_id, first_name, last_name, salary " +
                    "    FROM employees " +
                    "    ORDER BY salary DESC" +
                    "  ) emp " +
                    "  WHERE ROWNUM <= 20" +
                    ") " +
                    "WHERE rn > 10";

            testDamengTranspilation(mysqlSql, "ROWNUM分页查询");
        }

        @Test
        @DisplayName("ROW_NUMBER分页测试")
        void testRowNumberPagination() {
            // 根据达梦官方文档，测试Oracle兼容的ROW_NUMBER分页
            String mysqlSql = "SELECT * FROM (" +
                    "  SELECT employee_id, first_name, last_name, salary, " +
                    "         ROW_NUMBER() OVER (ORDER BY salary DESC) AS rn " +
                    "  FROM employees" +
                    ") " +
                    "WHERE rn BETWEEN 11 AND 20";

            testDamengTranspilation(mysqlSql, "ROW_NUMBER分页查询");
        }
    }

    @Nested
    @DisplayName("层次查询测试 - 基于达梦官方文档")
    class HierarchicalQueryTests {
        
        @Test
        @DisplayName("START WITH CONNECT BY测试")
        void testStartWithConnectBy() {
            // 根据达梦官方文档，测试Oracle兼容的层次查询
            String mysqlSql = "SELECT employee_id, first_name, last_name, manager_id, LEVEL " +
                    "FROM employees " +
                    "START WITH manager_id IS NULL " +
                    "CONNECT BY PRIOR employee_id = manager_id " +
                    "ORDER SIBLINGS BY first_name";

            testDamengTranspilation(mysqlSql, "层次查询START WITH CONNECT BY");
        }

        @Test
        @DisplayName("层次查询函数测试")
        void testHierarchicalFunctions() {
            // 根据达梦官方文档，测试层次查询相关函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "last_name, " +
                    "manager_id, " +
                    "LEVEL, " +
                    "SYS_CONNECT_BY_PATH(first_name, '/') AS path, " +
                    "CONNECT_BY_ISLEAF AS is_leaf, " +
                    "CONNECT_BY_ISCYCLE AS is_cycle " +
                    "FROM employees " +
                    "START WITH manager_id IS NULL " +
                    "CONNECT BY NOCYCLE PRIOR employee_id = manager_id";

            testDamengTranspilation(mysqlSql, "层次查询函数");
        }
    }

    @Nested
    @DisplayName("分析函数和窗口函数测试 - 基于达梦官方文档")
    class AnalyticWindowFunctionTests {
        
        @Test
        @DisplayName("排名分析函数测试")
        void testRankingAnalyticFunctions() {
            // 根据达梦官方文档，测试Oracle兼容的分析函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "last_name, " +
                    "salary, " +
                    "department_id, " +
                    "RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) AS salary_rank, " +
                    "DENSE_RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) AS dense_rank, " +
                    "ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) AS row_num, " +
                    "PERCENT_RANK() OVER (PARTITION BY department_id ORDER BY salary) AS percent_rank, " +
                    "CUME_DIST() OVER (PARTITION BY department_id ORDER BY salary) AS cumulative_dist, " +
                    "NTILE(4) OVER (PARTITION BY department_id ORDER BY salary DESC) AS quartile " +
                    "FROM employees";

            testDamengTranspilation(mysqlSql, "排名分析函数");
        }

        @Test
        @DisplayName("偏移分析函数测试")
        void testOffsetAnalyticFunctions() {
            // 根据达梦官方文档，测试偏移分析函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "salary, " +
                    "hire_date, " +
                    "LAG(salary, 1) OVER (ORDER BY hire_date) AS prev_salary, " +
                    "LEAD(salary, 1) OVER (ORDER BY hire_date) AS next_salary, " +
                    "FIRST_VALUE(salary) OVER (ORDER BY hire_date ROWS UNBOUNDED PRECEDING) AS first_salary, " +
                    "LAST_VALUE(salary) OVER (ORDER BY hire_date ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) AS last_salary " +
                    "FROM employees " +
                    "ORDER BY hire_date";

            testDamengTranspilation(mysqlSql, "偏移分析函数");
        }

        @Test
        @DisplayName("聚合分析函数测试")
        void testAggregateAnalyticFunctions() {
            // 根据达梦官方文档，测试聚合分析函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "salary, " +
                    "department_id, " +
                    "SUM(salary) OVER (PARTITION BY department_id) AS dept_total_salary, " +
                    "AVG(salary) OVER (PARTITION BY department_id) AS dept_avg_salary, " +
                    "COUNT(*) OVER (PARTITION BY department_id) AS dept_employee_count, " +
                    "MIN(salary) OVER (PARTITION BY department_id) AS dept_min_salary, " +
                    "MAX(salary) OVER (PARTITION BY department_id) AS dept_max_salary, " +
                    "SUM(salary) OVER (ORDER BY hire_date ROWS UNBOUNDED PRECEDING) AS running_total " +
                    "FROM employees";

            testDamengTranspilation(mysqlSql, "聚合分析函数");
        }
    }

    @Nested
    @DisplayName("数据类型映射测试 - 基于达梦官方文档")
    class DataTypeMappingTests {
        
        @Test
        @DisplayName("Oracle数据类型兼容性测试")
        void testOracleDataTypeCompatibility() {
            // 根据达梦官方文档，测试Oracle兼容的数据类型
            String mysqlSql = "CREATE TABLE oracle_types_test (" +
                    "id NUMBER(10) PRIMARY KEY, " +
                    "name VARCHAR2(100) NOT NULL, " +
                    "description CLOB, " +
                    "binary_data BLOB, " +
                    "birth_date DATE, " +
                    "created_timestamp TIMESTAMP, " +
                    "salary NUMBER(10,2), " +
                    "is_active CHAR(1) DEFAULT 'Y', " +
                    "raw_data RAW(16)" +
                    ")";

            testDamengTranspilation(mysqlSql, "Oracle数据类型兼容性");
        }
    }

    /**
     * 测试达梦数据库的转换
     */
    private void testDamengTranspilation(String mysqlSql, String testType) {
        TranspilationResult damengResult = damengTranspiler.transpile(mysqlSql);
        System.out.println("达梦" + testType + "转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (damengResult.isSuccess() ? damengResult.getTargetSql() : damengResult.getErrorMessage()));
        assertTrue(damengResult.isSuccess(), "达梦" + testType + "转换应该成功");
        assertNotNull(damengResult.getTargetSql(), "达梦转换结果不应为空");
        System.out.println("----------------------------------------");
    }
}
