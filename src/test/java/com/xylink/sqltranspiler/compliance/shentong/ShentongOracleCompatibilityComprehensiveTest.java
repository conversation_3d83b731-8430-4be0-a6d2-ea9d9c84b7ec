package com.xylink.sqltranspiler.compliance.shentong;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * 神通Oracle兼容性深度测试
 * 
 * 严格遵循神通数据库官方文档：
 * - 官方文档：参考项目内docs/shentong_mapping.md文档
 * - Oracle兼容性：神通数据库SQL参考手册
 * 
 * 根据rule-db.md规范：
 * - 所有测试基于神通官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. Oracle兼容性（DUAL表、TO_CHAR函数、DECODE函数、NVL函数）
 * 2. 层次查询（START WITH、CONNECT BY语法）
 * 3. 分析函数（RANK、DENSE_RANK、LEAD、LAG窗口函数）
 * 4. 空间数据类型（POINT、POLYGON、GEOMETRY类型）
 * 5. PL/SQL（Oracle兼容的存储过程和函数）
 * 6. 自增列（AUTO_INCREMENT完全兼容）
 * 7. 数据类型映射和转换
 */
@DisplayName("神通Oracle兼容性深度测试")
public class ShentongOracleCompatibilityComprehensiveTest {

    private SqlTranspiler shentongTranspiler;

    @BeforeEach
    void setUp() {
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("Oracle兼容性测试 - 基于神通官方文档")
    class OracleCompatibilityTests {
        
        @Test
        @DisplayName("DUAL表支持测试 - 基于神通官方文档")
        void testDualTableSupport() {
            // 根据神通官方文档，神通支持Oracle的DUAL表
            String[] dualQueries = {
                "SELECT SYSDATE FROM DUAL",
                "SELECT 1 + 1 FROM DUAL",
                "SELECT 'Hello World' FROM DUAL",
                "SELECT USER FROM DUAL",
                "SELECT CURRENT_TIMESTAMP FROM DUAL"
            };

            for (String mysqlSql : dualQueries) {
                testShentongTranspilation(mysqlSql, "DUAL表查询");
            }
        }

        @Test
        @DisplayName("Oracle函数兼容性测试 - 基于神通官方文档")
        void testOracleFunctionCompatibility() {
            // 根据神通官方文档，测试Oracle兼容函数
            String mysqlSql = "SELECT " +
                    "NVL(NULL, 'Default Value') AS nvl_result, " +
                    "TO_CHAR(SYSDATE, 'YYYY-MM-DD') AS formatted_date, " +
                    "DECODE(status, 1, 'Active', 2, 'Inactive', 'Unknown') AS status_desc, " +
                    "SUBSTR('Hello World', 1, 5) AS substr_result, " +
                    "INSTR('Hello World', 'World') AS instr_result, " +
                    "LENGTH('Hello') AS length_result, " +
                    "UPPER('hello') AS upper_result, " +
                    "LOWER('HELLO') AS lower_result, " +
                    "TRIM('  hello  ') AS trim_result, " +
                    "LPAD('hello', 10, '*') AS lpad_result, " +
                    "RPAD('hello', 10, '*') AS rpad_result " +
                    "FROM DUAL";

            testShentongTranspilation(mysqlSql, "Oracle函数兼容性");
        }

        @Test
        @DisplayName("Oracle数值函数测试")
        void testOracleNumericFunctions() {
            // 根据神通官方文档，测试Oracle兼容的数值函数
            String mysqlSql = "SELECT " +
                    "ABS(-10) AS abs_func, " +
                    "CEIL(4.3) AS ceil_func, " +
                    "FLOOR(4.7) AS floor_func, " +
                    "ROUND(4.567, 2) AS round_func, " +
                    "TRUNC(4.567, 2) AS trunc_func, " +
                    "MOD(10, 3) AS mod_func, " +
                    "POWER(2, 3) AS power_func, " +
                    "SQRT(16) AS sqrt_func, " +
                    "SIGN(-10) AS sign_func, " +
                    "GREATEST(1, 5, 3, 9, 2) AS greatest_func, " +
                    "LEAST(1, 5, 3, 9, 2) AS least_func " +
                    "FROM DUAL";

            testShentongTranspilation(mysqlSql, "Oracle数值函数");
        }

        @Test
        @DisplayName("Oracle日期函数测试")
        void testOracleDateFunctions() {
            // 根据神通官方文档，测试Oracle兼容的日期函数
            String mysqlSql = "SELECT " +
                    "SYSDATE AS current_date, " +
                    "ADD_MONTHS(SYSDATE, 3) AS add_months, " +
                    "MONTHS_BETWEEN(SYSDATE, SYSDATE - 30) AS months_between, " +
                    "NEXT_DAY(SYSDATE, 'MONDAY') AS next_monday, " +
                    "LAST_DAY(SYSDATE) AS last_day_of_month, " +
                    "TRUNC(SYSDATE) AS truncated_date, " +
                    "ROUND(SYSDATE, 'MM') AS rounded_date, " +
                    "EXTRACT(YEAR FROM SYSDATE) AS extract_year, " +
                    "TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS formatted_datetime, " +
                    "TO_DATE('2024-01-01', 'YYYY-MM-DD') AS to_date " +
                    "FROM DUAL";

            testShentongTranspilation(mysqlSql, "Oracle日期函数");
        }
    }

    @Nested
    @DisplayName("层次查询测试 - 基于神通官方文档")
    class HierarchicalQueryTests {
        
        @Test
        @DisplayName("START WITH CONNECT BY测试 - 基于神通官方文档")
        void testStartWithConnectBy() {
            // 根据神通官方文档，神通支持Oracle兼容的层次查询
            String mysqlSql = "SELECT employee_id, first_name, last_name, manager_id, LEVEL " +
                    "FROM employees " +
                    "START WITH manager_id IS NULL " +
                    "CONNECT BY PRIOR employee_id = manager_id " +
                    "ORDER SIBLINGS BY first_name";

            testShentongTranspilation(mysqlSql, "层次查询START WITH CONNECT BY");
        }

        @Test
        @DisplayName("层次查询函数测试")
        void testHierarchicalFunctions() {
            // 根据神通官方文档，测试层次查询相关函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "last_name, " +
                    "manager_id, " +
                    "LEVEL, " +
                    "SYS_CONNECT_BY_PATH(first_name, '/') AS path, " +
                    "CONNECT_BY_ISLEAF AS is_leaf, " +
                    "CONNECT_BY_ISCYCLE AS is_cycle " +
                    "FROM employees " +
                    "START WITH manager_id IS NULL " +
                    "CONNECT BY NOCYCLE PRIOR employee_id = manager_id";

            testShentongTranspilation(mysqlSql, "层次查询函数");
        }

        @Test
        @DisplayName("复杂层次查询测试")
        void testComplexHierarchicalQuery() {
            // 根据神通官方文档，测试复杂的层次查询
            String mysqlSql = "SELECT " +
                    "LPAD(' ', 2 * (LEVEL - 1)) || first_name AS indented_name, " +
                    "employee_id, " +
                    "manager_id, " +
                    "LEVEL, " +
                    "CONNECT_BY_ROOT first_name AS root_manager " +
                    "FROM employees " +
                    "WHERE LEVEL <= 3 " +
                    "START WITH manager_id IS NULL " +
                    "CONNECT BY PRIOR employee_id = manager_id " +
                    "AND PRIOR first_name != 'John'";

            testShentongTranspilation(mysqlSql, "复杂层次查询");
        }
    }

    @Nested
    @DisplayName("分析函数测试 - 基于神通官方文档")
    class AnalyticalFunctionTests {
        
        @Test
        @DisplayName("排名分析函数测试 - 基于神通官方文档")
        void testRankingAnalyticalFunctions() {
            // 根据神通官方文档，神通支持Oracle兼容的分析函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "last_name, " +
                    "salary, " +
                    "department_id, " +
                    "RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) AS salary_rank, " +
                    "DENSE_RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) AS dense_rank, " +
                    "ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) AS row_num, " +
                    "PERCENT_RANK() OVER (PARTITION BY department_id ORDER BY salary) AS percent_rank, " +
                    "CUME_DIST() OVER (PARTITION BY department_id ORDER BY salary) AS cumulative_dist, " +
                    "NTILE(4) OVER (PARTITION BY department_id ORDER BY salary DESC) AS quartile " +
                    "FROM employees";

            testShentongTranspilation(mysqlSql, "排名分析函数");
        }

        @Test
        @DisplayName("偏移分析函数测试")
        void testOffsetAnalyticalFunctions() {
            // 根据神通官方文档，测试偏移分析函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "salary, " +
                    "hire_date, " +
                    "LAG(salary, 1) OVER (ORDER BY hire_date) AS prev_salary, " +
                    "LEAD(salary, 1) OVER (ORDER BY hire_date) AS next_salary, " +
                    "LAG(salary, 2, 0) OVER (ORDER BY hire_date) AS prev_prev_salary, " +
                    "LEAD(salary, 2, 0) OVER (ORDER BY hire_date) AS next_next_salary, " +
                    "FIRST_VALUE(salary) OVER (ORDER BY hire_date ROWS UNBOUNDED PRECEDING) AS first_salary, " +
                    "LAST_VALUE(salary) OVER (ORDER BY hire_date ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) AS last_salary " +
                    "FROM employees " +
                    "ORDER BY hire_date";

            testShentongTranspilation(mysqlSql, "偏移分析函数");
        }

        @Test
        @DisplayName("聚合分析函数测试")
        void testAggregateAnalyticalFunctions() {
            // 根据神通官方文档，测试聚合分析函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "first_name, " +
                    "salary, " +
                    "department_id, " +
                    "SUM(salary) OVER (PARTITION BY department_id) AS dept_total_salary, " +
                    "AVG(salary) OVER (PARTITION BY department_id) AS dept_avg_salary, " +
                    "COUNT(*) OVER (PARTITION BY department_id) AS dept_employee_count, " +
                    "MIN(salary) OVER (PARTITION BY department_id) AS dept_min_salary, " +
                    "MAX(salary) OVER (PARTITION BY department_id) AS dept_max_salary, " +
                    "SUM(salary) OVER (ORDER BY hire_date ROWS UNBOUNDED PRECEDING) AS running_total, " +
                    "AVG(salary) OVER (ORDER BY hire_date ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING) AS moving_avg " +
                    "FROM employees";

            testShentongTranspilation(mysqlSql, "聚合分析函数");
        }
    }

    @Nested
    @DisplayName("空间数据类型测试 - 基于神通官方文档")
    class SpatialDataTypeTests {
        
        @Test
        @DisplayName("空间数据类型创建测试 - 基于神通官方文档")
        void testSpatialDataTypeCreation() {
            // 根据神通官方文档，神通支持空间数据类型
            String mysqlSql = "CREATE TABLE locations (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "coordinates POINT, " +
                    "area POLYGON, " +
                    "boundary GEOMETRY, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ")";

            testShentongTranspilation(mysqlSql, "空间数据类型创建");
        }

        @Test
        @DisplayName("空间函数测试")
        void testSpatialFunctions() {
            // 根据神通官方文档，测试空间函数
            String mysqlSql = "SELECT " +
                    "id, " +
                    "name, " +
                    "ST_X(coordinates) AS longitude, " +
                    "ST_Y(coordinates) AS latitude, " +
                    "ST_Area(area) AS area_size, " +
                    "ST_Perimeter(area) AS perimeter, " +
                    "ST_Contains(area, coordinates) AS contains_point, " +
                    "ST_Distance(coordinates, ST_GeomFromText('POINT(0 0)')) AS distance_from_origin " +
                    "FROM locations " +
                    "WHERE ST_Within(coordinates, area)";

            testShentongTranspilation(mysqlSql, "空间函数");
        }
    }

    @Nested
    @DisplayName("PL/SQL兼容性测试 - 基于神通官方文档")
    class PLSQLCompatibilityTests {
        
        @Test
        @DisplayName("存储过程测试 - 基于神通官方文档")
        void testStoredProcedures() {
            // 根据神通官方文档，神通支持Oracle兼容的PL/SQL存储过程
            String mysqlSql = "CREATE OR REPLACE PROCEDURE update_employee_salary(" +
                    "  p_employee_id IN NUMBER, " +
                    "  p_salary_increase IN NUMBER" +
                    ") AS " +
                    "  v_current_salary NUMBER; " +
                    "  v_new_salary NUMBER; " +
                    "BEGIN " +
                    "  SELECT salary INTO v_current_salary " +
                    "  FROM employees " +
                    "  WHERE employee_id = p_employee_id; " +
                    "  " +
                    "  v_new_salary := v_current_salary + p_salary_increase; " +
                    "  " +
                    "  UPDATE employees " +
                    "  SET salary = v_new_salary " +
                    "  WHERE employee_id = p_employee_id; " +
                    "  " +
                    "  COMMIT; " +
                    "EXCEPTION " +
                    "  WHEN NO_DATA_FOUND THEN " +
                    "    RAISE_APPLICATION_ERROR(-20001, 'Employee not found'); " +
                    "  WHEN OTHERS THEN " +
                    "    ROLLBACK; " +
                    "    RAISE; " +
                    "END;";

            testShentongTranspilation(mysqlSql, "PL/SQL存储过程");
        }

        @Test
        @DisplayName("函数测试")
        void testFunctions() {
            // 根据神通官方文档，测试Oracle兼容的函数
            String mysqlSql = "CREATE OR REPLACE FUNCTION calculate_annual_bonus(" +
                    "  p_employee_id IN NUMBER" +
                    ") RETURN NUMBER AS " +
                    "  v_salary NUMBER; " +
                    "  v_performance_rating NUMBER; " +
                    "  v_bonus NUMBER; " +
                    "BEGIN " +
                    "  SELECT salary, performance_rating " +
                    "  INTO v_salary, v_performance_rating " +
                    "  FROM employees " +
                    "  WHERE employee_id = p_employee_id; " +
                    "  " +
                    "  CASE v_performance_rating " +
                    "    WHEN 5 THEN v_bonus := v_salary * 0.2; " +
                    "    WHEN 4 THEN v_bonus := v_salary * 0.15; " +
                    "    WHEN 3 THEN v_bonus := v_salary * 0.1; " +
                    "    WHEN 2 THEN v_bonus := v_salary * 0.05; " +
                    "    ELSE v_bonus := 0; " +
                    "  END CASE; " +
                    "  " +
                    "  RETURN v_bonus; " +
                    "EXCEPTION " +
                    "  WHEN NO_DATA_FOUND THEN " +
                    "    RETURN 0; " +
                    "END;";

            testShentongTranspilation(mysqlSql, "PL/SQL函数");
        }
    }

    @Nested
    @DisplayName("自增列兼容性测试 - 基于神通官方文档")
    class AutoIncrementCompatibilityTests {
        
        @Test
        @DisplayName("AUTO_INCREMENT完全兼容测试 - 基于神通官方文档")
        void testAutoIncrementFullCompatibility() {
            // 根据神通官方文档，神通完全支持MySQL的AUTO_INCREMENT语法
            String mysqlSql = "CREATE TABLE test_auto_increment (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(50) NOT NULL, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ") AUTO_INCREMENT = 1000";

            testShentongTranspilation(mysqlSql, "AUTO_INCREMENT完全兼容");
        }

        @Test
        @DisplayName("ALTER TABLE AUTO_INCREMENT测试")
        void testAlterTableAutoIncrement() {
            // 根据神通官方文档，神通支持ALTER TABLE AUTO_INCREMENT语法
            String mysqlSql = "ALTER TABLE test_auto_increment AUTO_INCREMENT = 5000";

            testShentongTranspilation(mysqlSql, "ALTER TABLE AUTO_INCREMENT");
        }

        @Test
        @DisplayName("LAST_INSERT_ID函数测试")
        void testLastInsertIdFunction() {
            // 根据神通官方文档，神通支持LAST_INSERT_ID()函数
            String mysqlSql = "SELECT LAST_INSERT_ID() AS last_id FROM DUAL";

            testShentongTranspilation(mysqlSql, "LAST_INSERT_ID函数");
        }
    }

    @Nested
    @DisplayName("数据类型映射测试 - 基于神通官方文档")
    class DataTypeMappingTests {
        
        @Test
        @DisplayName("数据类型映射测试 - 基于神通官方文档")
        void testDataTypeMapping() {
            // 根据神通官方文档，测试数据类型映射
            String mysqlSql = "CREATE TABLE data_type_mapping_test (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "tiny_col TINYINT, " +
                    "small_col SMALLINT, " +
                    "medium_col MEDIUMINT, " +
                    "int_col INT, " +
                    "big_col BIGINT, " +
                    "decimal_col DECIMAL(10,2), " +
                    "float_col FLOAT, " +
                    "double_col DOUBLE, " +
                    "char_col CHAR(10), " +
                    "varchar_col VARCHAR(255), " +
                    "text_col TEXT, " +
                    "longtext_col LONGTEXT, " +
                    "blob_col BLOB, " +
                    "date_col DATE, " +
                    "datetime_col DATETIME, " +
                    "timestamp_col TIMESTAMP, " +
                    "json_col JSON" +
                    ")";

            testShentongTranspilation(mysqlSql, "数据类型映射");
        }
    }

    /**
     * 测试神通数据库的转换
     */
    private void testShentongTranspilation(String mysqlSql, String testType) {
        TranspilationResult shentongResult = shentongTranspiler.transpile(mysqlSql);
        System.out.println("神通" + testType + "转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (shentongResult.isSuccess() ? shentongResult.getTargetSql() : shentongResult.getErrorMessage()));
        assertTrue(shentongResult.isSuccess(), "神通" + testType + "转换应该成功");
        assertNotNull(shentongResult.getTargetSql(), "神通转换结果不应为空");
        System.out.println("----------------------------------------");
    }
}
