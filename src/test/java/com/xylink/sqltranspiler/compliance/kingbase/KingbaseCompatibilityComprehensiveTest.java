package com.xylink.sqltranspiler.compliance.kingbase;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * 金仓PostgreSQL/MySQL兼容性深度测试
 * 
 * 严格遵循金仓数据库官方文档：
 * - 官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - MySQL兼容性：https://help.kingbase.com.cn/v8.6.7.12/development/develop-transfer/transplant-mysql/
 * - PostgreSQL兼容性：金仓数据库基于PostgreSQL内核
 * 
 * 根据rule-db.md规范：
 * - 所有测试基于金仓官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. PostgreSQL兼容性（数组类型、JSON类型、窗口函数、CTE）
 * 2. MySQL兼容性（函数、语法、数据类型）
 * 3. 高级特性（分区表、存储过程、用户定义函数）
 * 4. 事务控制和并发
 * 5. 全文搜索和索引
 * 6. 扩展功能
 */
@DisplayName("金仓PostgreSQL/MySQL兼容性深度测试")
public class KingbaseCompatibilityComprehensiveTest {

    private SqlTranspiler kingbaseTranspiler;

    @BeforeEach
    void setUp() {
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("PostgreSQL兼容性测试 - 基于金仓官方文档")
    class PostgreSQLCompatibilityTests {
        
        @Test
        @DisplayName("数组类型测试 - 基于金仓官方文档3.1.3.6节")
        void testArrayTypes() {
            // 根据金仓官方文档3.1.3.6节，测试数组类型
            String mysqlSql = "CREATE TABLE array_test (" +
                    "id INTEGER PRIMARY KEY, " +
                    "tags TEXT[], " +
                    "scores INTEGER[], " +
                    "matrix INTEGER[][], " +
                    "names VARCHAR(50)[10]" +
                    ")";

            testKingbaseTranspilation(mysqlSql, "PostgreSQL数组类型");
        }

        @Test
        @DisplayName("数组操作测试")
        void testArrayOperations() {
            // 根据金仓官方文档，测试数组操作
            String mysqlSql = "SELECT " +
                    "ARRAY[1, 2, 3, 4] AS simple_array, " +
                    "ARRAY[ARRAY[1, 2], ARRAY[3, 4]] AS multi_array, " +
                    "ARRAY['hello', 'world'] AS string_array, " +
                    "tags[1] AS first_tag, " +
                    "tags[1:3] AS first_three_tags, " +
                    "array_length(tags, 1) AS tag_count, " +
                    "array_append(tags, 'new_tag') AS appended_tags, " +
                    "array_prepend('first_tag', tags) AS prepended_tags, " +
                    "array_cat(tags, ARRAY['extra1', 'extra2']) AS concatenated_tags, " +
                    "unnest(tags) AS expanded_tags " +
                    "FROM array_test";

            testKingbaseTranspilation(mysqlSql, "PostgreSQL数组操作");
        }

        @Test
        @DisplayName("JSON类型测试 - 基于金仓官方文档3.1.1.13节")
        void testJsonTypes() {
            // 根据金仓官方文档3.1.1.13节，测试JSON类型
            String mysqlSql = "CREATE TABLE json_test (" +
                    "id INTEGER PRIMARY KEY, " +
                    "data JSON, " +
                    "metadata JSONB, " +
                    "config JSON NOT NULL DEFAULT '{}'" +
                    ")";

            testKingbaseTranspilation(mysqlSql, "PostgreSQL JSON类型");
        }

        @Test
        @DisplayName("JSON操作测试")
        void testJsonOperations() {
            // 根据金仓官方文档，测试JSON操作
            String mysqlSql = "SELECT " +
                    "data->>'name' AS name, " +
                    "data->'address'->>'city' AS city, " +
                    "data #> '{address,street}' AS street, " +
                    "data #>> '{address,street}' AS street_text, " +
                    "data ? 'email' AS has_email, " +
                    "data ?& ARRAY['name', 'email'] AS has_name_and_email, " +
                    "data ?| ARRAY['phone', 'mobile'] AS has_phone_or_mobile, " +
                    "data @> '{\"status\": \"active\"}' AS is_active, " +
                    "data <@ '{\"name\": \"John\", \"status\": \"active\", \"extra\": \"value\"}' AS is_subset, " +
                    "jsonb_set(metadata, '{last_updated}', '\"2024-01-01\"') AS updated_metadata, " +
                    "jsonb_insert(metadata, '{tags,0}', '\"new_tag\"') AS inserted_metadata, " +
                    "jsonb_pretty(data) AS formatted_json " +
                    "FROM json_test";

            testKingbaseTranspilation(mysqlSql, "PostgreSQL JSON操作");
        }

        @Test
        @DisplayName("窗口函数测试 - 基于金仓官方文档")
        void testWindowFunctions() {
            // 根据金仓官方文档，测试PostgreSQL兼容的窗口函数
            String mysqlSql = "SELECT " +
                    "employee_id, " +
                    "department_id, " +
                    "salary, " +
                    "ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) AS row_num, " +
                    "RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) AS rank_val, " +
                    "DENSE_RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) AS dense_rank_val, " +
                    "PERCENT_RANK() OVER (PARTITION BY department_id ORDER BY salary) AS percent_rank_val, " +
                    "CUME_DIST() OVER (PARTITION BY department_id ORDER BY salary) AS cumulative_dist, " +
                    "NTILE(4) OVER (PARTITION BY department_id ORDER BY salary DESC) AS quartile, " +
                    "LAG(salary, 1) OVER (PARTITION BY department_id ORDER BY hire_date) AS prev_salary, " +
                    "LEAD(salary, 1) OVER (PARTITION BY department_id ORDER BY hire_date) AS next_salary, " +
                    "FIRST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY hire_date " +
                    "    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS first_salary, " +
                    "LAST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY hire_date " +
                    "    ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) AS last_salary " +
                    "FROM employees";

            testKingbaseTranspilation(mysqlSql, "PostgreSQL窗口函数");
        }

        @Test
        @DisplayName("CTE公共表表达式测试")
        void testCommonTableExpressions() {
            // 根据金仓官方文档，测试CTE
            String mysqlSql = "WITH RECURSIVE employee_hierarchy AS (" +
                    "  SELECT employee_id, name, manager_id, 1 AS level " +
                    "  FROM employees " +
                    "  WHERE manager_id IS NULL " +
                    "  UNION ALL " +
                    "  SELECT e.employee_id, e.name, e.manager_id, eh.level + 1 " +
                    "  FROM employees e " +
                    "  INNER JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id" +
                    "), " +
                    "department_stats AS (" +
                    "  SELECT department_id, COUNT(*) AS employee_count, AVG(salary) AS avg_salary " +
                    "  FROM employees " +
                    "  GROUP BY department_id" +
                    ") " +
                    "SELECT eh.name, eh.level, ds.employee_count, ds.avg_salary " +
                    "FROM employee_hierarchy eh " +
                    "LEFT JOIN employees e ON eh.employee_id = e.employee_id " +
                    "LEFT JOIN department_stats ds ON e.department_id = ds.department_id " +
                    "ORDER BY eh.level, eh.name";

            testKingbaseTranspilation(mysqlSql, "PostgreSQL CTE");
        }
    }

    @Nested
    @DisplayName("MySQL兼容性测试 - 基于金仓官方文档MySQL模式")
    class MySQLCompatibilityTests {
        
        @Test
        @DisplayName("MySQL函数兼容性测试 - 基于金仓官方文档23.4节")
        void testMySQLFunctionCompatibility() {
            // 根据金仓官方文档23.4节，测试MySQL兼容函数
            String mysqlSql = "SELECT " +
                    "CONCAT('Hello', ' ', 'World') AS concat_func, " +
                    "SUBSTRING('Hello World', 7, 5) AS substring_func, " +
                    "LENGTH('Hello') AS length_func, " +
                    "UPPER('hello') AS upper_func, " +
                    "LOWER('HELLO') AS lower_func, " +
                    "TRIM('  hello  ') AS trim_func, " +
                    "REPLACE('Hello World', 'World', 'MySQL') AS replace_func, " +
                    "INSTR('Hello World', 'World') AS instr_func, " +
                    "LEFT('Hello World', 5) AS left_func, " +
                    "RIGHT('Hello World', 5) AS right_func, " +
                    "LPAD('hello', 10, '*') AS lpad_func, " +
                    "RPAD('hello', 10, '*') AS rpad_func, " +
                    "REVERSE('Hello') AS reverse_func, " +
                    "REPEAT('MySQL', 3) AS repeat_func, " +
                    "LOCATE('World', 'Hello World') AS locate_func " +
                    "FROM DUAL";

            testKingbaseTranspilation(mysqlSql, "MySQL函数兼容性");
        }

        @Test
        @DisplayName("MySQL日期函数兼容性测试")
        void testMySQLDateFunctionCompatibility() {
            // 根据金仓官方文档，测试MySQL兼容的日期函数
            String mysqlSql = "SELECT " +
                    "NOW() AS current_datetime, " +
                    "CURDATE() AS current_date, " +
                    "CURTIME() AS current_time, " +
                    "DATE('2024-01-01 12:30:45') AS extract_date, " +
                    "TIME('2024-01-01 12:30:45') AS extract_time, " +
                    "YEAR('2024-01-01') AS extract_year, " +
                    "MONTH('2024-01-01') AS extract_month, " +
                    "DAY('2024-01-01') AS extract_day, " +
                    "HOUR('12:30:45') AS extract_hour, " +
                    "MINUTE('12:30:45') AS extract_minute, " +
                    "SECOND('12:30:45') AS extract_second, " +
                    "WEEKDAY('2024-01-01') AS weekday, " +
                    "WEEK('2024-01-01') AS week_of_year, " +
                    "YEARWEEK('2024-01-01') AS year_week, " +
                    "DATE_FORMAT('2024-01-01', '%Y-%m-%d') AS date_format, " +
                    "STR_TO_DATE('01,5,2024', '%d,%m,%Y') AS string_to_date, " +
                    "DATE_ADD('2024-01-01', INTERVAL 1 MONTH) AS date_add, " +
                    "DATE_SUB('2024-01-01', INTERVAL 1 WEEK) AS date_subtract, " +
                    "DATEDIFF('2024-12-31', '2024-01-01') AS date_difference, " +
                    "UNIX_TIMESTAMP('2024-01-01 00:00:00') AS unix_timestamp, " +
                    "FROM_UNIXTIME(1640995200) AS from_unix_time " +
                    "FROM DUAL";

            testKingbaseTranspilation(mysqlSql, "MySQL日期函数兼容性");
        }

        @Test
        @DisplayName("MySQL数据类型兼容性测试 - 基于金仓官方文档23.1节")
        void testMySQLDataTypeCompatibility() {
            // 根据金仓官方文档23.1节，测试MySQL兼容的数据类型
            String mysqlSql = "CREATE TABLE mysql_types_test (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "tiny_col TINYINT, " +
                    "small_col SMALLINT, " +
                    "medium_col MEDIUMINT, " +
                    "int_col INT, " +
                    "big_col BIGINT, " +
                    "float_col FLOAT, " +
                    "double_col DOUBLE, " +
                    "decimal_col DECIMAL(10,2), " +
                    "char_col CHAR(10), " +
                    "varchar_col VARCHAR(255), " +
                    "text_col TEXT, " +
                    "longtext_col LONGTEXT, " +
                    "mediumtext_col MEDIUMTEXT, " +
                    "tinytext_col TINYTEXT, " +
                    "blob_col BLOB, " +
                    "date_col DATE, " +
                    "time_col TIME, " +
                    "datetime_col DATETIME, " +
                    "timestamp_col TIMESTAMP, " +
                    "year_col YEAR, " +
                    "json_col JSON, " +
                    "enum_col ENUM('small', 'medium', 'large'), " +
                    "set_col SET('read', 'write', 'execute')" +
                    ")";

            testKingbaseTranspilation(mysqlSql, "MySQL数据类型兼容性");
        }

        @Test
        @DisplayName("MySQL语法兼容性测试 - 基于金仓官方文档23.5节")
        void testMySQLSyntaxCompatibility() {
            // 根据金仓官方文档23.5节，测试MySQL兼容语法
            String mysqlSql = "INSERT INTO users (name, email) " +
                    "VALUES ('John Doe', '<EMAIL>') " +
                    "ON DUPLICATE KEY UPDATE " +
                    "email = VALUES(email), " +
                    "updated_at = NOW()";

            testKingbaseTranspilation(mysqlSql, "MySQL语法兼容性");
        }
    }

    @Nested
    @DisplayName("高级特性测试 - 基于金仓官方文档")
    class AdvancedFeaturesTests {
        
        @Test
        @DisplayName("分区表测试")
        void testPartitionedTables() {
            // 根据金仓官方文档，测试分区表
            String mysqlSql = "CREATE TABLE sales (" +
                    "id SERIAL PRIMARY KEY, " +
                    "sale_date DATE NOT NULL, " +
                    "amount DECIMAL(10,2), " +
                    "region VARCHAR(50)" +
                    ") PARTITION BY RANGE (sale_date) (" +
                    "  PARTITION p2023 VALUES LESS THAN ('2024-01-01'), " +
                    "  PARTITION p2024 VALUES LESS THAN ('2025-01-01'), " +
                    "  PARTITION p_future VALUES LESS THAN MAXVALUE" +
                    ")";

            testKingbaseTranspilation(mysqlSql, "分区表");
        }

        @Test
        @DisplayName("存储过程测试")
        void testStoredProcedures() {
            // 根据金仓官方文档，测试存储过程
            String mysqlSql = "CREATE OR REPLACE FUNCTION calculate_bonus(" +
                    "  emp_id INTEGER, " +
                    "  bonus_rate DECIMAL DEFAULT 0.1" +
                    ") RETURNS DECIMAL AS $$ " +
                    "DECLARE " +
                    "  emp_salary DECIMAL; " +
                    "  bonus_amount DECIMAL; " +
                    "BEGIN " +
                    "  SELECT salary INTO emp_salary " +
                    "  FROM employees " +
                    "  WHERE employee_id = emp_id; " +
                    "  " +
                    "  IF emp_salary IS NULL THEN " +
                    "    RAISE EXCEPTION 'Employee not found: %', emp_id; " +
                    "  END IF; " +
                    "  " +
                    "  bonus_amount := emp_salary * bonus_rate; " +
                    "  " +
                    "  RETURN bonus_amount; " +
                    "END; " +
                    "$$ LANGUAGE plpgsql;";

            testKingbaseTranspilation(mysqlSql, "存储过程");
        }

        @Test
        @DisplayName("用户定义函数测试")
        void testUserDefinedFunctions() {
            // 根据金仓官方文档，测试用户定义函数
            String mysqlSql = "CREATE OR REPLACE FUNCTION get_employee_count(" +
                    "  dept_name VARCHAR" +
                    ") RETURNS INTEGER AS $$ " +
                    "BEGIN " +
                    "  RETURN (" +
                    "    SELECT COUNT(*) " +
                    "    FROM employees e " +
                    "    JOIN departments d ON e.department_id = d.department_id " +
                    "    WHERE d.department_name = dept_name" +
                    "  ); " +
                    "END; " +
                    "$$ LANGUAGE plpgsql;";

            testKingbaseTranspilation(mysqlSql, "用户定义函数");
        }

        @Test
        @DisplayName("触发器测试")
        void testTriggers() {
            // 根据金仓官方文档，测试触发器
            String mysqlSql = "CREATE OR REPLACE FUNCTION update_modified_time() " +
                    "RETURNS TRIGGER AS $$ " +
                    "BEGIN " +
                    "  NEW.updated_at = NOW(); " +
                    "  RETURN NEW; " +
                    "END; " +
                    "$$ LANGUAGE plpgsql; " +
                    "" +
                    "CREATE TRIGGER update_employee_modified_time " +
                    "  BEFORE UPDATE ON employees " +
                    "  FOR EACH ROW " +
                    "  EXECUTE FUNCTION update_modified_time();";

            testKingbaseTranspilation(mysqlSql, "触发器");
        }
    }

    @Nested
    @DisplayName("事务控制和并发测试 - 基于金仓官方文档")
    class TransactionConcurrencyTests {
        
        @Test
        @DisplayName("事务隔离级别测试")
        void testTransactionIsolationLevels() {
            // 根据金仓官方文档，测试事务隔离级别
            String[] transactionStatements = {
                "BEGIN TRANSACTION ISOLATION LEVEL READ UNCOMMITTED",
                "BEGIN TRANSACTION ISOLATION LEVEL READ COMMITTED",
                "BEGIN TRANSACTION ISOLATION LEVEL REPEATABLE READ",
                "BEGIN TRANSACTION ISOLATION LEVEL SERIALIZABLE",
                "SET TRANSACTION ISOLATION LEVEL READ COMMITTED",
                "COMMIT",
                "ROLLBACK"
            };

            for (String mysqlSql : transactionStatements) {
                testKingbaseTranspilation(mysqlSql, "事务隔离级别");
            }
        }

        @Test
        @DisplayName("保存点测试")
        void testSavepoints() {
            // 根据金仓官方文档，测试保存点
            String[] savepointStatements = {
                "BEGIN",
                "SAVEPOINT sp1",
                "INSERT INTO test_table (name) VALUES ('test1')",
                "SAVEPOINT sp2",
                "INSERT INTO test_table (name) VALUES ('test2')",
                "ROLLBACK TO SAVEPOINT sp1",
                "RELEASE SAVEPOINT sp1",
                "COMMIT"
            };

            for (String mysqlSql : savepointStatements) {
                testKingbaseTranspilation(mysqlSql, "保存点");
            }
        }
    }

    @Nested
    @DisplayName("全文搜索和索引测试 - 基于金仓官方文档")
    class FullTextSearchIndexTests {
        
        @Test
        @DisplayName("全文搜索测试")
        void testFullTextSearch() {
            // 根据金仓官方文档，测试全文搜索
            String mysqlSql = "SELECT " +
                    "title, " +
                    "content, " +
                    "ts_rank(to_tsvector('english', title || ' ' || content), " +
                    "         to_tsquery('english', 'database & search')) AS rank " +
                    "FROM articles " +
                    "WHERE to_tsvector('english', title || ' ' || content) @@ " +
                    "      to_tsquery('english', 'database & search') " +
                    "ORDER BY rank DESC";

            testKingbaseTranspilation(mysqlSql, "全文搜索");
        }

        @Test
        @DisplayName("GIN索引测试")
        void testGinIndexes() {
            // 根据金仓官方文档，测试GIN索引
            String mysqlSql = "CREATE INDEX idx_articles_fts " +
                    "ON articles " +
                    "USING GIN (to_tsvector('english', title || ' ' || content))";

            testKingbaseTranspilation(mysqlSql, "GIN索引");
        }

        @Test
        @DisplayName("部分索引测试")
        void testPartialIndexes() {
            // 根据金仓官方文档，测试部分索引
            String mysqlSql = "CREATE INDEX idx_active_users " +
                    "ON users (email) " +
                    "WHERE status = 'active'";

            testKingbaseTranspilation(mysqlSql, "部分索引");
        }
    }

    /**
     * 测试金仓数据库的转换
     */
    private void testKingbaseTranspilation(String mysqlSql, String testType) {
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(mysqlSql);
        System.out.println("金仓" + testType + "转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (kingbaseResult.isSuccess() ? kingbaseResult.getTargetSql() : kingbaseResult.getErrorMessage()));
        assertTrue(kingbaseResult.isSuccess(), "金仓" + testType + "转换应该成功");
        assertNotNull(kingbaseResult.getTargetSql(), "金仓转换结果不应为空");
        System.out.println("----------------------------------------");
    }
}
