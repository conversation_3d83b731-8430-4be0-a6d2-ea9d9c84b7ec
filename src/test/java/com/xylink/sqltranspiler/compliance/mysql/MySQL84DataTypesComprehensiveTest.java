package com.xylink.sqltranspiler.compliance.mysql;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * MySQL 8.4数据类型全覆盖测试
 * 
 * 严格遵循MySQL 8.4官方文档第13章：
 * - 官方文档：https://dev.mysql.com/doc/refman/8.4/en/data-types.html
 * - 数值类型：https://dev.mysql.com/doc/refman/8.4/en/numeric-types.html
 * - 日期时间类型：https://dev.mysql.com/doc/refman/8.4/en/date-and-time-types.html
 * - 字符串类型：https://dev.mysql.com/doc/refman/8.4/en/string-types.html
 * - 空间类型：https://dev.mysql.com/doc/refman/8.4/en/spatial-types.html
 * - JSON类型：https://dev.mysql.com/doc/refman/8.4/en/json.html
 * 
 * 根据rule-db.md规范：
 * - 所有测试基于MySQL 8.4官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. 数值类型（整数、定点、浮点、位值）
 * 2. 日期时间类型（DATE、TIME、DATETIME、TIMESTAMP、YEAR）
 * 3. 字符串类型（CHAR、VARCHAR、BINARY、VARBINARY、BLOB、TEXT、ENUM、SET）
 * 4. 空间类型（GEOMETRY、POINT、LINESTRING、POLYGON等）
 * 5. JSON类型
 * 6. 数据类型属性和约束
 */
@DisplayName("MySQL 8.4数据类型全覆盖测试")
public class MySQL84DataTypesComprehensiveTest {

    private SqlTranspiler damengTranspiler;
    private SqlTranspiler kingbaseTranspiler;
    private SqlTranspiler shentongTranspiler;

    @BeforeEach
    void setUp() {
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
            
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
            
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("数值类型全覆盖测试 - 基于MySQL 8.4官方文档13.1节")
    class NumericDataTypesComprehensiveTests {
        
        @Test
        @DisplayName("整数类型全覆盖测试 - TINYINT, SMALLINT, MEDIUMINT, INT, BIGINT")
        void testIntegerTypesComprehensive() {
            // 根据MySQL 8.4官方文档13.1.2节，测试所有整数类型
            String mysqlSql = "CREATE TABLE integer_types_test (" +
                    "tiny_col TINYINT, " +
                    "tiny_unsigned TINYINT UNSIGNED, " +
                    "tiny_zerofill TINYINT(3) ZEROFILL, " +
                    "small_col SMALLINT, " +
                    "small_unsigned SMALLINT UNSIGNED, " +
                    "medium_col MEDIUMINT, " +
                    "medium_unsigned MEDIUMINT UNSIGNED, " +
                    "int_col INT, " +
                    "int_unsigned INT UNSIGNED, " +
                    "int_auto INT AUTO_INCREMENT PRIMARY KEY, " +
                    "bigint_col BIGINT, " +
                    "bigint_unsigned BIGINT UNSIGNED" +
                    ")";

            // 测试达梦转换
            TranspilationResult damengResult = damengTranspiler.transpile(mysqlSql);
            System.out.println("达梦整数类型转换结果:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (damengResult.isSuccess() ? damengResult.getTargetSql() : damengResult.getErrorMessage()));
            
            assertTrue(damengResult.isSuccess(), "达梦整数类型转换应该成功");
            String damengSql = damengResult.getTargetSql();
            assertNotNull(damengSql, "达梦转换结果不应为空");
            
            // 根据达梦官方文档，验证类型映射
            assertTrue(damengSql.contains("SMALLINT"), "TINYINT应转换为SMALLINT");
            assertTrue(damengSql.contains("INT") || damengSql.contains("INTEGER"), "INT类型应保留或转换");
            assertTrue(damengSql.contains("BIGINT"), "BIGINT类型应保留");
            assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                      "AUTO_INCREMENT应转换为IDENTITY或保留");

            // 测试金仓转换
            TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(mysqlSql);
            System.out.println("金仓整数类型转换结果:");
            System.out.println("输出: " + (kingbaseResult.isSuccess() ? kingbaseResult.getTargetSql() : kingbaseResult.getErrorMessage()));
            
            assertTrue(kingbaseResult.isSuccess(), "金仓整数类型转换应该成功");
            
            // 测试神通转换
            TranspilationResult shentongResult = shentongTranspiler.transpile(mysqlSql);
            System.out.println("神通整数类型转换结果:");
            System.out.println("输出: " + (shentongResult.isSuccess() ? shentongResult.getTargetSql() : shentongResult.getErrorMessage()));
            
            assertTrue(shentongResult.isSuccess(), "神通整数类型转换应该成功");
        }

        @Test
        @DisplayName("定点数类型测试 - DECIMAL, NUMERIC")
        void testFixedPointTypesComprehensive() {
            // 根据MySQL 8.4官方文档13.1.3节，测试定点数类型
            String mysqlSql = "CREATE TABLE fixed_point_test (" +
                    "decimal_default DECIMAL, " +
                    "decimal_precision DECIMAL(10), " +
                    "decimal_scale DECIMAL(10,2), " +
                    "decimal_max DECIMAL(65,30), " +
                    "numeric_type NUMERIC(15,5), " +
                    "dec_type DEC(8,2), " +
                    "fixed_type FIXED(12,4)" +
                    ")";

            // 测试所有数据库的转换
            testAllDialects(mysqlSql, "定点数类型");
        }

        @Test
        @DisplayName("浮点数类型测试 - FLOAT, DOUBLE")
        void testFloatingPointTypesComprehensive() {
            // 根据MySQL 8.4官方文档13.1.4节，测试浮点数类型
            String mysqlSql = "CREATE TABLE floating_point_test (" +
                    "float_default FLOAT, " +
                    "float_precision FLOAT(7), " +
                    "float_scale FLOAT(7,4), " +
                    "double_default DOUBLE, " +
                    "double_precision DOUBLE(15,8), " +
                    "real_type REAL, " +
                    "double_precision_type DOUBLE PRECISION" +
                    ")";

            testAllDialects(mysqlSql, "浮点数类型");
        }

        @Test
        @DisplayName("位值类型测试 - BIT")
        void testBitValueTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.1.5节，测试位值类型
            String mysqlSql = "CREATE TABLE bit_value_test (" +
                    "bit_default BIT, " +
                    "bit_length BIT(8), " +
                    "bit_max BIT(64)" +
                    ")";

            testAllDialects(mysqlSql, "位值类型");
        }
    }

    @Nested
    @DisplayName("日期时间类型全覆盖测试 - 基于MySQL 8.4官方文档13.2节")
    class DateTimeTypesComprehensiveTests {
        
        @Test
        @DisplayName("DATE类型测试")
        void testDateTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.2.2节，测试DATE类型
            String mysqlSql = "CREATE TABLE date_test (" +
                    "id INT PRIMARY KEY, " +
                    "birth_date DATE, " +
                    "hire_date DATE NOT NULL, " +
                    "end_date DATE DEFAULT '2024-12-31'" +
                    ")";

            testAllDialects(mysqlSql, "DATE类型");
        }

        @Test
        @DisplayName("TIME类型测试")
        void testTimeTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.2.3节，测试TIME类型
            String mysqlSql = "CREATE TABLE time_test (" +
                    "id INT PRIMARY KEY, " +
                    "start_time TIME, " +
                    "end_time TIME(3), " +
                    "duration TIME(6), " +
                    "default_time TIME DEFAULT '08:00:00'" +
                    ")";

            testAllDialects(mysqlSql, "TIME类型");
        }

        @Test
        @DisplayName("DATETIME类型测试")
        void testDateTimeTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.2.2节，测试DATETIME类型
            String mysqlSql = "CREATE TABLE datetime_test (" +
                    "id INT PRIMARY KEY, " +
                    "created_at DATETIME, " +
                    "updated_at DATETIME(3), " +
                    "precise_time DATETIME(6), " +
                    "default_datetime DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    "auto_update DATETIME ON UPDATE CURRENT_TIMESTAMP" +
                    ")";

            testAllDialects(mysqlSql, "DATETIME类型");
        }

        @Test
        @DisplayName("TIMESTAMP类型测试")
        void testTimestampTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.2.2节，测试TIMESTAMP类型
            String mysqlSql = "CREATE TABLE timestamp_test (" +
                    "id INT PRIMARY KEY, " +
                    "created_ts TIMESTAMP, " +
                    "updated_ts TIMESTAMP(3), " +
                    "precise_ts TIMESTAMP(6), " +
                    "default_ts TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "auto_ts TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" +
                    ")";

            testAllDialects(mysqlSql, "TIMESTAMP类型");
        }

        @Test
        @DisplayName("YEAR类型测试")
        void testYearTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.2.4节，测试YEAR类型
            String mysqlSql = "CREATE TABLE year_test (" +
                    "id INT PRIMARY KEY, " +
                    "birth_year YEAR, " +
                    "graduation_year YEAR(4), " +
                    "default_year YEAR DEFAULT 2024" +
                    ")";

            testAllDialects(mysqlSql, "YEAR类型");
        }
    }

    @Nested
    @DisplayName("字符串类型全覆盖测试 - 基于MySQL 8.4官方文档13.3节")
    class StringTypesComprehensiveTests {
        
        @Test
        @DisplayName("CHAR和VARCHAR类型测试")
        void testCharVarcharTypesComprehensive() {
            // 根据MySQL 8.4官方文档13.3.2节，测试CHAR和VARCHAR类型
            String mysqlSql = "CREATE TABLE char_varchar_test (" +
                    "id INT PRIMARY KEY, " +
                    "char_default CHAR, " +
                    "char_length CHAR(10), " +
                    "char_max CHAR(255), " +
                    "varchar_length VARCHAR(50), " +
                    "varchar_max VARCHAR(65535), " +
                    "char_binary CHAR(10) BINARY, " +
                    "varchar_charset VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci" +
                    ")";

            testAllDialects(mysqlSql, "CHAR和VARCHAR类型");
        }

        @Test
        @DisplayName("BINARY和VARBINARY类型测试")
        void testBinaryVarbinaryTypesComprehensive() {
            // 根据MySQL 8.4官方文档13.3.3节，测试BINARY和VARBINARY类型
            String mysqlSql = "CREATE TABLE binary_varbinary_test (" +
                    "id INT PRIMARY KEY, " +
                    "binary_default BINARY, " +
                    "binary_length BINARY(16), " +
                    "binary_max BINARY(255), " +
                    "varbinary_length VARBINARY(100), " +
                    "varbinary_max VARBINARY(65535)" +
                    ")";

            testAllDialects(mysqlSql, "BINARY和VARBINARY类型");
        }

        @Test
        @DisplayName("BLOB类型测试")
        void testBlobTypesComprehensive() {
            // 根据MySQL 8.4官方文档13.3.4节，测试BLOB类型
            String mysqlSql = "CREATE TABLE blob_test (" +
                    "id INT PRIMARY KEY, " +
                    "tiny_blob TINYBLOB, " +
                    "blob_default BLOB, " +
                    "blob_length BLOB(1000), " +
                    "medium_blob MEDIUMBLOB, " +
                    "long_blob LONGBLOB" +
                    ")";

            testAllDialects(mysqlSql, "BLOB类型");
        }

        @Test
        @DisplayName("TEXT类型测试")
        void testTextTypesComprehensive() {
            // 根据MySQL 8.4官方文档13.3.4节，测试TEXT类型
            String mysqlSql = "CREATE TABLE text_test (" +
                    "id INT PRIMARY KEY, " +
                    "tiny_text TINYTEXT, " +
                    "text_default TEXT, " +
                    "text_length TEXT(2000), " +
                    "medium_text MEDIUMTEXT, " +
                    "long_text LONGTEXT, " +
                    "text_charset TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin" +
                    ")";

            testAllDialects(mysqlSql, "TEXT类型");
        }

        @Test
        @DisplayName("ENUM类型测试")
        void testEnumTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.3.5节，测试ENUM类型
            String mysqlSql = "CREATE TABLE enum_test (" +
                    "id INT PRIMARY KEY, " +
                    "status ENUM('active', 'inactive', 'pending'), " +
                    "priority ENUM('low', 'medium', 'high') DEFAULT 'medium', " +
                    "category ENUM('A', 'B', 'C', 'D', 'E') NOT NULL" +
                    ")";

            testAllDialects(mysqlSql, "ENUM类型");
        }

        @Test
        @DisplayName("SET类型测试")
        void testSetTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.3.6节，测试SET类型
            String mysqlSql = "CREATE TABLE set_test (" +
                    "id INT PRIMARY KEY, " +
                    "permissions SET('read', 'write', 'execute'), " +
                    "features SET('feature1', 'feature2', 'feature3', 'feature4') DEFAULT 'feature1', " +
                    "flags SET('flag_a', 'flag_b', 'flag_c') NOT NULL" +
                    ")";

            testAllDialects(mysqlSql, "SET类型");
        }
    }

    @Nested
    @DisplayName("空间数据类型全覆盖测试 - 基于MySQL 8.4官方文档13.4节")
    class SpatialDataTypesComprehensiveTests {

        @Test
        @DisplayName("几何类型测试 - GEOMETRY")
        void testGeometryTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.4.1节，测试GEOMETRY类型
            String mysqlSql = "CREATE TABLE geometry_test (" +
                    "id INT PRIMARY KEY, " +
                    "geom GEOMETRY, " +
                    "geom_not_null GEOMETRY NOT NULL, " +
                    "geom_srid GEOMETRY SRID 4326" +
                    ")";

            testAllDialects(mysqlSql, "GEOMETRY类型");
        }

        @Test
        @DisplayName("点类型测试 - POINT")
        void testPointTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.4.2.3节，测试POINT类型
            String mysqlSql = "CREATE TABLE point_test (" +
                    "id INT PRIMARY KEY, " +
                    "location POINT, " +
                    "coordinates POINT NOT NULL, " +
                    "gps_point POINT SRID 4326" +
                    ")";

            testAllDialects(mysqlSql, "POINT类型");
        }

        @Test
        @DisplayName("线串类型测试 - LINESTRING")
        void testLineStringTypeComprehensive() {
            // 根据MySQL 8.4官方文档13.4.2.5节，测试LINESTRING类型
            String mysqlSql = "CREATE TABLE linestring_test (" +
                    "id INT PRIMARY KEY, " +
                    "path LINESTRING, " +
                    "route LINESTRING NOT NULL, " +
                    "gps_track LINESTRING SRID 4326" +
                    ")";

            testAllDialects(mysqlSql, "LINESTRING类型");
        }

        @Test
        @DisplayName("多边形类型测试 - POLYGON")
        void testPolygonTypeComprehensive() {
            // 根据MySQL 8.4官方文档********节，测试POLYGON类型
            String mysqlSql = "CREATE TABLE polygon_test (" +
                    "id INT PRIMARY KEY, " +
                    "area POLYGON, " +
                    "boundary POLYGON NOT NULL, " +
                    "region POLYGON SRID 4326" +
                    ")";

            testAllDialects(mysqlSql, "POLYGON类型");
        }

        @Test
        @DisplayName("多点类型测试 - MULTIPOINT")
        void testMultiPointTypeComprehensive() {
            // 根据MySQL 8.4官方文档********节，测试MULTIPOINT类型
            String mysqlSql = "CREATE TABLE multipoint_test (" +
                    "id INT PRIMARY KEY, " +
                    "locations MULTIPOINT, " +
                    "waypoints MULTIPOINT NOT NULL" +
                    ")";

            testAllDialects(mysqlSql, "MULTIPOINT类型");
        }

        @Test
        @DisplayName("多线串类型测试 - MULTILINESTRING")
        void testMultiLineStringTypeComprehensive() {
            // 根据MySQL 8.4官方文档*********节，测试MULTILINESTRING类型
            String mysqlSql = "CREATE TABLE multilinestring_test (" +
                    "id INT PRIMARY KEY, " +
                    "paths MULTILINESTRING, " +
                    "routes MULTILINESTRING NOT NULL" +
                    ")";

            testAllDialects(mysqlSql, "MULTILINESTRING类型");
        }

        @Test
        @DisplayName("多多边形类型测试 - MULTIPOLYGON")
        void testMultiPolygonTypeComprehensive() {
            // 根据MySQL 8.4官方文档*********节，测试MULTIPOLYGON类型
            String mysqlSql = "CREATE TABLE multipolygon_test (" +
                    "id INT PRIMARY KEY, " +
                    "areas MULTIPOLYGON, " +
                    "regions MULTIPOLYGON NOT NULL" +
                    ")";

            testAllDialects(mysqlSql, "MULTIPOLYGON类型");
        }

        @Test
        @DisplayName("几何集合类型测试 - GEOMETRYCOLLECTION")
        void testGeometryCollectionTypeComprehensive() {
            // 根据MySQL 8.4官方文档********节，测试GEOMETRYCOLLECTION类型
            String mysqlSql = "CREATE TABLE geometrycollection_test (" +
                    "id INT PRIMARY KEY, " +
                    "mixed_geom GEOMETRYCOLLECTION, " +
                    "complex_geom GEOMETRYCOLLECTION NOT NULL" +
                    ")";

            testAllDialects(mysqlSql, "GEOMETRYCOLLECTION类型");
        }
    }

    @Nested
    @DisplayName("JSON数据类型全覆盖测试 - 基于MySQL 8.4官方文档13.5节")
    class JsonDataTypeComprehensiveTests {

        @Test
        @DisplayName("JSON类型基础测试")
        void testJsonTypeBasic() {
            // 根据MySQL 8.4官方文档13.5节，测试JSON类型
            String mysqlSql = "CREATE TABLE json_test (" +
                    "id INT PRIMARY KEY, " +
                    "data JSON, " +
                    "config JSON NOT NULL, " +
                    "metadata JSON DEFAULT '{}', " +
                    "settings JSON DEFAULT NULL" +
                    ")";

            testAllDialects(mysqlSql, "JSON类型基础");
        }

        @Test
        @DisplayName("JSON类型复杂测试")
        void testJsonTypeComplex() {
            // 根据MySQL 8.4官方文档13.5节，测试JSON类型的复杂用法
            String mysqlSql = "CREATE TABLE json_complex_test (" +
                    "id INT PRIMARY KEY, " +
                    "user_profile JSON COMMENT 'User profile data', " +
                    "preferences JSON DEFAULT '{}' COMMENT 'User preferences', " +
                    "audit_log JSON NOT NULL COMMENT 'Audit trail', " +
                    "INDEX idx_user_profile ((CAST(user_profile->>'$.name' AS CHAR(50))))" +
                    ")";

            testAllDialects(mysqlSql, "JSON类型复杂");
        }
    }

    @Nested
    @DisplayName("数据类型属性和约束全覆盖测试")
    class DataTypeAttributesComprehensiveTests {

        @Test
        @DisplayName("AUTO_INCREMENT属性测试")
        void testAutoIncrementAttribute() {
            // 根据MySQL 8.4官方文档，测试AUTO_INCREMENT属性
            String mysqlSql = "CREATE TABLE auto_increment_test (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "bigint_auto BIGINT AUTO_INCREMENT UNIQUE, " +
                    "small_auto SMALLINT AUTO_INCREMENT" +
                    ") AUTO_INCREMENT = 1000";

            testAllDialects(mysqlSql, "AUTO_INCREMENT属性");
        }

        @Test
        @DisplayName("UNSIGNED属性测试")
        void testUnsignedAttribute() {
            // 根据MySQL 8.4官方文档13.1.6节，测试UNSIGNED属性
            String mysqlSql = "CREATE TABLE unsigned_test (" +
                    "id INT UNSIGNED PRIMARY KEY, " +
                    "tiny_unsigned TINYINT UNSIGNED, " +
                    "small_unsigned SMALLINT UNSIGNED, " +
                    "medium_unsigned MEDIUMINT UNSIGNED, " +
                    "int_unsigned INT UNSIGNED, " +
                    "bigint_unsigned BIGINT UNSIGNED, " +
                    "float_unsigned FLOAT UNSIGNED, " +
                    "double_unsigned DOUBLE UNSIGNED, " +
                    "decimal_unsigned DECIMAL(10,2) UNSIGNED" +
                    ")";

            testAllDialects(mysqlSql, "UNSIGNED属性");
        }

        @Test
        @DisplayName("ZEROFILL属性测试")
        void testZerofillAttribute() {
            // 根据MySQL 8.4官方文档13.1.6节，测试ZEROFILL属性
            String mysqlSql = "CREATE TABLE zerofill_test (" +
                    "id INT PRIMARY KEY, " +
                    "padded_int INT(8) ZEROFILL, " +
                    "padded_decimal DECIMAL(10,2) ZEROFILL, " +
                    "padded_float FLOAT(7,2) ZEROFILL" +
                    ")";

            testAllDialects(mysqlSql, "ZEROFILL属性");
        }

        @Test
        @DisplayName("字符集和排序规则测试")
        void testCharsetCollationAttributes() {
            // 根据MySQL 8.4官方文档，测试字符集和排序规则
            String mysqlSql = "CREATE TABLE charset_test (" +
                    "id INT PRIMARY KEY, " +
                    "utf8_col VARCHAR(100) CHARACTER SET utf8mb4, " +
                    "collated_col VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci, " +
                    "binary_col VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin, " +
                    "latin1_col VARCHAR(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci" +
                    ") CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";

            testAllDialects(mysqlSql, "字符集和排序规则");
        }
    }

    /**
     * 测试所有数据库方言的转换
     */
    private void testAllDialects(String mysqlSql, String testType) {
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(mysqlSql);
        System.out.println("达梦" + testType + "转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (damengResult.isSuccess() ? damengResult.getTargetSql() : damengResult.getErrorMessage()));
        assertTrue(damengResult.isSuccess(), "达梦" + testType + "转换应该成功");
        assertNotNull(damengResult.getTargetSql(), "达梦转换结果不应为空");

        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(mysqlSql);
        System.out.println("金仓" + testType + "转换结果:");
        System.out.println("输出: " + (kingbaseResult.isSuccess() ? kingbaseResult.getTargetSql() : kingbaseResult.getErrorMessage()));
        assertTrue(kingbaseResult.isSuccess(), "金仓" + testType + "转换应该成功");
        assertNotNull(kingbaseResult.getTargetSql(), "金仓转换结果不应为空");

        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(mysqlSql);
        System.out.println("神通" + testType + "转换结果:");
        System.out.println("输出: " + (shentongResult.isSuccess() ? shentongResult.getTargetSql() : shentongResult.getErrorMessage()));
        assertTrue(shentongResult.isSuccess(), "神通" + testType + "转换应该成功");
        assertNotNull(shentongResult.getTargetSql(), "神通转换结果不应为空");

        System.out.println("----------------------------------------");
    }
}
