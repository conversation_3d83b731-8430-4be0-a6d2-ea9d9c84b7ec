package com.xylink.sqltranspiler.compliance.mysql;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * MySQL 8.4 SQL语句全覆盖测试
 * 
 * 严格遵循MySQL 8.4官方文档第15章：
 * - 官方文档：https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html
 * - DDL语句：https://dev.mysql.com/doc/refman/8.4/en/sql-data-definition-statements.html
 * - DML语句：https://dev.mysql.com/doc/refman/8.4/en/sql-data-manipulation-statements.html
 * - 事务语句：https://dev.mysql.com/doc/refman/8.4/en/sql-transactional-statements.html
 * - 复合语句：https://dev.mysql.com/doc/refman/8.4/en/sql-compound-statements.html
 * 
 * 根据rule-db.md规范：
 * - 所有测试基于MySQL 8.4官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. 数据定义语句（CREATE、ALTER、DROP）
 * 2. 数据操作语句（SELECT、INSERT、UPDATE、DELETE）
 * 3. 事务控制语句（START TRANSACTION、COMMIT、ROLLBACK）
 * 4. 复合语句（存储过程、函数、触发器）
 * 5. 管理语句（SHOW、DESCRIBE、EXPLAIN）
 * 6. 实用语句（USE、HELP）
 */
@DisplayName("MySQL 8.4 SQL语句全覆盖测试")
public class MySQL84SqlStatementsComprehensiveTest {

    private SqlTranspiler damengTranspiler;
    private SqlTranspiler kingbaseTranspiler;
    private SqlTranspiler shentongTranspiler;

    @BeforeEach
    void setUp() {
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
            
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
            
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("数据定义语句全覆盖测试 - 基于MySQL 8.4官方文档15.1节")
    class DataDefinitionStatementsComprehensiveTests {
        
        @Test
        @DisplayName("CREATE TABLE语句全覆盖测试")
        void testCreateTableStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.1.20节，测试CREATE TABLE语句的各种特性
            String mysqlSql = "CREATE TABLE comprehensive_table (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL COMMENT '姓名', " +
                    "email VARCHAR(255) UNIQUE KEY, " +
                    "age TINYINT UNSIGNED DEFAULT 0, " +
                    "salary DECIMAL(10,2) CHECK (salary >= 0), " +
                    "department_id INT, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                    "status ENUM('active', 'inactive') DEFAULT 'active', " +
                    "tags SET('tag1', 'tag2', 'tag3'), " +
                    "profile JSON, " +
                    "location POINT, " +
                    "INDEX idx_name (name), " +
                    "INDEX idx_email_status (email, status), " +
                    "FULLTEXT INDEX ft_name (name), " +
                    "SPATIAL INDEX sp_location (location), " +
                    "FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL ON UPDATE CASCADE" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci " +
                    "AUTO_INCREMENT=1000 COMMENT='综合测试表'";

            testAllDialects(mysqlSql, "CREATE TABLE语句全覆盖");
        }

        @Test
        @DisplayName("ALTER TABLE语句全覆盖测试")
        void testAlterTableStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.1.9节，测试ALTER TABLE语句的各种操作
            String mysqlSql = "ALTER TABLE test_table " +
                    "ADD COLUMN new_column VARCHAR(50) AFTER existing_column, " +
                    "ADD INDEX idx_new (new_column), " +
                    "MODIFY COLUMN old_column TEXT NOT NULL, " +
                    "CHANGE COLUMN old_name new_name INT UNSIGNED, " +
                    "DROP COLUMN unwanted_column, " +
                    "DROP INDEX old_index, " +
                    "ADD CONSTRAINT fk_test FOREIGN KEY (ref_id) REFERENCES ref_table(id), " +
                    "DROP FOREIGN KEY old_fk, " +
                    "RENAME TO new_table_name, " +
                    "ENGINE=MyISAM, " +
                    "AUTO_INCREMENT=5000, " +
                    "COMMENT='修改后的表'";

            testAllDialects(mysqlSql, "ALTER TABLE语句全覆盖");
        }

        @Test
        @DisplayName("CREATE INDEX语句全覆盖测试")
        void testCreateIndexStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.1.15节，测试CREATE INDEX语句
            String mysqlSql = "CREATE UNIQUE INDEX idx_unique_email " +
                    "ON users (email) " +
                    "USING BTREE " +
                    "COMMENT '邮箱唯一索引'";

            testAllDialects(mysqlSql, "CREATE INDEX语句");
        }

        @Test
        @DisplayName("CREATE VIEW语句全覆盖测试")
        void testCreateViewStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.1.23节，测试CREATE VIEW语句
            String mysqlSql = "CREATE OR REPLACE VIEW user_summary AS " +
                    "SELECT u.id, u.name, u.email, d.name as department_name, " +
                    "       COUNT(p.id) as project_count " +
                    "FROM users u " +
                    "LEFT JOIN departments d ON u.department_id = d.id " +
                    "LEFT JOIN user_projects up ON u.id = up.user_id " +
                    "LEFT JOIN projects p ON up.project_id = p.id " +
                    "WHERE u.status = 'active' " +
                    "GROUP BY u.id, u.name, u.email, d.name " +
                    "WITH CHECK OPTION";

            testAllDialects(mysqlSql, "CREATE VIEW语句");
        }

        @Test
        @DisplayName("CREATE TRIGGER语句全覆盖测试")
        void testCreateTriggerStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.1.22节，测试CREATE TRIGGER语句
            String mysqlSql = "CREATE TRIGGER update_timestamp " +
                    "BEFORE UPDATE ON users " +
                    "FOR EACH ROW " +
                    "BEGIN " +
                    "  SET NEW.updated_at = CURRENT_TIMESTAMP; " +
                    "  IF NEW.email != OLD.email THEN " +
                    "    INSERT INTO audit_log (table_name, operation, old_value, new_value, changed_at) " +
                    "    VALUES ('users', 'UPDATE', OLD.email, NEW.email, NOW()); " +
                    "  END IF; " +
                    "END";

            testAllDialects(mysqlSql, "CREATE TRIGGER语句");
        }

        @Test
        @DisplayName("DROP语句全覆盖测试")
        void testDropStatementsComprehensive() {
            // 根据MySQL 8.4官方文档，测试各种DROP语句
            String[] dropStatements = {
                "DROP TABLE IF EXISTS temp_table",
                "DROP VIEW IF EXISTS temp_view",
                "DROP INDEX idx_temp ON test_table",
                "DROP TRIGGER IF EXISTS temp_trigger",
                "DROP DATABASE IF EXISTS temp_db"
            };

            for (String dropSql : dropStatements) {
                testAllDialects(dropSql, "DROP语句");
            }
        }
    }

    @Nested
    @DisplayName("数据操作语句全覆盖测试 - 基于MySQL 8.4官方文档15.2节")
    class DataManipulationStatementsComprehensiveTests {
        
        @Test
        @DisplayName("SELECT语句全覆盖测试")
        void testSelectStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.2.13节，测试SELECT语句的复杂特性
            String mysqlSql = "SELECT DISTINCT " +
                    "  u.id, " +
                    "  u.name, " +
                    "  u.email, " +
                    "  d.name AS department_name, " +
                    "  COUNT(p.id) AS project_count, " +
                    "  AVG(p.budget) AS avg_budget, " +
                    "  RANK() OVER (PARTITION BY d.id ORDER BY u.salary DESC) AS salary_rank, " +
                    "  CASE " +
                    "    WHEN u.salary > 10000 THEN 'High' " +
                    "    WHEN u.salary > 5000 THEN 'Medium' " +
                    "    ELSE 'Low' " +
                    "  END AS salary_level, " +
                    "  JSON_EXTRACT(u.profile, '$.skills') AS skills " +
                    "FROM users u " +
                    "INNER JOIN departments d ON u.department_id = d.id " +
                    "LEFT JOIN user_projects up ON u.id = up.user_id " +
                    "LEFT JOIN projects p ON up.project_id = p.id " +
                    "WHERE u.status = 'active' " +
                    "  AND u.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR) " +
                    "  AND (u.salary BETWEEN 3000 AND 15000 OR u.department_id IN (1, 2, 3)) " +
                    "GROUP BY u.id, u.name, u.email, d.id, d.name, u.salary, u.profile " +
                    "HAVING COUNT(p.id) > 0 AND AVG(p.budget) > 5000 " +
                    "ORDER BY salary_rank, u.name " +
                    "LIMIT 50 OFFSET 10 " +
                    "FOR UPDATE";

            testAllDialects(mysqlSql, "SELECT语句全覆盖");
        }

        @Test
        @DisplayName("INSERT语句全覆盖测试")
        void testInsertStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.2.7节，测试INSERT语句的各种形式
            String[] insertStatements = {
                // 基本INSERT
                "INSERT INTO users (name, email, age) VALUES ('John Doe', '<EMAIL>', 30)",
                
                // 多行INSERT
                "INSERT INTO users (name, email, age) VALUES " +
                "('Alice Smith', '<EMAIL>', 25), " +
                "('Bob Johnson', '<EMAIL>', 35), " +
                "('Carol Brown', '<EMAIL>', 28)",
                
                // INSERT ... SELECT
                "INSERT INTO user_backup (id, name, email, created_at) " +
                "SELECT id, name, email, created_at FROM users WHERE status = 'inactive'",
                
                // INSERT ... ON DUPLICATE KEY UPDATE
                "INSERT INTO user_stats (user_id, login_count, last_login) " +
                "VALUES (1, 1, NOW()) " +
                "ON DUPLICATE KEY UPDATE " +
                "login_count = login_count + 1, last_login = NOW()",
                
                // INSERT IGNORE
                "INSERT IGNORE INTO users (name, email) VALUES ('Test User', '<EMAIL>')"
            };

            for (String insertSql : insertStatements) {
                testAllDialects(insertSql, "INSERT语句");
            }
        }

        @Test
        @DisplayName("UPDATE语句全覆盖测试")
        void testUpdateStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.2.17节，测试UPDATE语句
            String[] updateStatements = {
                // 基本UPDATE
                "UPDATE users SET email = '<EMAIL>', updated_at = NOW() WHERE id = 1",
                
                // 多表UPDATE
                "UPDATE users u " +
                "INNER JOIN departments d ON u.department_id = d.id " +
                "SET u.salary = u.salary * 1.1 " +
                "WHERE d.name = 'Engineering' AND u.performance_rating >= 4",
                
                // 条件UPDATE
                "UPDATE users SET " +
                "status = CASE " +
                "  WHEN last_login < DATE_SUB(NOW(), INTERVAL 6 MONTH) THEN 'inactive' " +
                "  ELSE 'active' " +
                "END, " +
                "updated_at = NOW() " +
                "WHERE status != 'deleted'",
                
                // LIMIT UPDATE
                "UPDATE users SET priority = priority + 1 WHERE department_id = 1 ORDER BY created_at LIMIT 10"
            };

            for (String updateSql : updateStatements) {
                testAllDialects(updateSql, "UPDATE语句");
            }
        }

        @Test
        @DisplayName("DELETE语句全覆盖测试")
        void testDeleteStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.2.2节，测试DELETE语句
            String[] deleteStatements = {
                // 基本DELETE
                "DELETE FROM users WHERE status = 'deleted' AND updated_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)",
                
                // 多表DELETE
                "DELETE u, up FROM users u " +
                "INNER JOIN user_projects up ON u.id = up.user_id " +
                "WHERE u.status = 'inactive' AND u.last_login < DATE_SUB(NOW(), INTERVAL 2 YEAR)",
                
                // LIMIT DELETE
                "DELETE FROM audit_log WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH) ORDER BY created_at LIMIT 1000"
            };

            for (String deleteSql : deleteStatements) {
                testAllDialects(deleteSql, "DELETE语句");
            }
        }

        @Test
        @DisplayName("REPLACE语句测试")
        void testReplaceStatementComprehensive() {
            // 根据MySQL 8.4官方文档15.2.12节，测试REPLACE语句
            String[] replaceStatements = {
                "REPLACE INTO user_settings (user_id, setting_key, setting_value) " +
                "VALUES (1, 'theme', 'dark')",
                
                "REPLACE INTO user_cache " +
                "SELECT user_id, 'last_activity', NOW() FROM user_sessions WHERE active = 1"
            };

            for (String replaceSql : replaceStatements) {
                testAllDialects(replaceSql, "REPLACE语句");
            }
        }
    }

    @Nested
    @DisplayName("事务控制语句全覆盖测试 - 基于MySQL 8.4官方文档15.3节")
    class TransactionalStatementsComprehensiveTests {

        @Test
        @DisplayName("事务控制语句测试")
        void testTransactionControlStatements() {
            // 根据MySQL 8.4官方文档15.3.1节，测试事务控制语句
            String[] transactionStatements = {
                "START TRANSACTION",
                "START TRANSACTION READ ONLY",
                "START TRANSACTION READ WRITE",
                "START TRANSACTION WITH CONSISTENT SNAPSHOT",
                "BEGIN",
                "BEGIN WORK",
                "COMMIT",
                "COMMIT WORK",
                "ROLLBACK",
                "ROLLBACK WORK"
            };

            for (String transactionSql : transactionStatements) {
                testAllDialects(transactionSql, "事务控制语句");
            }
        }

        @Test
        @DisplayName("保存点语句测试")
        void testSavepointStatements() {
            // 根据MySQL 8.4官方文档15.3.4节，测试保存点语句
            String[] savepointStatements = {
                "SAVEPOINT sp1",
                "ROLLBACK TO SAVEPOINT sp1",
                "RELEASE SAVEPOINT sp1"
            };

            for (String savepointSql : savepointStatements) {
                testAllDialects(savepointSql, "保存点语句");
            }
        }

        @Test
        @DisplayName("锁定语句测试")
        void testLockingStatements() {
            // 根据MySQL 8.4官方文档15.3.6节，测试锁定语句
            String[] lockStatements = {
                "LOCK TABLES users READ",
                "LOCK TABLES users WRITE, departments READ",
                "UNLOCK TABLES"
            };

            for (String lockSql : lockStatements) {
                testAllDialects(lockSql, "锁定语句");
            }
        }

        @Test
        @DisplayName("事务隔离级别设置测试")
        void testTransactionIsolationLevel() {
            // 根据MySQL 8.4官方文档15.3.7节，测试SET TRANSACTION语句
            String[] isolationStatements = {
                "SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED",
                "SET TRANSACTION ISOLATION LEVEL READ COMMITTED",
                "SET TRANSACTION ISOLATION LEVEL REPEATABLE READ",
                "SET TRANSACTION ISOLATION LEVEL SERIALIZABLE",
                "SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED",
                "SET GLOBAL TRANSACTION ISOLATION LEVEL REPEATABLE READ"
            };

            for (String isolationSql : isolationStatements) {
                testAllDialects(isolationSql, "事务隔离级别");
            }
        }
    }

    @Nested
    @DisplayName("复合语句全覆盖测试 - 基于MySQL 8.4官方文档15.6节")
    class CompoundStatementsComprehensiveTests {

        @Test
        @DisplayName("存储过程创建测试")
        void testCreateProcedureStatement() {
            // 根据MySQL 8.4官方文档15.1.17节，测试CREATE PROCEDURE语句
            String mysqlSql = "CREATE PROCEDURE GetUsersByDepartment(" +
                    "IN dept_id INT, " +
                    "IN status_filter VARCHAR(20), " +
                    "OUT user_count INT" +
                    ") " +
                    "BEGIN " +
                    "  DECLARE done INT DEFAULT FALSE; " +
                    "  DECLARE user_name VARCHAR(100); " +
                    "  DECLARE user_cursor CURSOR FOR " +
                    "    SELECT name FROM users " +
                    "    WHERE department_id = dept_id AND status = status_filter; " +
                    "  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE; " +
                    "  " +
                    "  SET user_count = 0; " +
                    "  " +
                    "  OPEN user_cursor; " +
                    "  read_loop: LOOP " +
                    "    FETCH user_cursor INTO user_name; " +
                    "    IF done THEN " +
                    "      LEAVE read_loop; " +
                    "    END IF; " +
                    "    SET user_count = user_count + 1; " +
                    "  END LOOP; " +
                    "  CLOSE user_cursor; " +
                    "END";

            testAllDialects(mysqlSql, "存储过程创建");
        }

        @Test
        @DisplayName("函数创建测试")
        void testCreateFunctionStatement() {
            // 根据MySQL 8.4官方文档15.1.14节，测试CREATE FUNCTION语句
            String mysqlSql = "CREATE FUNCTION CalculateBonus(salary DECIMAL(10,2), performance_rating INT) " +
                    "RETURNS DECIMAL(10,2) " +
                    "DETERMINISTIC " +
                    "READS SQL DATA " +
                    "BEGIN " +
                    "  DECLARE bonus DECIMAL(10,2) DEFAULT 0; " +
                    "  " +
                    "  CASE performance_rating " +
                    "    WHEN 5 THEN SET bonus = salary * 0.2; " +
                    "    WHEN 4 THEN SET bonus = salary * 0.15; " +
                    "    WHEN 3 THEN SET bonus = salary * 0.1; " +
                    "    WHEN 2 THEN SET bonus = salary * 0.05; " +
                    "    ELSE SET bonus = 0; " +
                    "  END CASE; " +
                    "  " +
                    "  RETURN bonus; " +
                    "END";

            testAllDialects(mysqlSql, "函数创建");
        }

        @Test
        @DisplayName("流程控制语句测试")
        void testFlowControlStatements() {
            // 根据MySQL 8.4官方文档15.6.5节，测试流程控制语句
            String mysqlSql = "CREATE PROCEDURE FlowControlExample(IN input_value INT) " +
                    "BEGIN " +
                    "  DECLARE counter INT DEFAULT 0; " +
                    "  DECLARE result VARCHAR(100) DEFAULT ''; " +
                    "  " +
                    "  -- IF语句 " +
                    "  IF input_value > 100 THEN " +
                    "    SET result = 'High'; " +
                    "  ELSEIF input_value > 50 THEN " +
                    "    SET result = 'Medium'; " +
                    "  ELSE " +
                    "    SET result = 'Low'; " +
                    "  END IF; " +
                    "  " +
                    "  -- WHILE循环 " +
                    "  WHILE counter < input_value DO " +
                    "    SET counter = counter + 1; " +
                    "    IF counter = 10 THEN " +
                    "      ITERATE; " +
                    "    END IF; " +
                    "    IF counter > 20 THEN " +
                    "      LEAVE; " +
                    "    END IF; " +
                    "  END WHILE; " +
                    "  " +
                    "  -- REPEAT循环 " +
                    "  REPEAT " +
                    "    SET counter = counter - 1; " +
                    "  UNTIL counter <= 0 END REPEAT; " +
                    "  " +
                    "  -- LOOP循环 " +
                    "  loop_label: LOOP " +
                    "    SET counter = counter + 1; " +
                    "    IF counter > 5 THEN " +
                    "      LEAVE loop_label; " +
                    "    END IF; " +
                    "  END LOOP; " +
                    "END";

            testAllDialects(mysqlSql, "流程控制语句");
        }

        @Test
        @DisplayName("异常处理语句测试")
        void testExceptionHandlingStatements() {
            // 根据MySQL 8.4官方文档15.6.7节，测试异常处理语句
            String mysqlSql = "CREATE PROCEDURE ExceptionHandlingExample() " +
                    "BEGIN " +
                    "  DECLARE duplicate_key CONDITION FOR SQLSTATE '23000'; " +
                    "  DECLARE EXIT HANDLER FOR duplicate_key " +
                    "  BEGIN " +
                    "    ROLLBACK; " +
                    "    RESIGNAL SET MESSAGE_TEXT = 'Duplicate key error occurred'; " +
                    "  END; " +
                    "  " +
                    "  DECLARE CONTINUE HANDLER FOR SQLWARNING " +
                    "  BEGIN " +
                    "    GET DIAGNOSTICS CONDITION 1 " +
                    "      @sqlstate = RETURNED_SQLSTATE, " +
                    "      @errno = MYSQL_ERRNO, " +
                    "      @text = MESSAGE_TEXT; " +
                    "  END; " +
                    "  " +
                    "  START TRANSACTION; " +
                    "  INSERT INTO users (name, email) VALUES ('Test', '<EMAIL>'); " +
                    "  COMMIT; " +
                    "END";

            testAllDialects(mysqlSql, "异常处理语句");
        }
    }

    @Nested
    @DisplayName("管理和实用语句测试 - 基于MySQL 8.4官方文档15.7和15.8节")
    class AdministrativeStatementsComprehensiveTests {

        @Test
        @DisplayName("SHOW语句测试")
        void testShowStatements() {
            // 根据MySQL 8.4官方文档15.7.7节，测试SHOW语句
            String[] showStatements = {
                "SHOW DATABASES",
                "SHOW TABLES",
                "SHOW TABLES LIKE 'user%'",
                "SHOW COLUMNS FROM users",
                "SHOW INDEX FROM users",
                "SHOW CREATE TABLE users",
                "SHOW VARIABLES LIKE 'innodb%'",
                "SHOW STATUS LIKE 'Threads%'",
                "SHOW PROCESSLIST",
                "SHOW ENGINES",
                "SHOW WARNINGS",
                "SHOW ERRORS"
            };

            for (String showSql : showStatements) {
                testAllDialects(showSql, "SHOW语句");
            }
        }

        @Test
        @DisplayName("DESCRIBE语句测试")
        void testDescribeStatements() {
            // 根据MySQL 8.4官方文档15.8.1节，测试DESCRIBE语句
            String[] describeStatements = {
                "DESCRIBE users",
                "DESC users",
                "DESCRIBE users name",
                "EXPLAIN SELECT * FROM users WHERE id = 1"
            };

            for (String describeSql : describeStatements) {
                testAllDialects(describeSql, "DESCRIBE语句");
            }
        }

        @Test
        @DisplayName("USE语句测试")
        void testUseStatement() {
            // 根据MySQL 8.4官方文档15.8.4节，测试USE语句
            String mysqlSql = "USE test_database";
            testAllDialects(mysqlSql, "USE语句");
        }
    }

    /**
     * 测试所有数据库方言的转换
     */
    private void testAllDialects(String mysqlSql, String testType) {
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(mysqlSql);
        System.out.println("达梦" + testType + "转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (damengResult.isSuccess() ? damengResult.getTargetSql() : damengResult.getErrorMessage()));
        assertTrue(damengResult.isSuccess(), "达梦" + testType + "转换应该成功");
        assertNotNull(damengResult.getTargetSql(), "达梦转换结果不应为空");

        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(mysqlSql);
        System.out.println("金仓" + testType + "转换结果:");
        System.out.println("输出: " + (kingbaseResult.isSuccess() ? kingbaseResult.getTargetSql() : kingbaseResult.getErrorMessage()));
        assertTrue(kingbaseResult.isSuccess(), "金仓" + testType + "转换应该成功");
        assertNotNull(kingbaseResult.getTargetSql(), "金仓转换结果不应为空");

        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(mysqlSql);
        System.out.println("神通" + testType + "转换结果:");
        System.out.println("输出: " + (shentongResult.isSuccess() ? shentongResult.getTargetSql() : shentongResult.getErrorMessage()));
        assertTrue(shentongResult.isSuccess(), "神通" + testType + "转换应该成功");
        assertNotNull(shentongResult.getTargetSql(), "神通转换结果不应为空");

        System.out.println("----------------------------------------");
    }
}
