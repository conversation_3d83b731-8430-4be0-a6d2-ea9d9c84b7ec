package com.xylink.sqltranspiler.compliance.mysql;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;

/**
 * MySQL 8.4函数和操作符全覆盖测试
 * 
 * 严格遵循MySQL 8.4官方文档第14章：
 * - 官方文档：https://dev.mysql.com/doc/refman/8.4/en/functions.html
 * - 操作符：https://dev.mysql.com/doc/refman/8.4/en/operators.html
 * - 数值函数：https://dev.mysql.com/doc/refman/8.4/en/numeric-functions.html
 * - 字符串函数：https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
 * - 日期时间函数：https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
 * - 聚合函数：https://dev.mysql.com/doc/refman/8.4/en/aggregate-functions.html
 * - 窗口函数：https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
 * 
 * 根据rule-db.md规范：
 * - 所有测试基于MySQL 8.4官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * 1. 操作符（算术、比较、逻辑、赋值）
 * 2. 数值函数和操作符
 * 3. 字符串函数和操作符
 * 4. 日期时间函数
 * 5. 聚合函数
 * 6. 窗口函数
 * 7. 流程控制函数
 * 8. 信息函数
 * 9. 加密和压缩函数
 * 10. JSON函数
 */
@DisplayName("MySQL 8.4函数和操作符全覆盖测试")
public class MySQL84FunctionsOperatorsComprehensiveTest {

    private SqlTranspiler damengTranspiler;
    private SqlTranspiler kingbaseTranspiler;
    private SqlTranspiler shentongTranspiler;

    @BeforeEach
    void setUp() {
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
            
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
            
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(true)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("操作符全覆盖测试 - 基于MySQL 8.4官方文档14.4节")
    class OperatorsComprehensiveTests {
        
        @Test
        @DisplayName("算术操作符测试 - 基于MySQL 8.4官方文档14.6.1节")
        void testArithmeticOperators() {
            // 根据MySQL 8.4官方文档14.6.1节，测试所有算术操作符
            String mysqlSql = "SELECT " +
                    "10 + 5 AS addition, " +
                    "10 - 5 AS subtraction, " +
                    "10 * 5 AS multiplication, " +
                    "10 / 5 AS division, " +
                    "10 DIV 3 AS integer_division, " +
                    "10 % 3 AS modulo, " +
                    "10 MOD 3 AS mod_function, " +
                    "-10 AS unary_minus, " +
                    "+10 AS unary_plus " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "算术操作符");
        }

        @Test
        @DisplayName("比较操作符测试 - 基于MySQL 8.4官方文档14.4.2节")
        void testComparisonOperators() {
            // 根据MySQL 8.4官方文档14.4.2节，测试所有比较操作符
            String mysqlSql = "SELECT * FROM users WHERE " +
                    "age = 25 AND " +
                    "salary <> 0 AND " +
                    "salary != 0 AND " +
                    "salary <= 100000 AND " +
                    "salary >= 30000 AND " +
                    "salary < 200000 AND " +
                    "salary > 20000 AND " +
                    "name <=> 'John' AND " +
                    "status IS NULL AND " +
                    "email IS NOT NULL AND " +
                    "department IN ('IT', 'HR', 'Finance') AND " +
                    "age NOT IN (18, 65) AND " +
                    "name LIKE 'J%' AND " +
                    "name NOT LIKE '%admin%' AND " +
                    "description REGEXP '^[A-Z]' AND " +
                    "description NOT REGEXP '[0-9]$' AND " +
                    "salary BETWEEN 30000 AND 80000 AND " +
                    "age NOT BETWEEN 60 AND 70";

            testAllDialects(mysqlSql, "比较操作符");
        }

        @Test
        @DisplayName("逻辑操作符测试 - 基于MySQL 8.4官方文档14.4.3节")
        void testLogicalOperators() {
            // 根据MySQL 8.4官方文档14.4.3节，测试所有逻辑操作符
            String mysqlSql = "SELECT * FROM users WHERE " +
                    "(age > 18 AND age < 65) OR " +
                    "(status = 'VIP' AND NOT deleted) AND " +
                    "NOT (banned OR suspended) AND " +
                    "(active XOR premium) AND " +
                    "((department = 'IT' AND experience > 5) OR " +
                    " (department = 'HR' AND certification IS NOT NULL))";

            testAllDialects(mysqlSql, "逻辑操作符");
        }

        @Test
        @DisplayName("赋值操作符测试 - 基于MySQL 8.4官方文档14.4.4节")
        void testAssignmentOperators() {
            // 根据MySQL 8.4官方文档14.4.4节，测试赋值操作符
            String mysqlSql = "UPDATE users SET " +
                    "salary = 50000, " +
                    "bonus := salary * 0.1, " +
                    "total_compensation = salary + bonus " +
                    "WHERE department = 'Engineering'";

            testAllDialects(mysqlSql, "赋值操作符");
        }
    }

    @Nested
    @DisplayName("数值函数全覆盖测试 - 基于MySQL 8.4官方文档14.6节")
    class NumericFunctionsComprehensiveTests {
        
        @Test
        @DisplayName("数学函数测试 - 基于MySQL 8.4官方文档14.6.2节")
        void testMathematicalFunctions() {
            // 根据MySQL 8.4官方文档14.6.2节，测试所有数学函数
            String mysqlSql = "SELECT " +
                    "ABS(-10) AS absolute_value, " +
                    "ACOS(0.5) AS arc_cosine, " +
                    "ASIN(0.5) AS arc_sine, " +
                    "ATAN(1) AS arc_tangent, " +
                    "ATAN2(1, 1) AS arc_tangent2, " +
                    "CEIL(4.3) AS ceiling, " +
                    "CEILING(4.3) AS ceiling_alias, " +
                    "COS(PI()) AS cosine, " +
                    "COT(1) AS cotangent, " +
                    "DEGREES(PI()) AS degrees, " +
                    "EXP(1) AS exponential, " +
                    "FLOOR(4.7) AS floor_value, " +
                    "LN(2.718281828) AS natural_log, " +
                    "LOG(10) AS logarithm, " +
                    "LOG10(100) AS log_base_10, " +
                    "LOG2(8) AS log_base_2, " +
                    "MOD(10, 3) AS modulo_function, " +
                    "PI() AS pi_value, " +
                    "POW(2, 3) AS power, " +
                    "POWER(2, 3) AS power_alias, " +
                    "RADIANS(180) AS radians, " +
                    "RAND() AS random_value, " +
                    "ROUND(4.567, 2) AS round_value, " +
                    "SIGN(-10) AS sign_value, " +
                    "SIN(PI()/2) AS sine, " +
                    "SQRT(16) AS square_root, " +
                    "TAN(PI()/4) AS tangent, " +
                    "TRUNCATE(4.567, 2) AS truncate_value " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "数学函数");
        }

        @Test
        @DisplayName("数值处理函数测试")
        void testNumericProcessingFunctions() {
            // 根据MySQL 8.4官方文档，测试数值处理相关函数
            String mysqlSql = "SELECT " +
                    "CONV('A', 16, 10) AS convert_base, " +
                    "FORMAT(1234567.89, 2) AS format_number, " +
                    "HEX(255) AS hexadecimal, " +
                    "OCT(8) AS octal, " +
                    "BIN(10) AS binary, " +
                    "GREATEST(1, 5, 3, 9, 2) AS greatest_value, " +
                    "LEAST(1, 5, 3, 9, 2) AS least_value " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "数值处理函数");
        }
    }

    @Nested
    @DisplayName("字符串函数全覆盖测试 - 基于MySQL 8.4官方文档14.8节")
    class StringFunctionsComprehensiveTests {
        
        @Test
        @DisplayName("字符串操作函数测试 - 基于MySQL 8.4官方文档14.8节")
        void testStringManipulationFunctions() {
            // 根据MySQL 8.4官方文档14.8节，测试字符串操作函数
            String mysqlSql = "SELECT " +
                    "ASCII('A') AS ascii_value, " +
                    "BIN(10) AS binary_string, " +
                    "BIT_LENGTH('hello') AS bit_length, " +
                    "CHAR(65, 66, 67) AS char_from_ascii, " +
                    "CHAR_LENGTH('hello') AS char_length, " +
                    "CHARACTER_LENGTH('hello') AS character_length, " +
                    "CONCAT('Hello', ' ', 'World') AS concatenation, " +
                    "CONCAT_WS('-', 'A', 'B', 'C') AS concat_with_separator, " +
                    "ELT(2, 'first', 'second', 'third') AS element_at_index, " +
                    "EXPORT_SET(5, 'Y', 'N', ',', 4) AS export_set, " +
                    "FIELD('B', 'A', 'B', 'C') AS field_position, " +
                    "FIND_IN_SET('B', 'A,B,C,D') AS find_in_set, " +
                    "HEX('MySQL') AS hex_string, " +
                    "INSERT('Hello World', 7, 5, 'MySQL') AS insert_string, " +
                    "INSTR('Hello World', 'World') AS instr_position, " +
                    "LCASE('HELLO') AS lowercase, " +
                    "LEFT('Hello World', 5) AS left_substring, " +
                    "LENGTH('Hello') AS byte_length, " +
                    "LOAD_FILE('/tmp/test.txt') AS load_file_content, " +
                    "LOCATE('World', 'Hello World') AS locate_position, " +
                    "LOWER('HELLO') AS lower_case, " +
                    "LPAD('hi', 5, '?') AS left_pad, " +
                    "LTRIM('  hello  ') AS left_trim " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "字符串操作函数");
        }

        @Test
        @DisplayName("字符串处理函数测试")
        void testStringProcessingFunctions() {
            // 根据MySQL 8.4官方文档，测试更多字符串处理函数
            String mysqlSql = "SELECT " +
                    "MAKE_SET(1|4|16, 'hello', 'nice', 'world') AS make_set, " +
                    "MID('Hello World', 7, 5) AS mid_substring, " +
                    "OCT(12) AS octal_string, " +
                    "OCTET_LENGTH('hello') AS octet_length, " +
                    "ORD('A') AS ord_value, " +
                    "POSITION('World' IN 'Hello World') AS position_in, " +
                    "QUOTE('Don\\'t') AS quote_string, " +
                    "REPEAT('MySQL', 3) AS repeat_string, " +
                    "REPLACE('Hello World', 'World', 'MySQL') AS replace_string, " +
                    "REVERSE('Hello') AS reverse_string, " +
                    "RIGHT('Hello World', 5) AS right_substring, " +
                    "RPAD('hi', 5, '?') AS right_pad, " +
                    "RTRIM('  hello  ') AS right_trim, " +
                    "SOUNDEX('Hello') AS soundex_value, " +
                    "SPACE(5) AS space_string, " +
                    "STRCMP('text1', 'text2') AS string_compare, " +
                    "SUBSTR('Hello World', 7) AS substr_function, " +
                    "SUBSTRING('Hello World', 7, 5) AS substring_function, " +
                    "SUBSTRING_INDEX('www.mysql.com', '.', 2) AS substring_index, " +
                    "TRIM('  hello  ') AS trim_spaces, " +
                    "UCASE('hello') AS uppercase, " +
                    "UNHEX('4D7953514C') AS unhex_string, " +
                    "UPPER('hello') AS upper_case " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "字符串处理函数");
        }

        @Test
        @DisplayName("正则表达式函数测试 - 基于MySQL 8.4官方文档14.8.2节")
        void testRegularExpressionFunctions() {
            // 根据MySQL 8.4官方文档14.8.2节，测试正则表达式函数
            String mysqlSql = "SELECT " +
                    "name, " +
                    "email, " +
                    "REGEXP_LIKE(email, '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$') AS valid_email, " +
                    "REGEXP_REPLACE(phone, '[^0-9]', '') AS clean_phone, " +
                    "REGEXP_SUBSTR(description, '[A-Z][a-z]+') AS first_word, " +
                    "REGEXP_INSTR(content, 'MySQL', 1, 1, 0) AS mysql_position " +
                    "FROM users " +
                    "WHERE name REGEXP '^[A-Z][a-z]+ [A-Z][a-z]+$'";

            testAllDialects(mysqlSql, "正则表达式函数");
        }
    }

    @Nested
    @DisplayName("日期时间函数全覆盖测试 - 基于MySQL 8.4官方文档14.7节")
    class DateTimeFunctionsComprehensiveTests {
        
        @Test
        @DisplayName("日期时间获取函数测试 - 基于MySQL 8.4官方文档14.7节")
        void testDateTimeRetrievalFunctions() {
            // 根据MySQL 8.4官方文档14.7节，测试日期时间获取函数
            String mysqlSql = "SELECT " +
                    "ADDDATE('2024-01-01', INTERVAL 30 DAY) AS add_date, " +
                    "ADDTIME('2024-01-01 10:00:00', '02:30:00') AS add_time, " +
                    "CONVERT_TZ('2024-01-01 12:00:00', '+00:00', '+08:00') AS convert_timezone, " +
                    "CURDATE() AS current_date, " +
                    "CURRENT_DATE() AS current_date_func, " +
                    "CURRENT_TIME() AS current_time_func, " +
                    "CURRENT_TIMESTAMP() AS current_timestamp_func, " +
                    "CURTIME() AS current_time, " +
                    "DATE('2024-01-01 12:30:45') AS extract_date, " +
                    "DATEDIFF('2024-12-31', '2024-01-01') AS date_difference, " +
                    "DATE_ADD('2024-01-01', INTERVAL 1 MONTH) AS date_add, " +
                    "DATE_FORMAT('2024-01-01', '%Y-%m-%d') AS date_format, " +
                    "DATE_SUB('2024-01-01', INTERVAL 1 WEEK) AS date_subtract, " +
                    "DAY('2024-01-15') AS day_of_month, " +
                    "DAYNAME('2024-01-01') AS day_name, " +
                    "DAYOFMONTH('2024-01-15') AS day_of_month_func, " +
                    "DAYOFWEEK('2024-01-01') AS day_of_week, " +
                    "DAYOFYEAR('2024-01-01') AS day_of_year, " +
                    "EXTRACT(YEAR FROM '2024-01-01') AS extract_year, " +
                    "FROM_DAYS(738156) AS from_days, " +
                    "FROM_UNIXTIME(1640995200) AS from_unix_time, " +
                    "GET_FORMAT(DATE, 'USA') AS get_format, " +
                    "HOUR('12:30:45') AS extract_hour, " +
                    "LAST_DAY('2024-02-15') AS last_day_of_month " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "日期时间获取函数");
        }

        @Test
        @DisplayName("日期时间计算函数测试")
        void testDateTimeCalculationFunctions() {
            // 根据MySQL 8.4官方文档，测试日期时间计算函数
            String mysqlSql = "SELECT " +
                    "MAKEDATE(2024, 100) AS make_date, " +
                    "MAKETIME(12, 30, 45) AS make_time, " +
                    "MICROSECOND('12:30:45.123456') AS extract_microsecond, " +
                    "MINUTE('12:30:45') AS extract_minute, " +
                    "MONTH('2024-01-15') AS extract_month, " +
                    "MONTHNAME('2024-01-01') AS month_name, " +
                    "NOW() AS current_datetime, " +
                    "PERIOD_ADD(202401, 3) AS period_add, " +
                    "PERIOD_DIFF(202404, 202401) AS period_diff, " +
                    "QUARTER('2024-07-15') AS extract_quarter, " +
                    "SEC_TO_TIME(3661) AS seconds_to_time, " +
                    "SECOND('12:30:45') AS extract_second, " +
                    "STR_TO_DATE('01,5,2024', '%d,%m,%Y') AS string_to_date, " +
                    "SUBDATE('2024-01-31', INTERVAL 1 MONTH) AS subtract_date, " +
                    "SUBTIME('12:30:45', '01:15:30') AS subtract_time, " +
                    "SYSDATE() AS system_date, " +
                    "TIME('2024-01-01 12:30:45') AS extract_time, " +
                    "TIME_FORMAT('12:30:45', '%H:%i:%s') AS time_format, " +
                    "TIME_TO_SEC('01:30:45') AS time_to_seconds, " +
                    "TIMEDIFF('12:30:45', '10:15:30') AS time_difference, " +
                    "TIMESTAMP('2024-01-01', '12:30:45') AS create_timestamp, " +
                    "TIMESTAMPADD(HOUR, 2, '2024-01-01 10:00:00') AS timestamp_add, " +
                    "TIMESTAMPDIFF(DAY, '2024-01-01', '2024-01-31') AS timestamp_diff, " +
                    "TO_DAYS('2024-01-01') AS to_days, " +
                    "UNIX_TIMESTAMP('2024-01-01 00:00:00') AS unix_timestamp, " +
                    "UTC_DATE() AS utc_date, " +
                    "UTC_TIME() AS utc_time, " +
                    "UTC_TIMESTAMP() AS utc_timestamp, " +
                    "WEEK('2024-01-01') AS week_of_year, " +
                    "WEEKDAY('2024-01-01') AS weekday, " +
                    "WEEKOFYEAR('2024-01-01') AS week_of_year_func, " +
                    "YEAR('2024-01-01') AS extract_year_func, " +
                    "YEARWEEK('2024-01-01') AS year_week " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "日期时间计算函数");
        }
    }

    @Nested
    @DisplayName("聚合函数全覆盖测试 - 基于MySQL 8.4官方文档14.19节")
    class AggregateFunctionsComprehensiveTests {

        @Test
        @DisplayName("基础聚合函数测试 - 基于MySQL 8.4官方文档14.19.1节")
        void testBasicAggregateFunctions() {
            // 根据MySQL 8.4官方文档14.19.1节，测试基础聚合函数
            String mysqlSql = "SELECT " +
                    "COUNT(*) AS total_count, " +
                    "COUNT(DISTINCT department) AS unique_departments, " +
                    "SUM(salary) AS total_salary, " +
                    "AVG(salary) AS average_salary, " +
                    "MIN(salary) AS minimum_salary, " +
                    "MAX(salary) AS maximum_salary, " +
                    "STDDEV(salary) AS standard_deviation, " +
                    "STDDEV_POP(salary) AS population_stddev, " +
                    "STDDEV_SAMP(salary) AS sample_stddev, " +
                    "VARIANCE(salary) AS variance, " +
                    "VAR_POP(salary) AS population_variance, " +
                    "VAR_SAMP(salary) AS sample_variance " +
                    "FROM employees " +
                    "GROUP BY department";

            testAllDialects(mysqlSql, "基础聚合函数");
        }

        @Test
        @DisplayName("高级聚合函数测试")
        void testAdvancedAggregateFunctions() {
            // 根据MySQL 8.4官方文档，测试高级聚合函数
            String mysqlSql = "SELECT " +
                    "department, " +
                    "GROUP_CONCAT(name ORDER BY salary DESC SEPARATOR ', ') AS employee_list, " +
                    "GROUP_CONCAT(DISTINCT skill ORDER BY skill SEPARATOR '|') AS skills, " +
                    "BIT_AND(permissions) AS common_permissions, " +
                    "BIT_OR(permissions) AS all_permissions, " +
                    "BIT_XOR(permissions) AS xor_permissions, " +
                    "JSON_ARRAYAGG(name) AS names_array, " +
                    "JSON_OBJECTAGG(name, salary) AS name_salary_object " +
                    "FROM employees " +
                    "GROUP BY department " +
                    "WITH ROLLUP";

            testAllDialects(mysqlSql, "高级聚合函数");
        }
    }

    @Nested
    @DisplayName("窗口函数全覆盖测试 - 基于MySQL 8.4官方文档14.20节")
    class WindowFunctionsComprehensiveTests {

        @Test
        @DisplayName("排名窗口函数测试 - 基于MySQL 8.4官方文档14.20.1节")
        void testRankingWindowFunctions() {
            // 根据MySQL 8.4官方文档14.20.1节，测试排名窗口函数
            String mysqlSql = "SELECT " +
                    "name, " +
                    "department, " +
                    "salary, " +
                    "ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) AS row_num, " +
                    "RANK() OVER (PARTITION BY department ORDER BY salary DESC) AS rank_val, " +
                    "DENSE_RANK() OVER (PARTITION BY department ORDER BY salary DESC) AS dense_rank_val, " +
                    "PERCENT_RANK() OVER (PARTITION BY department ORDER BY salary) AS percent_rank_val, " +
                    "CUME_DIST() OVER (PARTITION BY department ORDER BY salary) AS cumulative_dist, " +
                    "NTILE(4) OVER (PARTITION BY department ORDER BY salary DESC) AS quartile " +
                    "FROM employees " +
                    "ORDER BY department, salary DESC";

            testAllDialects(mysqlSql, "排名窗口函数");
        }

        @Test
        @DisplayName("值窗口函数测试")
        void testValueWindowFunctions() {
            // 根据MySQL 8.4官方文档，测试值窗口函数
            String mysqlSql = "SELECT " +
                    "name, " +
                    "department, " +
                    "salary, " +
                    "hire_date, " +
                    "LAG(salary, 1) OVER (PARTITION BY department ORDER BY hire_date) AS prev_salary, " +
                    "LEAD(salary, 1) OVER (PARTITION BY department ORDER BY hire_date) AS next_salary, " +
                    "FIRST_VALUE(salary) OVER (PARTITION BY department ORDER BY hire_date " +
                    "    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS first_salary, " +
                    "LAST_VALUE(salary) OVER (PARTITION BY department ORDER BY hire_date " +
                    "    ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) AS last_salary, " +
                    "NTH_VALUE(salary, 2) OVER (PARTITION BY department ORDER BY hire_date " +
                    "    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS second_salary " +
                    "FROM employees " +
                    "ORDER BY department, hire_date";

            testAllDialects(mysqlSql, "值窗口函数");
        }

        @Test
        @DisplayName("聚合窗口函数测试")
        void testAggregateWindowFunctions() {
            // 根据MySQL 8.4官方文档，测试聚合窗口函数
            String mysqlSql = "SELECT " +
                    "name, " +
                    "department, " +
                    "salary, " +
                    "SUM(salary) OVER (PARTITION BY department) AS dept_total_salary, " +
                    "AVG(salary) OVER (PARTITION BY department) AS dept_avg_salary, " +
                    "COUNT(*) OVER (PARTITION BY department) AS dept_employee_count, " +
                    "SUM(salary) OVER (PARTITION BY department ORDER BY hire_date " +
                    "    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS running_total, " +
                    "AVG(salary) OVER (PARTITION BY department ORDER BY hire_date " +
                    "    ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING) AS moving_avg " +
                    "FROM employees " +
                    "ORDER BY department, hire_date";

            testAllDialects(mysqlSql, "聚合窗口函数");
        }
    }

    @Nested
    @DisplayName("流程控制函数全覆盖测试 - 基于MySQL 8.4官方文档14.5节")
    class FlowControlFunctionsComprehensiveTests {

        @Test
        @DisplayName("条件函数测试 - 基于MySQL 8.4官方文档14.5节")
        void testConditionalFunctions() {
            // 根据MySQL 8.4官方文档14.5节，测试流程控制函数
            String mysqlSql = "SELECT " +
                    "name, " +
                    "salary, " +
                    "department, " +
                    "CASE " +
                    "    WHEN salary > 80000 THEN 'High' " +
                    "    WHEN salary > 50000 THEN 'Medium' " +
                    "    ELSE 'Low' " +
                    "END AS salary_level, " +
                    "IF(salary > 60000, 'Senior', 'Junior') AS seniority, " +
                    "IFNULL(bonus, 0) AS bonus_amount, " +
                    "ISNULL(commission) AS no_commission, " +
                    "NULLIF(department, 'Unknown') AS valid_department, " +
                    "COALESCE(phone, mobile, 'No Contact') AS contact_number " +
                    "FROM employees";

            testAllDialects(mysqlSql, "条件函数");
        }
    }

    @Nested
    @DisplayName("信息函数全覆盖测试 - 基于MySQL 8.4官方文档14.15节")
    class InformationFunctionsComprehensiveTests {

        @Test
        @DisplayName("系统信息函数测试 - 基于MySQL 8.4官方文档14.15节")
        void testSystemInformationFunctions() {
            // 根据MySQL 8.4官方文档14.15节，测试信息函数
            String mysqlSql = "SELECT " +
                    "BENCHMARK(1000000, MD5('test')) AS benchmark_result, " +
                    "CHARSET('string') AS character_set, " +
                    "COERCIBILITY('string') AS coercibility, " +
                    "COLLATION('string') AS collation, " +
                    "CONNECTION_ID() AS connection_id, " +
                    "CURRENT_USER() AS current_user, " +
                    "DATABASE() AS current_database, " +
                    "FOUND_ROWS() AS found_rows, " +
                    "LAST_INSERT_ID() AS last_insert_id, " +
                    "ROW_COUNT() AS affected_rows, " +
                    "SCHEMA() AS current_schema, " +
                    "SESSION_USER() AS session_user, " +
                    "SYSTEM_USER() AS system_user, " +
                    "USER() AS user_info, " +
                    "VERSION() AS mysql_version " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "系统信息函数");
        }
    }

    @Nested
    @DisplayName("加密和压缩函数全覆盖测试 - 基于MySQL 8.4官方文档14.13节")
    class EncryptionCompressionFunctionsComprehensiveTests {

        @Test
        @DisplayName("加密函数测试 - 基于MySQL 8.4官方文档14.13节")
        void testEncryptionFunctions() {
            // 根据MySQL 8.4官方文档14.13节，测试加密函数
            String mysqlSql = "SELECT " +
                    "AES_ENCRYPT('data', 'key') AS aes_encrypted, " +
                    "AES_DECRYPT(AES_ENCRYPT('data', 'key'), 'key') AS aes_decrypted, " +
                    "MD5('password') AS md5_hash, " +
                    "SHA1('password') AS sha1_hash, " +
                    "SHA2('password', 256) AS sha256_hash, " +
                    "PASSWORD('secret') AS mysql_password, " +
                    "RANDOM_BYTES(16) AS random_bytes, " +
                    "COMPRESS('data to compress') AS compressed_data, " +
                    "UNCOMPRESS(COMPRESS('data to compress')) AS uncompressed_data, " +
                    "UNCOMPRESSED_LENGTH(COMPRESS('data to compress')) AS original_length " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "加密函数");
        }
    }

    @Nested
    @DisplayName("JSON函数全覆盖测试 - 基于MySQL 8.4官方文档14.17节")
    class JsonFunctionsComprehensiveTests {

        @Test
        @DisplayName("JSON创建和修改函数测试 - 基于MySQL 8.4官方文档14.17节")
        void testJsonCreationModificationFunctions() {
            // 根据MySQL 8.4官方文档14.17节，测试JSON函数
            String mysqlSql = "SELECT " +
                    "JSON_ARRAY(1, 2, 3, 'four') AS json_array, " +
                    "JSON_OBJECT('name', 'John', 'age', 30) AS json_object, " +
                    "JSON_QUOTE('string with \"quotes\"') AS json_quoted, " +
                    "JSON_MERGE_PATCH('{\"a\": 1}', '{\"b\": 2}') AS json_merged, " +
                    "JSON_MERGE_PRESERVE('{\"a\": [1]}', '{\"a\": [2]}') AS json_preserved, " +
                    "JSON_INSERT('{\"a\": 1}', '$.b', 2) AS json_inserted, " +
                    "JSON_REPLACE('{\"a\": 1, \"b\": 2}', '$.a', 10) AS json_replaced, " +
                    "JSON_SET('{\"a\": 1}', '$.b', 2, '$.a', 10) AS json_set, " +
                    "JSON_REMOVE('{\"a\": 1, \"b\": 2}', '$.b') AS json_removed " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "JSON创建和修改函数");
        }

        @Test
        @DisplayName("JSON查询函数测试")
        void testJsonQueryFunctions() {
            // 根据MySQL 8.4官方文档，测试JSON查询函数
            String mysqlSql = "SELECT " +
                    "id, " +
                    "profile, " +
                    "JSON_EXTRACT(profile, '$.name') AS name, " +
                    "profile->>'$.email' AS email, " +
                    "profile->'$.skills' AS skills, " +
                    "JSON_UNQUOTE(JSON_EXTRACT(profile, '$.name')) AS unquoted_name, " +
                    "JSON_CONTAINS(profile, '\"MySQL\"', '$.skills') AS has_mysql_skill, " +
                    "JSON_CONTAINS_PATH(profile, 'one', '$.email', '$.phone') AS has_contact, " +
                    "JSON_KEYS(profile) AS profile_keys, " +
                    "JSON_LENGTH(profile, '$.skills') AS skills_count, " +
                    "JSON_TYPE(profile->'$.age') AS age_type, " +
                    "JSON_VALID(profile) AS is_valid_json, " +
                    "JSON_SEARCH(profile, 'one', 'John%') AS search_result " +
                    "FROM users " +
                    "WHERE JSON_EXTRACT(profile, '$.active') = true";

            testAllDialects(mysqlSql, "JSON查询函数");
        }
    }

    @Nested
    @DisplayName("类型转换函数全覆盖测试 - 基于MySQL 8.4官方文档14.10节")
    class CastConversionFunctionsComprehensiveTests {

        @Test
        @DisplayName("类型转换函数测试 - 基于MySQL 8.4官方文档14.10节")
        void testCastConversionFunctions() {
            // 根据MySQL 8.4官方文档14.10节，测试类型转换函数
            String mysqlSql = "SELECT " +
                    "CAST('123' AS SIGNED) AS cast_to_signed, " +
                    "CAST('123' AS UNSIGNED) AS cast_to_unsigned, " +
                    "CAST('123.45' AS DECIMAL(5,2)) AS cast_to_decimal, " +
                    "CAST('2024-01-01' AS DATE) AS cast_to_date, " +
                    "CAST('12:30:45' AS TIME) AS cast_to_time, " +
                    "CAST('2024-01-01 12:30:45' AS DATETIME) AS cast_to_datetime, " +
                    "CAST('hello' AS CHAR(10)) AS cast_to_char, " +
                    "CAST('hello' AS BINARY(10)) AS cast_to_binary, " +
                    "CAST('{\"a\": 1}' AS JSON) AS cast_to_json, " +
                    "CONVERT('123', SIGNED) AS convert_to_signed, " +
                    "CONVERT('hello' USING utf8mb4) AS convert_charset " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "类型转换函数");
        }
    }

    @Nested
    @DisplayName("XML函数全覆盖测试 - 基于MySQL 8.4官方文档14.11节")
    class XmlFunctionsComprehensiveTests {

        @Test
        @DisplayName("XML函数测试 - 基于MySQL 8.4官方文档14.11节")
        void testXmlFunctions() {
            // 根据MySQL 8.4官方文档14.11节，测试XML函数
            String mysqlSql = "SELECT " +
                    "ExtractValue('<a><b>Hello</b><c>World</c></a>', '//b') AS extract_value, " +
                    "ExtractValue('<a><b>X</b><b>Y</b></a>', '//b[2]') AS extract_second, " +
                    "ExtractValue('<a><b id=\"1\">First</b><b id=\"2\">Second</b></a>', '//b[@id=\"2\"]') AS extract_by_attr, " +
                    "UpdateXML('<a><b>old</b></a>', '//b', '<b>new</b>') AS update_xml, " +
                    "UpdateXML('<a><b>X</b><c>Y</c></a>', '//c', '<c>Z</c>') AS update_element " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "XML函数");
        }

        @Test
        @DisplayName("XML XPath表达式测试")
        void testXmlXPathExpressions() {
            // 根据MySQL 8.4官方文档，测试复杂的XPath表达式
            String mysqlSql = "SELECT " +
                    "ExtractValue('<root><item>1</item><item>2</item><item>3</item></root>', 'count(//item)') AS count_items, " +
                    "ExtractValue('<data><user name=\"John\" age=\"30\"/><user name=\"Jane\" age=\"25\"/></data>', '//user[@age>\"27\"]/@name') AS filter_by_age, " +
                    "ExtractValue('<books><book><title>MySQL</title><author>Oracle</author></book></books>', '//book/title/text()') AS extract_text, " +
                    "UpdateXML('<config><setting name=\"debug\">false</setting></config>', '//setting[@name=\"debug\"]', '<setting name=\"debug\">true</setting>') AS update_config " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "XML XPath表达式");
        }
    }

    @Nested
    @DisplayName("位函数和操作符全覆盖测试 - 基于MySQL 8.4官方文档14.12节")
    class BitFunctionsOperatorsComprehensiveTests {

        @Test
        @DisplayName("位操作符测试 - 基于MySQL 8.4官方文档14.12节")
        void testBitOperators() {
            // 根据MySQL 8.4官方文档14.12节，测试位操作符
            String mysqlSql = "SELECT " +
                    "29 | 15 AS bitwise_or, " +
                    "29 & 15 AS bitwise_and, " +
                    "29 ^ 15 AS bitwise_xor, " +
                    "~29 AS bitwise_not, " +
                    "1 << 4 AS left_shift, " +
                    "16 >> 2 AS right_shift, " +
                    "BIT_COUNT(29) AS bit_count, " +
                    "BIT_COUNT(255) AS bit_count_255, " +
                    "BIT_COUNT(0) AS bit_count_zero " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "位操作符");
        }

        @Test
        @DisplayName("二进制字符串位操作测试")
        void testBinaryStringBitOperations() {
            // 根据MySQL 8.4官方文档，测试二进制字符串的位操作
            String mysqlSql = "SELECT " +
                    "HEX(_binary X'FF' & _binary X'0F') AS binary_and, " +
                    "HEX(_binary X'F0' | _binary X'0F') AS binary_or, " +
                    "HEX(_binary X'FF' ^ _binary X'0F') AS binary_xor, " +
                    "HEX(~_binary X'0F') AS binary_not, " +
                    "HEX(_binary X'01' << 4) AS binary_left_shift, " +
                    "HEX(_binary X'F0' >> 4) AS binary_right_shift " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "二进制字符串位操作");
        }
    }

    @Nested
    @DisplayName("锁定函数全覆盖测试 - 基于MySQL 8.4官方文档14.14节")
    class LockingFunctionsComprehensiveTests {

        @Test
        @DisplayName("锁定函数测试 - 基于MySQL 8.4官方文档14.14节")
        void testLockingFunctions() {
            // 根据MySQL 8.4官方文档14.14节，测试锁定函数
            String mysqlSql = "SELECT " +
                    "GET_LOCK('test_lock', 10) AS get_lock_result, " +
                    "IS_FREE_LOCK('test_lock') AS is_free_lock, " +
                    "IS_USED_LOCK('test_lock') AS is_used_lock, " +
                    "RELEASE_LOCK('test_lock') AS release_lock_result, " +
                    "RELEASE_ALL_LOCKS() AS release_all_locks " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "锁定函数");
        }
    }

    @Nested
    @DisplayName("杂项函数全覆盖测试 - 基于MySQL 8.4官方文档14.23节")
    class MiscellaneousFunctionsComprehensiveTests {

        @Test
        @DisplayName("杂项函数测试 - 基于MySQL 8.4官方文档14.23节")
        void testMiscellaneousFunctions() {
            // 根据MySQL 8.4官方文档14.23节，测试杂项函数
            String mysqlSql = "SELECT " +
                    "DEFAULT(id) AS default_value, " +
                    "INET_ATON('192.168.1.1') AS inet_aton, " +
                    "INET_NTOA(3232235777) AS inet_ntoa, " +
                    "IS_UUID('6ccd780c-baba-1026-9564-5b8c656024db') AS is_uuid_valid, " +
                    "IS_UUID('invalid-uuid') AS is_uuid_invalid, " +
                    "NAME_CONST('myname', 'myvalue') AS name_const, " +
                    "SLEEP(0.1) AS sleep_result, " +
                    "UUID() AS generate_uuid, " +
                    "UUID_SHORT() AS generate_uuid_short, " +
                    "VALUES(name) AS values_function " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "杂项函数");
        }

        @Test
        @DisplayName("UUID函数测试")
        void testUuidFunctions() {
            // 根据MySQL 8.4官方文档，测试UUID相关函数
            String mysqlSql = "SELECT " +
                    "UUID() AS new_uuid, " +
                    "UUID_SHORT() AS short_uuid, " +
                    "UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db') AS uuid_to_bin, " +
                    "BIN_TO_UUID(UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db')) AS bin_to_uuid, " +
                    "UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db', 1) AS uuid_to_bin_swap, " +
                    "BIN_TO_UUID(UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db', 1), 1) AS bin_to_uuid_swap " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "UUID函数");
        }
    }

    @Nested
    @DisplayName("全文搜索函数全覆盖测试 - 基于MySQL 8.4官方文档14.9节")
    class FullTextSearchFunctionsComprehensiveTests {

        @Test
        @DisplayName("全文搜索函数测试 - 基于MySQL 8.4官方文档14.9节")
        void testFullTextSearchFunctions() {
            // 根据MySQL 8.4官方文档14.9节，测试全文搜索函数
            String mysqlSql = "SELECT " +
                    "id, " +
                    "title, " +
                    "content, " +
                    "MATCH(title, content) AGAINST('database') AS relevance_score, " +
                    "MATCH(title, content) AGAINST('database mysql') AS multi_word_score, " +
                    "MATCH(title, content) AGAINST('+database -oracle' IN BOOLEAN MODE) AS boolean_search, " +
                    "MATCH(title, content) AGAINST('\"mysql database\"' IN BOOLEAN MODE) AS phrase_search, " +
                    "MATCH(title, content) AGAINST('database*' IN BOOLEAN MODE) AS wildcard_search, " +
                    "MATCH(title, content) AGAINST('database' WITH QUERY EXPANSION) AS query_expansion " +
                    "FROM articles " +
                    "WHERE MATCH(title, content) AGAINST('database')";

            testAllDialects(mysqlSql, "全文搜索函数");
        }

        @Test
        @DisplayName("布尔模式全文搜索测试")
        void testBooleanModeFullTextSearch() {
            // 根据MySQL 8.4官方文档，测试布尔模式全文搜索
            String mysqlSql = "SELECT " +
                    "title, " +
                    "MATCH(title, content) AGAINST('+mysql +database' IN BOOLEAN MODE) AS must_contain_both, " +
                    "MATCH(title, content) AGAINST('mysql database' IN BOOLEAN MODE) AS contain_either, " +
                    "MATCH(title, content) AGAINST('+mysql -oracle' IN BOOLEAN MODE) AS must_not_contain, " +
                    "MATCH(title, content) AGAINST('mysql >database' IN BOOLEAN MODE) AS increase_relevance, " +
                    "MATCH(title, content) AGAINST('mysql <database' IN BOOLEAN MODE) AS decrease_relevance, " +
                    "MATCH(title, content) AGAINST('~mysql' IN BOOLEAN MODE) AS negation_operator, " +
                    "MATCH(title, content) AGAINST('\"mysql database\"' IN BOOLEAN MODE) AS exact_phrase, " +
                    "MATCH(title, content) AGAINST('data*' IN BOOLEAN MODE) AS prefix_search " +
                    "FROM articles";

            testAllDialects(mysqlSql, "布尔模式全文搜索");
        }
    }

    @Nested
    @DisplayName("空间分析函数全覆盖测试 - 基于MySQL 8.4官方文档14.16节")
    class SpatialAnalysisFunctionsComprehensiveTests {

        @Test
        @DisplayName("空间几何创建函数测试 - 基于MySQL 8.4官方文档14.16节")
        void testSpatialGeometryCreationFunctions() {
            // 根据MySQL 8.4官方文档14.16节，测试空间几何创建函数
            String mysqlSql = "SELECT " +
                    "ST_GeomFromText('POINT(1 1)') AS point_from_wkt, " +
                    "ST_GeomFromWKB(ST_AsBinary(ST_GeomFromText('POINT(1 1)'))) AS point_from_wkb, " +
                    "ST_PointFromText('POINT(1 1)') AS point_from_text, " +
                    "ST_LineFromText('LINESTRING(0 0, 1 1, 2 2)') AS line_from_text, " +
                    "ST_PolygonFromText('POLYGON((0 0, 0 1, 1 1, 1 0, 0 0))') AS polygon_from_text, " +
                    "ST_GeomCollFromText('GEOMETRYCOLLECTION(POINT(1 1), LINESTRING(0 0, 1 1))') AS geom_coll_from_text, " +
                    "Point(1, 2) AS point_constructor, " +
                    "LineString(Point(0, 0), Point(1, 1)) AS linestring_constructor, " +
                    "Polygon(LineString(Point(0, 0), Point(0, 1), Point(1, 1), Point(1, 0), Point(0, 0))) AS polygon_constructor " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "空间几何创建函数");
        }

        @Test
        @DisplayName("空间关系函数测试")
        void testSpatialRelationshipFunctions() {
            // 根据MySQL 8.4官方文档，测试空间关系函数
            String mysqlSql = "SELECT " +
                    "ST_Contains(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))'), ST_GeomFromText('POINT(1 1)')) AS contains_test, " +
                    "ST_Within(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))')) AS within_test, " +
                    "ST_Intersects(ST_GeomFromText('LINESTRING(0 0, 2 2)'), ST_GeomFromText('LINESTRING(0 2, 2 0)')) AS intersects_test, " +
                    "ST_Disjoint(ST_GeomFromText('POINT(0 0)'), ST_GeomFromText('POINT(2 2)')) AS disjoint_test, " +
                    "ST_Touches(ST_GeomFromText('POLYGON((0 0, 0 1, 1 1, 1 0, 0 0))'), ST_GeomFromText('POLYGON((1 0, 1 1, 2 1, 2 0, 1 0))')) AS touches_test, " +
                    "ST_Crosses(ST_GeomFromText('LINESTRING(0 0, 2 2)'), ST_GeomFromText('LINESTRING(0 2, 2 0)')) AS crosses_test, " +
                    "ST_Overlaps(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))'), ST_GeomFromText('POLYGON((1 1, 1 3, 3 3, 3 1, 1 1))')) AS overlaps_test, " +
                    "ST_Equals(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(1 1)')) AS equals_test " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "空间关系函数");
        }

        @Test
        @DisplayName("空间属性函数测试")
        void testSpatialPropertyFunctions() {
            // 根据MySQL 8.4官方文档，测试空间属性函数
            String mysqlSql = "SELECT " +
                    "ST_X(ST_GeomFromText('POINT(1 2)')) AS point_x, " +
                    "ST_Y(ST_GeomFromText('POINT(1 2)')) AS point_y, " +
                    "ST_Length(ST_GeomFromText('LINESTRING(0 0, 3 4)')) AS line_length, " +
                    "ST_Area(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))')) AS polygon_area, " +
                    "ST_Perimeter(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))')) AS polygon_perimeter, " +
                    "ST_Centroid(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))')) AS polygon_centroid, " +
                    "ST_Envelope(ST_GeomFromText('LINESTRING(0 0, 3 4)')) AS bounding_box, " +
                    "ST_Dimension(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))')) AS geometry_dimension, " +
                    "ST_GeometryType(ST_GeomFromText('POINT(1 1)')) AS geometry_type, " +
                    "ST_SRID(ST_GeomFromText('POINT(1 1)')) AS spatial_reference_id " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "空间属性函数");
        }

        @Test
        @DisplayName("空间分析函数测试")
        void testSpatialAnalysisFunctions() {
            // 根据MySQL 8.4官方文档，测试空间分析函数
            String mysqlSql = "SELECT " +
                    "ST_Distance(ST_GeomFromText('POINT(0 0)'), ST_GeomFromText('POINT(3 4)')) AS distance, " +
                    "ST_Buffer(ST_GeomFromText('POINT(0 0)'), 1) AS buffer_around_point, " +
                    "ST_ConvexHull(ST_GeomFromText('MULTIPOINT(0 0, 1 1, 2 0)')) AS convex_hull, " +
                    "ST_Intersection(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))'), ST_GeomFromText('POLYGON((1 1, 1 3, 3 3, 3 1, 1 1))')) AS intersection, " +
                    "ST_Union(ST_GeomFromText('POLYGON((0 0, 0 1, 1 1, 1 0, 0 0))'), ST_GeomFromText('POLYGON((1 0, 1 1, 2 1, 2 0, 1 0))')) AS union_geom, " +
                    "ST_Difference(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))'), ST_GeomFromText('POLYGON((1 1, 1 3, 3 3, 3 1, 1 1))')) AS difference, " +
                    "ST_SymDifference(ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))'), ST_GeomFromText('POLYGON((1 1, 1 3, 3 3, 3 1, 1 1))')) AS sym_difference " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "空间分析函数");
        }
    }

    @Nested
    @DisplayName("性能模式函数全覆盖测试 - 基于MySQL 8.4官方文档14.21节")
    class PerformanceSchemaFunctionsComprehensiveTests {

        @Test
        @DisplayName("性能模式函数测试 - 基于MySQL 8.4官方文档14.21节")
        void testPerformanceSchemaFunctions() {
            // 根据MySQL 8.4官方文档14.21节，测试性能模式函数
            String mysqlSql = "SELECT " +
                    "PS_CURRENT_THREAD_ID() AS current_thread_id, " +
                    "PS_THREAD_ID(CONNECTION_ID()) AS thread_id_for_connection, " +
                    "FORMAT_BYTES(1024) AS format_bytes_kb, " +
                    "FORMAT_BYTES(1048576) AS format_bytes_mb, " +
                    "FORMAT_BYTES(1073741824) AS format_bytes_gb, " +
                    "FORMAT_PICO_TIME(1000000000000) AS format_pico_time_1s, " +
                    "FORMAT_PICO_TIME(1000000000) AS format_pico_time_1ms, " +
                    "FORMAT_PICO_TIME(1000000) AS format_pico_time_1us " +
                    "FROM DUAL";

            testAllDialects(mysqlSql, "性能模式函数");
        }
    }

    /**
     * 测试所有数据库方言的转换
     */
    private void testAllDialects(String mysqlSql, String testType) {
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(mysqlSql);
        System.out.println("达梦" + testType + "转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (damengResult.isSuccess() ? damengResult.getTargetSql() : damengResult.getErrorMessage()));
        assertTrue(damengResult.isSuccess(), "达梦" + testType + "转换应该成功");
        assertNotNull(damengResult.getTargetSql(), "达梦转换结果不应为空");

        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(mysqlSql);
        System.out.println("金仓" + testType + "转换结果:");
        System.out.println("输出: " + (kingbaseResult.isSuccess() ? kingbaseResult.getTargetSql() : kingbaseResult.getErrorMessage()));
        assertTrue(kingbaseResult.isSuccess(), "金仓" + testType + "转换应该成功");
        assertNotNull(kingbaseResult.getTargetSql(), "金仓转换结果不应为空");

        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(mysqlSql);
        System.out.println("神通" + testType + "转换结果:");
        System.out.println("输出: " + (shentongResult.isSuccess() ? shentongResult.getTargetSql() : shentongResult.getErrorMessage()));
        assertTrue(shentongResult.isSuccess(), "神通" + testType + "转换应该成功");
        assertNotNull(shentongResult.getTargetSql(), "神通转换结果不应为空");

        System.out.println("----------------------------------------");
    }
}
