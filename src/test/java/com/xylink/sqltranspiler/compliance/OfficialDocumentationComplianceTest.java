package com.xylink.sqltranspiler.compliance;

import static org.junit.jupiter.api.Assertions.*;

import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.dialects.SqlDialectType;

/**
 * 官方文档合规性测试
 * 
 * 严格遵循rule-db.md规范：
 * - 官方文档绝对权威原则
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 官方文档来源：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 * 
 * 测试覆盖：
 * 1. MySQL官方文档合规性验证
 * 2. 达梦官方文档合规性验证
 * 3. 金仓官方文档合规性验证
 * 4. 神通官方文档合规性验证
 * 5. 转换规则官方文档依据验证
 */
@DisplayName("官方文档合规性测试")
public class OfficialDocumentationComplianceTest {

    private SqlTranspiler damengTranspiler;
    private SqlTranspiler kingbaseTranspiler;
    private SqlTranspiler shentongTranspiler;

    @BeforeEach
    void setUp() {
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
        
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
        
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Nested
    @DisplayName("MySQL官方文档合规性验证")
    class MySqlOfficialDocumentationComplianceTests {
        
        @Test
        @DisplayName("验证MySQL CREATE TABLE语法合规性")
        void testMySqlCreateTableSyntaxCompliance() {
            // 根据MySQL 8.4官方文档 https://dev.mysql.com/doc/refman/8.4/en/create-table.html
            // CREATE TABLE语法的基本形式
            String mysqlCreateTableSql = "CREATE TABLE t1 (" +
                "id INT AUTO_INCREMENT PRIMARY KEY," +
                "name VARCHAR(50) NOT NULL," +
                "email VARCHAR(100) UNIQUE," +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")";

            // 验证MySQL语法被正确识别
            assertDoesNotThrow(() -> {
                TranspilationResult result = damengTranspiler.transpile(mysqlCreateTableSql);
                assertNotNull(result, "MySQL CREATE TABLE语法应被正确处理");
            }, "MySQL官方文档定义的CREATE TABLE语法应被支持");
        }

        @Test
        @DisplayName("验证MySQL AUTO_INCREMENT语法合规性")
        void testMySqlAutoIncrementSyntaxCompliance() {
            // 根据MySQL 8.4官方文档 https://dev.mysql.com/doc/refman/8.4/en/example-auto-increment.html
            // AUTO_INCREMENT属性的使用
            String mysqlAutoIncrementSql = "CREATE TABLE animals (" +
                "id MEDIUMINT NOT NULL AUTO_INCREMENT," +
                "name CHAR(30) NOT NULL," +
                "PRIMARY KEY (id)" +
                ")";

            // 验证MySQL AUTO_INCREMENT语法被正确识别
            assertDoesNotThrow(() -> {
                TranspilationResult result = damengTranspiler.transpile(mysqlAutoIncrementSql);
                assertNotNull(result, "MySQL AUTO_INCREMENT语法应被正确处理");
            }, "MySQL官方文档定义的AUTO_INCREMENT语法应被支持");
        }

        @Test
        @DisplayName("验证MySQL LIMIT语法合规性")
        void testMySqlLimitSyntaxCompliance() {
            // 根据MySQL 8.4官方文档 https://dev.mysql.com/doc/refman/8.4/en/select.html
            // LIMIT子句的使用
            String mysqlLimitSql = "SELECT * FROM users ORDER BY id LIMIT 10 OFFSET 5";

            // 验证MySQL LIMIT语法被正确识别
            assertDoesNotThrow(() -> {
                TranspilationResult result = damengTranspiler.transpile(mysqlLimitSql);
                assertNotNull(result, "MySQL LIMIT语法应被正确处理");
            }, "MySQL官方文档定义的LIMIT语法应被支持");
        }
    }

    @Nested
    @DisplayName("达梦官方文档合规性验证")
    class DamengOfficialDocumentationComplianceTests {
        
        @Test
        @DisplayName("验证达梦IDENTITY列合规性")
        void testDamengIdentityColumnCompliance() {
            // 根据达梦官方文档，IDENTITY列是达梦数据库的自增列实现方式
            // 测试MySQL AUTO_INCREMENT到达梦IDENTITY的转换
            String mysqlSql = "CREATE TABLE test_identity (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))";
            
            TranspilationResult result = damengTranspiler.transpile(mysqlSql);
            
            System.out.println("达梦IDENTITY列转换测试:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
            
            assertTrue(result.isSuccess(), "达梦转换应该成功");
            
            // 根据达梦官方文档，验证IDENTITY转换或保留AUTO_INCREMENT
            String damengSql = result.getTargetSql();
            assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                      "达梦应支持IDENTITY列或保留AUTO_INCREMENT");
        }

        @Test
        @DisplayName("验证达梦SYSDATE函数合规性")
        void testDamengSysdateFunctionCompliance() {
            // 根据达梦官方文档，SYSDATE是达梦数据库的当前时间函数
            String mysqlSql = "SELECT CURRENT_TIMESTAMP AS current_time";
            
            TranspilationResult result = damengTranspiler.transpile(mysqlSql);
            
            System.out.println("达梦SYSDATE函数转换测试:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
            
            assertTrue(result.isSuccess(), "达梦转换应该成功");
            
            // 根据达梦官方文档，验证时间函数转换
            String damengSql = result.getTargetSql();
            assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                      "达梦应支持SYSDATE函数或保留CURRENT_TIMESTAMP");
        }
    }

    @Nested
    @DisplayName("金仓官方文档合规性验证")
    class KingbaseOfficialDocumentationComplianceTests {
        
        @Test
        @DisplayName("验证金仓SERIAL类型合规性")
        void testKingbaseSerialTypeCompliance() {
            // 根据金仓官方文档，SERIAL是金仓数据库的自增类型（PostgreSQL兼容）
            String mysqlSql = "CREATE TABLE test_serial (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))";
            
            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);
            
            System.out.println("金仓SERIAL类型转换测试:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
            
            assertTrue(result.isSuccess(), "金仓转换应该成功");
            
            // 根据金仓官方文档，验证SERIAL转换或其他自增实现
            String kingbaseSql = result.getTargetSql();
            assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("IDENTITY") || 
                      kingbaseSql.contains("AUTO_INCREMENT"), 
                      "金仓应支持SERIAL类型、IDENTITY或保留AUTO_INCREMENT");
        }

        @Test
        @DisplayName("验证金仓PostgreSQL兼容性合规性")
        void testKingbasePostgreSQLCompatibilityCompliance() {
            // 根据金仓官方文档，金仓数据库支持PostgreSQL兼容模式
            String mysqlSql = "SELECT * FROM users LIMIT 10 OFFSET 5";
            
            TranspilationResult result = kingbaseTranspiler.transpile(mysqlSql);
            
            System.out.println("金仓PostgreSQL兼容性测试:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
            
            assertTrue(result.isSuccess(), "金仓转换应该成功");
            
            // 根据金仓官方文档，验证PostgreSQL兼容语法
            String kingbaseSql = result.getTargetSql();
            assertTrue(kingbaseSql.contains("LIMIT") && kingbaseSql.contains("OFFSET"), 
                      "金仓应支持PostgreSQL兼容的LIMIT OFFSET语法");
        }
    }

    @Nested
    @DisplayName("神通官方文档合规性验证")
    class ShentongOfficialDocumentationComplianceTests {
        
        @Test
        @DisplayName("验证神通ROWNUM分页合规性")
        void testShentongRownumPaginationCompliance() {
            // 根据神通官方文档，神通数据库支持Oracle兼容的ROWNUM分页
            String mysqlSql = "SELECT * FROM users LIMIT 10";
            
            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);
            
            System.out.println("神通ROWNUM分页转换测试:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
            
            assertTrue(result.isSuccess(), "神通转换应该成功");
            
            // 根据神通官方文档，验证ROWNUM转换或保留LIMIT
            String shentongSql = result.getTargetSql();
            assertTrue(shentongSql.contains("ROWNUM") || shentongSql.contains("LIMIT"), 
                      "神通应支持ROWNUM分页或保留LIMIT");
        }

        @Test
        @DisplayName("验证神通Oracle兼容性合规性")
        void testShentongOracleCompatibilityCompliance() {
            // 根据神通官方文档，神通数据库支持Oracle兼容模式
            String mysqlSql = "SELECT 1 AS test_value";
            
            TranspilationResult result = shentongTranspiler.transpile(mysqlSql);
            
            System.out.println("神通Oracle兼容性测试:");
            System.out.println("输入: " + mysqlSql);
            System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
            
            assertTrue(result.isSuccess(), "神通转换应该成功");
            
            // 根据神通官方文档，验证Oracle兼容语法
            String shentongSql = result.getTargetSql();
            assertNotNull(shentongSql, "神通转换结果不应为空");
            // 可能需要添加FROM DUAL或保持原样
            assertTrue(shentongSql.contains("SELECT"), "应保留SELECT语句");
        }
    }

    @Nested
    @DisplayName("转换器配置合规性验证")
    class TranspilerConfigurationComplianceTests {
        
        @Test
        @DisplayName("验证方言类型配置合规性")
        void testDialectTypeConfigurationCompliance() {
            // 验证所有支持的方言类型都有对应的官方文档URL
            assertEquals("https://dev.mysql.com/doc/refman/8.4/en/", 
                        SqlDialectType.MYSQL.getOfficialDocumentationUrl(),
                        "MySQL方言应有正确的官方文档URL");
            
            assertEquals("https://eco.dameng.com/document/dm/zh-cn/sql-dev/", 
                        SqlDialectType.DAMENG.getOfficialDocumentationUrl(),
                        "达梦方言应有正确的官方文档URL");
            
            assertEquals("https://help.kingbase.com.cn/v8/development/sql-plsql/sql/", 
                        SqlDialectType.KINGBASE.getOfficialDocumentationUrl(),
                        "金仓方言应有正确的官方文档URL");
            
            assertEquals("@shentong.md", 
                        SqlDialectType.SHENTONG.getOfficialDocumentationUrl(),
                        "神通方言应有正确的官方文档引用");
        }

        @Test
        @DisplayName("验证转换器构建器合规性")
        void testTranspilerBuilderCompliance() {
            // 验证预配置的构建器方法符合官方文档
            SqlTranspiler dameng = TranspilerBuilder.mysqlToDameng().build();
            assertEquals(SqlDialectType.MYSQL, dameng.getSourceDialect());
            assertEquals(SqlDialectType.DAMENG, dameng.getTargetDialect());
            
            SqlTranspiler kingbase = TranspilerBuilder.mysqlToKingbase().build();
            assertEquals(SqlDialectType.MYSQL, kingbase.getSourceDialect());
            assertEquals(SqlDialectType.KINGBASE, kingbase.getTargetDialect());
            
            SqlTranspiler shentong = TranspilerBuilder.mysqlToShentong().build();
            assertEquals(SqlDialectType.MYSQL, shentong.getSourceDialect());
            assertEquals(SqlDialectType.SHENTONG, shentong.getTargetDialect());
        }
    }
}
