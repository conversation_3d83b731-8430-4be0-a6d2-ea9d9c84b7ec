package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * MySQL特有语法转换测试
 * 
 * 验证MySQL特有语法到各种目标数据库的转换功能
 * 
 * 根据官方文档：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 */
public class MysqlSpecificSyntaxTest {
    
    @Test
    @DisplayName("测试AUTO_INCREMENT转换到达梦")
    void testAutoIncrementToDameng() {
        String mysqlSql = "CREATE TABLE test_auto (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== AUTO_INCREMENT转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("IDENTITY"), "AUTO_INCREMENT应转换为IDENTITY");
        assertTrue(targetSql.contains("INTEGER"), "INT应转换为INTEGER");
    }
    
    @Test
    @DisplayName("测试ENUM类型转换到达梦")
    void testEnumTypeToDameng() {
        String mysqlSql = "CREATE TABLE test_enum (id INT, status ENUM('active','inactive','pending') DEFAULT 'pending')";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== ENUM类型转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("VARCHAR"), "ENUM应转换为VARCHAR");
        // 达梦不支持ENUM，应该转换为VARCHAR并添加CHECK约束或注释
    }
    
    @Test
    @DisplayName("测试SET类型转换到达梦")
    void testSetTypeToDameng() {
        String mysqlSql = "CREATE TABLE test_set (id INT, permissions SET('read','write','execute'))";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== SET类型转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("VARCHAR"), "SET应转换为VARCHAR");
        // 达梦不支持SET，应该转换为VARCHAR并添加说明
    }
    
    @Test
    @DisplayName("测试JSON类型转换到达梦")
    void testJsonTypeToDameng() {
        String mysqlSql = "CREATE TABLE test_json (id INT, data JSON, metadata JSON NOT NULL)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== JSON类型转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CLOB"), "JSON应转换为CLOB");
    }
    
    @Test
    @DisplayName("测试REPLACE INTO转换到达梦")
    void testReplaceIntoToDameng() {
        String mysqlSql = "REPLACE INTO users (id, name, email) VALUES (1, 'John', '<EMAIL>')";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== REPLACE INTO转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 达梦不支持REPLACE INTO，应该转换为注释说明
        assertTrue(targetSql.contains("注意") || targetSql.contains("MERGE") || targetSql.contains("INSERT"), 
                  "应包含转换说明或替代语法");
    }
    
    @Test
    @DisplayName("测试MySQL函数转换到达梦")
    void testMysqlFunctionsToDameng() {
        String mysqlSql = "SELECT CONCAT(first_name, ' ', last_name) as full_name, NOW(), CURDATE() FROM users";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== MySQL函数转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 达梦支持大部分MySQL函数，应该直接兼容
        assertTrue(targetSql.contains("CONCAT"), "CONCAT函数应保持不变");
        assertTrue(targetSql.contains("NOW"), "NOW函数应保持不变");
    }
    
    @Test
    @DisplayName("测试AUTO_INCREMENT转换到金仓")
    void testAutoIncrementToKingbase() {
        String mysqlSql = "CREATE TABLE test_auto (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== AUTO_INCREMENT转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("AUTO_INCREMENT"), "金仓在MySQL兼容模式下支持AUTO_INCREMENT");
    }
    
    @Test
    @DisplayName("测试ENUM类型转换到金仓")
    void testEnumTypeToKingbase() {
        String mysqlSql = "CREATE TABLE test_enum (id INT, status ENUM('active','inactive','pending') DEFAULT 'pending')";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== ENUM类型转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 金仓在MySQL兼容模式下可能支持ENUM，或者转换为其他类型
        assertTrue(targetSql.contains("ENUM") || targetSql.contains("VARCHAR"), 
                  "应保持ENUM或转换为VARCHAR");
    }
    
    @Test
    @DisplayName("测试JSON类型转换到金仓")
    void testJsonTypeToKingbase() {
        String mysqlSql = "CREATE TABLE test_json (id INT, data JSON, metadata JSON NOT NULL)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== JSON类型转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 金仓在MySQL兼容模式下可能支持JSON，或者转换为其他类型
        assertTrue(targetSql.contains("JSON") || targetSql.contains("TEXT") || targetSql.contains("VARCHAR"), 
                  "应保持JSON或转换为TEXT/VARCHAR");
    }
    
    @Test
    @DisplayName("测试REPLACE INTO转换到金仓")
    void testReplaceIntoToKingbase() {
        String mysqlSql = "REPLACE INTO users (id, name, email) VALUES (1, 'John', '<EMAIL>')";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== REPLACE INTO转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 金仓在MySQL兼容模式下支持REPLACE INTO
        assertTrue(targetSql.contains("REPLACE INTO"), "金仓在MySQL兼容模式下支持REPLACE INTO");
    }
}
