package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * 函数和操作符转换测试
 * 
 * 验证MySQL函数到各种目标数据库的转换功能
 * 
 * 根据官方文档：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/functions.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 */
public class FunctionTranspilationTest {
    
    @Test
    @DisplayName("测试字符串函数转换到达梦")
    void testStringFunctionsToDameng() {
        String mysqlSql = "SELECT CONCAT(first_name, ' ', last_name) as full_name, " +
                         "SUBSTRING(email, 1, 10) as email_prefix, " +
                         "LENGTH(name) as name_length, " +
                         "UPPER(status) as upper_status " +
                         "FROM users";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 字符串函数转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 根据达梦官方文档，这些函数都支持
        assertTrue(targetSql.contains("CONCAT"), "CONCAT函数应保持不变");
        assertTrue(targetSql.contains("SUBSTRING"), "SUBSTRING函数应保持不变");
        assertTrue(targetSql.contains("LENGTH"), "LENGTH函数应保持不变");
        assertTrue(targetSql.contains("UPPER"), "UPPER函数应保持不变");
    }
    
    @Test
    @DisplayName("测试日期时间函数转换到达梦")
    void testDateTimeFunctionsToDameng() {
        String mysqlSql = "SELECT NOW() as current_time, " +
                         "CURDATE() as current_date, " +
                         "DATE_FORMAT(created_at, '%Y-%m-%d') as formatted_date, " +
                         "YEAR(created_at) as year_part " +
                         "FROM orders";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 日期时间函数转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 根据达梦官方文档，大部分日期函数都支持
        assertTrue(targetSql.contains("NOW"), "NOW函数应保持不变或有等价转换");
        assertTrue(targetSql.contains("YEAR"), "YEAR函数应保持不变");
    }
    
    @Test
    @DisplayName("测试数学函数转换到达梦")
    void testMathFunctionsToDameng() {
        String mysqlSql = "SELECT ROUND(price, 2) as rounded_price, " +
                         "CEIL(quantity) as ceiling_qty, " +
                         "FLOOR(discount) as floor_discount, " +
                         "ABS(balance) as absolute_balance " +
                         "FROM products";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 数学函数转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 根据达梦官方文档，这些数学函数都支持
        assertTrue(targetSql.contains("ROUND"), "ROUND函数应保持不变");
        assertTrue(targetSql.contains("CEIL"), "CEIL函数应保持不变");
        assertTrue(targetSql.contains("FLOOR"), "FLOOR函数应保持不变");
        assertTrue(targetSql.contains("ABS"), "ABS函数应保持不变");
    }
    
    @Test
    @DisplayName("测试聚合函数转换到达梦")
    void testAggregateFunctionsToDameng() {
        String mysqlSql = "SELECT COUNT(*) as total_count, " +
                         "SUM(amount) as total_amount, " +
                         "AVG(price) as average_price, " +
                         "MAX(created_at) as latest_date, " +
                         "MIN(id) as min_id " +
                         "FROM orders GROUP BY status";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 聚合函数转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 根据达梦官方文档，这些聚合函数都支持
        assertTrue(targetSql.contains("COUNT"), "COUNT函数应保持不变");
        assertTrue(targetSql.contains("SUM"), "SUM函数应保持不变");
        assertTrue(targetSql.contains("AVG"), "AVG函数应保持不变");
        assertTrue(targetSql.contains("MAX"), "MAX函数应保持不变");
        assertTrue(targetSql.contains("MIN"), "MIN函数应保持不变");
    }
    
    @Test
    @DisplayName("测试MySQL特有函数转换到达梦")
    void testMysqlSpecificFunctionsToDameng() {
        String mysqlSql = "SELECT IF(status = 'active', 1, 0) as is_active, " +
                         "IFNULL(description, 'No description') as desc_text, " +
                         "COALESCE(phone, email, 'No contact') as contact " +
                         "FROM users";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== MySQL特有函数转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 根据达梦官方文档，需要检查这些函数的支持情况
        // IF函数可能需要转换为CASE WHEN
        // IFNULL可能需要转换为NVL或COALESCE
        // COALESCE在达梦中应该支持
    }
    
    @Test
    @DisplayName("测试字符串函数转换到金仓")
    void testStringFunctionsToKingbase() {
        String mysqlSql = "SELECT CONCAT(first_name, ' ', last_name) as full_name, " +
                         "SUBSTRING(email, 1, 10) as email_prefix " +
                         "FROM users";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== 字符串函数转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 金仓在MySQL兼容模式下应该支持这些函数
        assertTrue(targetSql.contains("CONCAT"), "CONCAT函数应保持不变");
        assertTrue(targetSql.contains("SUBSTRING"), "SUBSTRING函数应保持不变");
    }
    
    @Test
    @DisplayName("测试日期时间函数转换到金仓")
    void testDateTimeFunctionsToKingbase() {
        String mysqlSql = "SELECT NOW() as current_time, CURDATE() as current_date FROM dual";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== 日期时间函数转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 金仓在MySQL兼容模式下应该支持这些函数
        assertTrue(targetSql.contains("NOW"), "NOW函数应保持不变");
        assertTrue(targetSql.contains("CURDATE"), "CURDATE函数应保持不变");
    }
}
