package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * 简化的ENUM类型转换测试
 * 
 * 测试不带默认值的ENUM类型转换
 */
public class SimpleEnumTest {
    
    @Test
    @DisplayName("测试简单ENUM类型转换到达梦")
    void testSimpleEnumToDameng() {
        String mysqlSql = "CREATE TABLE test_enum (id INT, status ENUM('active','inactive'))";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 简单ENUM类型转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("VARCHAR"), "ENUM应转换为VARCHAR");
        assertTrue(targetSql.contains("INTEGER"), "INT应转换为INTEGER");
    }
    
    @Test
    @DisplayName("测试SET类型转换到达梦")
    void testSetTypeToDameng() {
        String mysqlSql = "CREATE TABLE test_set (id INT, permissions SET('read','write'))";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== SET类型转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("VARCHAR"), "SET应转换为VARCHAR");
    }
}
