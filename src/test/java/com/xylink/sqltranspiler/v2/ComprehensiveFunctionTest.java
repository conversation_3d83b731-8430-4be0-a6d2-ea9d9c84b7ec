package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * 综合函数转换测试
 * 
 * 验证所有数据库的函数转换功能
 */
public class ComprehensiveFunctionTest {
    
    @Test
    @DisplayName("验证完整的函数转换流程")
    void testCompleteFunctionTranspilationFlow() {
        // 测试包含多种函数的复杂SQL
        String complexSql = "SELECT " +
                           "CONCAT(first_name, ' ', last_name) as full_name, " +
                           "IF(age >= 18, 'Adult', 'Minor') as age_group, " +
                           "IFNULL(phone, 'No phone') as contact_phone, " +
                           "ROUND(salary, 2) as rounded_salary, " +
                           "NOW() as current_time, " +
                           "COUNT(*) as total_count " +
                           "FROM employees " +
                           "WHERE status = 'active' " +
                           "GROUP BY age_group " +
                           "ORDER BY total_count DESC " +
                           "LIMIT 10";
        
        // 测试达梦转换
        TranspilationResult damengResult = TranspilerBuilder.mysqlToDameng().transpile(complexSql);
        assertTrue(damengResult.isSuccess(), "达梦函数转换应该成功");
        
        String damengSql = damengResult.getTargetSql();
        assertTrue(damengSql.contains("CONCAT"), "CONCAT函数应保持不变");
        assertTrue(damengSql.contains("CASE WHEN"), "IF函数应转换为CASE WHEN");
        assertTrue(damengSql.contains("NVL"), "IFNULL函数应转换为NVL");
        assertTrue(damengSql.contains("ROUND"), "ROUND函数应保持不变");
        assertTrue(damengSql.contains("NOW"), "NOW函数应保持不变");
        assertTrue(damengSql.contains("COUNT"), "COUNT函数应保持不变");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase().transpile(complexSql);
        assertTrue(kingbaseResult.isSuccess(), "金仓函数转换应该成功");
        
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertTrue(kingbaseSql.contains("CONCAT"), "CONCAT函数应保持不变");
        assertTrue(kingbaseSql.contains("IF"), "IF函数在MySQL兼容模式下应保持不变");
        assertTrue(kingbaseSql.contains("IFNULL"), "IFNULL函数在MySQL兼容模式下应保持不变");
        
        // 测试神通转换
        TranspilationResult shentongResult = TranspilerBuilder.mysqlToShentong().transpile(complexSql);
        assertTrue(shentongResult.isSuccess(), "神通函数转换应该成功");
        
        System.out.println("=== 综合函数转换验证 ===");
        System.out.println("✅ 达梦转换: " + damengResult.isSuccess());
        System.out.println("✅ 金仓转换: " + kingbaseResult.isSuccess());
        System.out.println("✅ 神通转换: " + shentongResult.isSuccess());
        
        System.out.println("\n=== 达梦转换结果 ===");
        System.out.println(damengSql);
        
        System.out.println("\n=== 金仓转换结果 ===");
        System.out.println(kingbaseSql);
    }
    
    @Test
    @DisplayName("验证函数映射的准确性")
    void testFunctionMappingAccuracy() {
        // 测试每个重要函数的映射
        
        // 1. 字符串函数
        String stringFuncSql = "SELECT CONCAT('Hello', ' ', 'World'), SUBSTRING('test', 1, 2), LENGTH('abc'), UPPER('test')";
        TranspilationResult result1 = TranspilerBuilder.mysqlToDameng().transpile(stringFuncSql);
        assertTrue(result1.isSuccess(), "字符串函数转换应该成功");
        
        // 2. 数学函数
        String mathFuncSql = "SELECT ROUND(3.14159, 2), CEIL(2.1), FLOOR(2.9), ABS(-5)";
        TranspilationResult result2 = TranspilerBuilder.mysqlToDameng().transpile(mathFuncSql);
        assertTrue(result2.isSuccess(), "数学函数转换应该成功");
        
        // 3. 日期函数
        String dateFuncSql = "SELECT NOW(), CURDATE(), YEAR('2023-12-25'), MONTH('2023-12-25')";
        TranspilationResult result3 = TranspilerBuilder.mysqlToDameng().transpile(dateFuncSql);
        assertTrue(result3.isSuccess(), "日期函数转换应该成功");
        
        // 4. 聚合函数
        String aggFuncSql = "SELECT COUNT(*), SUM(amount), AVG(price), MAX(date), MIN(id) FROM orders";
        TranspilationResult result4 = TranspilerBuilder.mysqlToDameng().transpile(aggFuncSql);
        assertTrue(result4.isSuccess(), "聚合函数转换应该成功");
        
        // 5. MySQL特有函数
        String mysqlFuncSql = "SELECT IF(1=1, 'true', 'false'), IFNULL(NULL, 'default'), COALESCE(NULL, NULL, 'value')";
        TranspilationResult result5 = TranspilerBuilder.mysqlToDameng().transpile(mysqlFuncSql);
        assertTrue(result5.isSuccess(), "MySQL特有函数转换应该成功");
        
        String damengSql = result5.getTargetSql();
        assertTrue(damengSql.contains("CASE WHEN"), "IF应转换为CASE WHEN");
        assertTrue(damengSql.contains("NVL"), "IFNULL应转换为NVL");
        assertTrue(damengSql.contains("COALESCE"), "COALESCE应保持不变");
        
        System.out.println("=== 函数映射准确性验证 ===");
        System.out.println("✅ 字符串函数: " + result1.isSuccess());
        System.out.println("✅ 数学函数: " + result2.isSuccess());
        System.out.println("✅ 日期函数: " + result3.isSuccess());
        System.out.println("✅ 聚合函数: " + result4.isSuccess());
        System.out.println("✅ MySQL特有函数: " + result5.isSuccess());
        
        System.out.println("\n=== MySQL特有函数转换结果 ===");
        System.out.println("输入: " + mysqlFuncSql);
        System.out.println("输出: " + damengSql);
    }
    
    @Test
    @DisplayName("验证跨数据库函数兼容性")
    void testCrossDatabaseFunctionCompatibility() {
        String testSql = "SELECT CONCAT(name, ' - ', status), ROUND(price, 2), NOW(), COUNT(*) FROM products GROUP BY status";
        
        // 测试所有目标数据库
        TranspilationResult damengResult = TranspilerBuilder.mysqlToDameng().transpile(testSql);
        TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase().transpile(testSql);
        TranspilationResult shentongResult = TranspilerBuilder.mysqlToShentong().transpile(testSql);
        
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        
        // 验证基础函数在所有数据库中都能正确处理
        String damengSql = damengResult.getTargetSql();
        String kingbaseSql = kingbaseResult.getTargetSql();
        String shentongSql = shentongResult.getTargetSql();
        
        // 所有数据库都应该支持这些基础函数
        assertTrue(damengSql.contains("CONCAT"), "达梦应支持CONCAT");
        assertTrue(kingbaseSql.contains("CONCAT"), "金仓应支持CONCAT");
        assertTrue(shentongSql.contains("CONCAT"), "神通应支持CONCAT");
        
        assertTrue(damengSql.contains("ROUND"), "达梦应支持ROUND");
        assertTrue(kingbaseSql.contains("ROUND"), "金仓应支持ROUND");
        assertTrue(shentongSql.contains("ROUND"), "神通应支持ROUND");
        
        System.out.println("=== 跨数据库函数兼容性验证 ===");
        System.out.println("✅ 达梦兼容性: " + damengResult.isSuccess());
        System.out.println("✅ 金仓兼容性: " + kingbaseResult.isSuccess());
        System.out.println("✅ 神通兼容性: " + shentongResult.isSuccess());
    }
    
    @Test
    @DisplayName("验证函数转换的性能")
    void testFunctionTranspilationPerformance() {
        String complexSql = "SELECT " +
                           "CONCAT(UPPER(first_name), ' ', LOWER(last_name)) as formatted_name, " +
                           "IF(YEAR(birth_date) < 1990, 'Senior', IF(YEAR(birth_date) < 2000, 'Middle', 'Junior')) as generation, " +
                           "ROUND(AVG(IFNULL(salary, 0)), 2) as avg_salary, " +
                           "COUNT(DISTINCT department) as dept_count, " +
                           "MAX(DATE_FORMAT(hire_date, '%Y-%m-%d')) as latest_hire " +
                           "FROM employees " +
                           "WHERE status IN ('active', 'pending') " +
                           "GROUP BY generation " +
                           "HAVING avg_salary > 50000 " +
                           "ORDER BY avg_salary DESC " +
                           "LIMIT 20";
        
        long startTime = System.currentTimeMillis();
        
        // 执行多次转换测试性能
        for (int i = 0; i < 10; i++) {
            TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(complexSql);
            assertTrue(result.isSuccess(), "复杂函数转换应该成功");
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("=== 函数转换性能验证 ===");
        System.out.println("10次复杂函数转换耗时: " + duration + "ms");
        System.out.println("平均每次转换耗时: " + (duration / 10.0) + "ms");
        
        // 性能应该在合理范围内（每次转换不超过100ms）
        assertTrue(duration / 10.0 < 100, "函数转换性能应该在合理范围内");
    }
}
