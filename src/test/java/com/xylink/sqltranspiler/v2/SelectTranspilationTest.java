package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * SELECT语句转换测试
 * 
 * 验证MySQL SELECT语句到各种目标数据库的转换功能
 */
public class SelectTranspilationTest {
    
    @Test
    @DisplayName("测试基础SELECT转换到达梦")
    void testBasicSelectToDameng() {
        String mysqlSql = "SELECT id, name FROM users WHERE age > 18";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 基础SELECT转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("SELECT"), "应包含SELECT");
        assertTrue(targetSql.contains("FROM"), "应包含FROM");
        assertTrue(targetSql.contains("WHERE"), "应包含WHERE");
    }
    
    @Test
    @DisplayName("测试带LIMIT的SELECT转换到达梦")
    void testSelectWithLimitToDameng() {
        String mysqlSql = "SELECT id, name FROM users ORDER BY id LIMIT 10";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 带LIMIT的SELECT转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        // 达梦数据库支持LIMIT语法，或者转换为ROWNUM
        assertTrue(targetSql.contains("SELECT"), "应包含SELECT");
        assertTrue(targetSql.contains("ORDER BY"), "应包含ORDER BY");
    }
    
    @Test
    @DisplayName("测试带LIMIT OFFSET的SELECT转换到达梦")
    void testSelectWithLimitOffsetToDameng() {
        String mysqlSql = "SELECT id, name FROM users ORDER BY id LIMIT 10 OFFSET 20";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 带LIMIT OFFSET的SELECT转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
    }
    
    @Test
    @DisplayName("测试JOIN查询转换到达梦")
    void testSelectWithJoinToDameng() {
        String mysqlSql = "SELECT u.id, u.name, o.total FROM users u LEFT JOIN orders o ON u.id = o.user_id";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== JOIN查询转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("LEFT JOIN"), "应包含LEFT JOIN");
        assertTrue(targetSql.contains("ON"), "应包含ON条件");
    }
    
    @Test
    @DisplayName("测试子查询转换到达梦")
    void testSelectWithSubqueryToDameng() {
        String mysqlSql = "SELECT id, name FROM users WHERE id IN (SELECT user_id FROM orders WHERE total > 100)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 子查询转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("IN"), "应包含IN");
        assertTrue(targetSql.contains("SELECT"), "应包含子查询的SELECT");
    }
    
    @Test
    @DisplayName("测试聚合函数转换到达梦")
    void testSelectWithAggregateToDameng() {
        String mysqlSql = "SELECT COUNT(*), AVG(age), MAX(salary) FROM users GROUP BY department";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 聚合函数转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("COUNT"), "应包含COUNT函数");
        assertTrue(targetSql.contains("AVG"), "应包含AVG函数");
        assertTrue(targetSql.contains("MAX"), "应包含MAX函数");
        assertTrue(targetSql.contains("GROUP BY"), "应包含GROUP BY");
    }
    
    @Test
    @DisplayName("测试基础SELECT转换到金仓")
    void testBasicSelectToKingbase() {
        String mysqlSql = "SELECT id, name FROM users WHERE age > 18";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== 基础SELECT转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("SELECT"), "应包含SELECT");
        assertTrue(targetSql.contains("FROM"), "应包含FROM");
        assertTrue(targetSql.contains("WHERE"), "应包含WHERE");
    }
    
    @Test
    @DisplayName("测试基础SELECT转换到神通")
    void testBasicSelectToShentong() {
        String mysqlSql = "SELECT id, name FROM users WHERE age > 18";
        
        TranspilationResult result = TranspilerBuilder.mysqlToShentong().transpile(mysqlSql);
        
        System.out.println("=== 基础SELECT转换到神通 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("SELECT"), "应包含SELECT");
        assertTrue(targetSql.contains("FROM"), "应包含FROM");
        assertTrue(targetSql.contains("WHERE"), "应包含WHERE");
    }
}
