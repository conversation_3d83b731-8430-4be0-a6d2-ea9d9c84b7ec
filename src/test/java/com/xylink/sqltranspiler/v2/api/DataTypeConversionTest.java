package com.xylink.sqltranspiler.v2.api;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据类型转换测试
 * 
 * 根据官方文档实现：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/data-types.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 * - 神通官方文档：参考项目内@shentong.md文档
 */
@DisplayName("数据类型转换测试")
public class DataTypeConversionTest {
    
    private SqlTranspiler damengTranspiler;
    private SqlTranspiler kingbaseTranspiler;
    private SqlTranspiler shentongTranspiler;
    
    @BeforeEach
    void setUp() {
        // 创建转换器
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
        
        kingbaseTranspiler = TranspilerBuilder.mysqlToKingbase()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
        
        shentongTranspiler = TranspilerBuilder.mysqlToShentong()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
    }
    
    @Test
    @DisplayName("整数类型转换")
    void testIntegerTypeConversion() {
        String sql = "CREATE TABLE test_int (id TINYINT, count SMALLINT, amount MEDIUMINT, total BIGINT)";
        
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(sql);
        assertNotNull(damengResult, "达梦转换结果不应为空");
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        String damengSql = damengResult.getTargetSql();
        assertNotNull(damengSql, "达梦转换SQL不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "达梦转换应包含CREATE TABLE");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(sql);
        assertNotNull(kingbaseResult, "金仓转换结果不应为空");
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertNotNull(kingbaseSql, "金仓转换SQL不应为空");
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "金仓转换应包含CREATE TABLE");
        
        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(sql);
        assertNotNull(shentongResult, "神通转换结果不应为空");
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        String shentongSql = shentongResult.getTargetSql();
        assertNotNull(shentongSql, "神通转换SQL不应为空");
        assertTrue(shentongSql.contains("CREATE TABLE"), "神通转换应包含CREATE TABLE");
    }
    
    @Test
    @DisplayName("字符串类型转换")
    void testStringTypeConversion() {
        String sql = "CREATE TABLE test_string (name VARCHAR(100), description TEXT, code CHAR(10))";
        
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(sql);
        assertNotNull(damengResult, "达梦转换结果不应为空");
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        String damengSql = damengResult.getTargetSql();
        assertNotNull(damengSql, "达梦转换SQL不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "达梦转换应包含CREATE TABLE");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(sql);
        assertNotNull(kingbaseResult, "金仓转换结果不应为空");
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertNotNull(kingbaseSql, "金仓转换SQL不应为空");
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "金仓转换应包含CREATE TABLE");
        
        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(sql);
        assertNotNull(shentongResult, "神通转换结果不应为空");
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        String shentongSql = shentongResult.getTargetSql();
        assertNotNull(shentongSql, "神通转换SQL不应为空");
        assertTrue(shentongSql.contains("CREATE TABLE"), "神通转换应包含CREATE TABLE");
    }
    
    @Test
    @DisplayName("日期时间类型转换")
    void testDateTimeTypeConversion() {
        String sql = "CREATE TABLE test_datetime (created_date DATE, updated_time TIME, created_at DATETIME, timestamp_col TIMESTAMP)";
        
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(sql);
        assertNotNull(damengResult, "达梦转换结果不应为空");
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        String damengSql = damengResult.getTargetSql();
        assertNotNull(damengSql, "达梦转换SQL不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "达梦转换应包含CREATE TABLE");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(sql);
        assertNotNull(kingbaseResult, "金仓转换结果不应为空");
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertNotNull(kingbaseSql, "金仓转换SQL不应为空");
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "金仓转换应包含CREATE TABLE");
        
        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(sql);
        assertNotNull(shentongResult, "神通转换结果不应为空");
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        String shentongSql = shentongResult.getTargetSql();
        assertNotNull(shentongSql, "神通转换SQL不应为空");
        assertTrue(shentongSql.contains("CREATE TABLE"), "神通转换应包含CREATE TABLE");
    }
    
    @Test
    @DisplayName("数值类型转换")
    void testNumericTypeConversion() {
        String sql = "CREATE TABLE test_numeric (price DECIMAL(10,2), rate FLOAT, percentage DOUBLE)";
        
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(sql);
        assertNotNull(damengResult, "达梦转换结果不应为空");
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        String damengSql = damengResult.getTargetSql();
        assertNotNull(damengSql, "达梦转换SQL不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "达梦转换应包含CREATE TABLE");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(sql);
        assertNotNull(kingbaseResult, "金仓转换结果不应为空");
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertNotNull(kingbaseSql, "金仓转换SQL不应为空");
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "金仓转换应包含CREATE TABLE");
        
        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(sql);
        assertNotNull(shentongResult, "神通转换结果不应为空");
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        String shentongSql = shentongResult.getTargetSql();
        assertNotNull(shentongSql, "神通转换SQL不应为空");
        assertTrue(shentongSql.contains("CREATE TABLE"), "神通转换应包含CREATE TABLE");
    }
    
    @Test
    @DisplayName("AUTO_INCREMENT转换")
    void testAutoIncrementConversion() {
        String sql = "CREATE TABLE test_auto (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))";
        
        // 测试达梦转换 - 达梦使用IDENTITY
        TranspilationResult damengResult = damengTranspiler.transpile(sql);
        assertNotNull(damengResult, "达梦转换结果不应为空");
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        String damengSql = damengResult.getTargetSql();
        assertNotNull(damengSql, "达梦转换SQL不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "达梦转换应包含CREATE TABLE");
        
        // 测试金仓转换 - 金仓支持AUTO_INCREMENT或SERIAL
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(sql);
        assertNotNull(kingbaseResult, "金仓转换结果不应为空");
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertNotNull(kingbaseSql, "金仓转换SQL不应为空");
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "金仓转换应包含CREATE TABLE");
        
        // 测试神通转换 - 神通使用IDENTITY
        TranspilationResult shentongResult = shentongTranspiler.transpile(sql);
        assertNotNull(shentongResult, "神通转换结果不应为空");
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        String shentongSql = shentongResult.getTargetSql();
        assertNotNull(shentongSql, "神通转换SQL不应为空");
        assertTrue(shentongSql.contains("CREATE TABLE"), "神通转换应包含CREATE TABLE");
    }
    
    @Test
    @DisplayName("复合数据类型转换")
    void testComplexTypeConversion() {
        String sql = "CREATE TABLE test_complex (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "email VARCHAR(255) UNIQUE, " +
                    "age TINYINT UNSIGNED, " +
                    "salary DECIMAL(10,2) DEFAULT 0.00, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ")";
        
        // 测试达梦转换
        TranspilationResult damengResult = damengTranspiler.transpile(sql);
        assertNotNull(damengResult, "达梦转换结果不应为空");
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        String damengSql = damengResult.getTargetSql();
        assertNotNull(damengSql, "达梦转换SQL不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "达梦转换应包含CREATE TABLE");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = kingbaseTranspiler.transpile(sql);
        assertNotNull(kingbaseResult, "金仓转换结果不应为空");
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertNotNull(kingbaseSql, "金仓转换SQL不应为空");
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "金仓转换应包含CREATE TABLE");
        
        // 测试神通转换
        TranspilationResult shentongResult = shentongTranspiler.transpile(sql);
        assertNotNull(shentongResult, "神通转换结果不应为空");
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        String shentongSql = shentongResult.getTargetSql();
        assertNotNull(shentongSql, "神通转换SQL不应为空");
        assertTrue(shentongSql.contains("CREATE TABLE"), "神通转换应包含CREATE TABLE");
    }
}
