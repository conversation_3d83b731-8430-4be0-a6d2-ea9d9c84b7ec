package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * 最终验证测试
 * 
 * 全面验证SQL转换器的所有功能
 */
public class FinalValidationTest {
    
    @Test
    @DisplayName("全面验证SQL转换器功能")
    void testCompleteTranspilerFunctionality() {
        System.out.println("=== SQL转换器全面功能验证 ===");
        
        // 1. CREATE TABLE语句验证
        validateCreateTableTranspilation();
        
        // 2. DML语句验证
        validateDmlTranspilation();
        
        // 3. DDL语句验证
        validateDdlTranspilation();
        
        // 4. MySQL特有语法验证
        validateMysqlSpecificSyntax();
        
        // 5. 函数转换验证
        validateFunctionTranspilation();
        
        // 6. 跨数据库兼容性验证
        validateCrossDatabaseCompatibility();
        
        System.out.println("✅ 所有功能验证完成！");
    }
    
    private void validateCreateTableTranspilation() {
        System.out.println("\n--- CREATE TABLE语句验证 ---");
        
        String createTableSql = "CREATE TABLE users (" +
                               "id INT AUTO_INCREMENT PRIMARY KEY, " +
                               "name VARCHAR(100) NOT NULL, " +
                               "email VARCHAR(255) UNIQUE, " +
                               "age TINYINT, " +
                               "status ENUM('active','inactive') DEFAULT 'active', " +
                               "data JSON, " +
                               "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                               ")";
        
        // 达梦转换
        TranspilationResult damengResult = TranspilerBuilder.mysqlToDameng().transpile(createTableSql);
        assertTrue(damengResult.isSuccess(), "达梦CREATE TABLE转换应该成功");
        
        String damengSql = damengResult.getTargetSql();
        assertTrue(damengSql.contains("IDENTITY"), "应包含IDENTITY");
        assertTrue(damengSql.contains("INTEGER"), "TINYINT应转换为INTEGER");
        assertTrue(damengSql.contains("VARCHAR(255)"), "ENUM应转换为VARCHAR");
        assertTrue(damengSql.contains("CLOB"), "JSON应转换为CLOB");
        
        // 金仓转换
        TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase().transpile(createTableSql);
        assertTrue(kingbaseResult.isSuccess(), "金仓CREATE TABLE转换应该成功");
        
        System.out.println("✅ CREATE TABLE验证通过");
    }
    
    private void validateDmlTranspilation() {
        System.out.println("\n--- DML语句验证 ---");
        
        // INSERT
        String insertSql = "INSERT INTO users (name, age, email) VALUES ('John', 25, '<EMAIL>')";
        TranspilationResult insertResult = TranspilerBuilder.mysqlToDameng().transpile(insertSql);
        assertTrue(insertResult.isSuccess(), "INSERT转换应该成功");
        
        // UPDATE
        String updateSql = "UPDATE users SET age = 26, status = 'active' WHERE id = 1";
        TranspilationResult updateResult = TranspilerBuilder.mysqlToDameng().transpile(updateSql);
        assertTrue(updateResult.isSuccess(), "UPDATE转换应该成功");
        
        // DELETE
        String deleteSql = "DELETE FROM users WHERE age < 18 LIMIT 10";
        TranspilationResult deleteResult = TranspilerBuilder.mysqlToDameng().transpile(deleteSql);
        assertTrue(deleteResult.isSuccess(), "DELETE转换应该成功");
        
        // SELECT
        String selectSql = "SELECT id, name, age FROM users WHERE status = 'active' ORDER BY age DESC LIMIT 20";
        TranspilationResult selectResult = TranspilerBuilder.mysqlToDameng().transpile(selectSql);
        assertTrue(selectResult.isSuccess(), "SELECT转换应该成功");
        
        System.out.println("✅ DML语句验证通过");
    }
    
    private void validateDdlTranspilation() {
        System.out.println("\n--- DDL语句验证 ---");
        
        // CREATE INDEX
        String createIndexSql = "CREATE UNIQUE INDEX idx_user_email ON users (email)";
        TranspilationResult createIndexResult = TranspilerBuilder.mysqlToDameng().transpile(createIndexSql);
        assertTrue(createIndexResult.isSuccess(), "CREATE INDEX转换应该成功");
        
        // ALTER TABLE
        String alterTableSql = "ALTER TABLE users ADD COLUMN phone VARCHAR(20) NOT NULL";
        TranspilationResult alterTableResult = TranspilerBuilder.mysqlToDameng().transpile(alterTableSql);
        assertTrue(alterTableResult.isSuccess(), "ALTER TABLE转换应该成功");
        
        // DROP INDEX
        String dropIndexSql = "DROP INDEX idx_user_email ON users";
        TranspilationResult dropIndexResult = TranspilerBuilder.mysqlToDameng().transpile(dropIndexSql);
        assertTrue(dropIndexResult.isSuccess(), "DROP INDEX转换应该成功");
        
        // DROP TABLE
        String dropTableSql = "DROP TABLE IF EXISTS temp_table";
        TranspilationResult dropTableResult = TranspilerBuilder.mysqlToDameng().transpile(dropTableSql);
        assertTrue(dropTableResult.isSuccess(), "DROP TABLE转换应该成功");
        
        System.out.println("✅ DDL语句验证通过");
    }
    
    private void validateMysqlSpecificSyntax() {
        System.out.println("\n--- MySQL特有语法验证 ---");
        
        // REPLACE INTO
        String replaceIntoSql = "REPLACE INTO users (id, name) VALUES (1, 'John')";
        TranspilationResult replaceResult = TranspilerBuilder.mysqlToDameng().transpile(replaceIntoSql);
        assertTrue(replaceResult.isSuccess(), "REPLACE INTO转换应该成功");
        assertTrue(replaceResult.getTargetSql().contains("注意"), "应包含转换说明");
        
        // INSERT IGNORE
        String insertIgnoreSql = "INSERT IGNORE INTO users (name) VALUES ('Jane')";
        TranspilationResult insertIgnoreResult = TranspilerBuilder.mysqlToDameng().transpile(insertIgnoreSql);
        assertTrue(insertIgnoreResult.isSuccess(), "INSERT IGNORE转换应该成功");
        
        // ON DUPLICATE KEY UPDATE
        String onDuplicateSql = "INSERT INTO users (id, name) VALUES (1, 'John') ON DUPLICATE KEY UPDATE name = VALUES(name)";
        TranspilationResult onDuplicateResult = TranspilerBuilder.mysqlToDameng().transpile(onDuplicateSql);
        assertTrue(onDuplicateResult.isSuccess(), "ON DUPLICATE KEY UPDATE转换应该成功");
        
        System.out.println("✅ MySQL特有语法验证通过");
    }
    
    private void validateFunctionTranspilation() {
        System.out.println("\n--- 函数转换验证 ---");
        
        String functionSql = "SELECT " +
                           "CONCAT(first_name, ' ', last_name) as full_name, " +
                           "IF(age >= 18, 'Adult', 'Minor') as age_group, " +
                           "IFNULL(phone, 'No phone') as contact, " +
                           "ROUND(salary, 2) as rounded_salary, " +
                           "NOW() as current_time, " +
                           "COUNT(*) as total " +
                           "FROM employees " +
                           "GROUP BY age_group";
        
        // 达梦函数转换
        TranspilationResult damengResult = TranspilerBuilder.mysqlToDameng().transpile(functionSql);
        assertTrue(damengResult.isSuccess(), "达梦函数转换应该成功");
        
        String damengSql = damengResult.getTargetSql();
        assertTrue(damengSql.contains("CASE WHEN"), "IF应转换为CASE WHEN");
        assertTrue(damengSql.contains("NVL"), "IFNULL应转换为NVL");
        assertTrue(damengSql.contains("CONCAT"), "CONCAT应保持不变");
        
        // 金仓函数转换
        TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase().transpile(functionSql);
        assertTrue(kingbaseResult.isSuccess(), "金仓函数转换应该成功");
        
        String kingbaseSql = kingbaseResult.getTargetSql();
        assertTrue(kingbaseSql.contains("IF"), "金仓应保持IF函数");
        assertTrue(kingbaseSql.contains("IFNULL"), "金仓应保持IFNULL函数");
        
        System.out.println("✅ 函数转换验证通过");
    }
    
    private void validateCrossDatabaseCompatibility() {
        System.out.println("\n--- 跨数据库兼容性验证 ---");
        
        String testSql = "SELECT id, name, UPPER(status) as status_upper, COUNT(*) as cnt " +
                        "FROM users " +
                        "WHERE age BETWEEN 18 AND 65 " +
                        "GROUP BY status " +
                        "ORDER BY cnt DESC " +
                        "LIMIT 10";
        
        // 测试所有目标数据库
        TranspilationResult damengResult = TranspilerBuilder.mysqlToDameng().transpile(testSql);
        TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase().transpile(testSql);
        TranspilationResult shentongResult = TranspilerBuilder.mysqlToShentong().transpile(testSql);
        
        assertTrue(damengResult.isSuccess(), "达梦兼容性验证应该成功");
        assertTrue(kingbaseResult.isSuccess(), "金仓兼容性验证应该成功");
        assertTrue(shentongResult.isSuccess(), "神通兼容性验证应该成功");
        
        // 验证基础语法在所有数据库中都正确
        assertTrue(damengResult.getTargetSql().contains("SELECT"), "达梦应包含SELECT");
        assertTrue(kingbaseResult.getTargetSql().contains("SELECT"), "金仓应包含SELECT");
        assertTrue(shentongResult.getTargetSql().contains("SELECT"), "神通应包含SELECT");
        
        System.out.println("✅ 跨数据库兼容性验证通过");
    }
    
    @Test
    @DisplayName("验证转换结果的质量")
    void testTranspilationQuality() {
        System.out.println("=== 转换结果质量验证 ===");
        
        String complexSql = "CREATE TABLE orders (" +
                           "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                           "user_id INT NOT NULL, " +
                           "product_name VARCHAR(255) NOT NULL, " +
                           "quantity MEDIUMINT DEFAULT 1, " +
                           "price DECIMAL(10,2) NOT NULL, " +
                           "status ENUM('pending','paid','shipped','delivered') DEFAULT 'pending', " +
                           "metadata JSON, " +
                           "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                           "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                           "INDEX idx_user_id (user_id), " +
                           "INDEX idx_status (status), " +
                           "FOREIGN KEY (user_id) REFERENCES users(id)" +
                           ")";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(complexSql);
        assertTrue(result.isSuccess(), "复杂CREATE TABLE转换应该成功");
        
        String targetSql = result.getTargetSql();
        
        // 验证数据类型映射
        assertTrue(targetSql.contains("BIGINT"), "BIGINT应保持不变");
        assertTrue(targetSql.contains("INTEGER"), "INT应转换为INTEGER");
        assertTrue(targetSql.contains("DECIMAL"), "DECIMAL应保持不变");
        assertTrue(targetSql.contains("VARCHAR(255)"), "ENUM应转换为VARCHAR");
        assertTrue(targetSql.contains("CLOB"), "JSON应转换为CLOB");
        
        // 验证AUTO_INCREMENT转换
        assertTrue(targetSql.contains("IDENTITY"), "AUTO_INCREMENT应转换为IDENTITY");
        
        // 验证约束处理
        assertTrue(targetSql.contains("PRIMARY KEY"), "主键约束应保持");
        assertTrue(targetSql.contains("NOT NULL"), "NOT NULL约束应保持");
        
        System.out.println("✅ 转换结果质量验证通过");
        System.out.println("\n转换结果示例：");
        System.out.println(targetSql);
    }
}
