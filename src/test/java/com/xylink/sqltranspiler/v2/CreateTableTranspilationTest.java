package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * CREATE TABLE语句转换测试
 * 
 * 验证MySQL CREATE TABLE语句到各种目标数据库的转换功能
 */
public class CreateTableTranspilationTest {
    
    @Test
    @DisplayName("测试基础CREATE TABLE转换到达梦")
    void testBasicCreateTableToDameng() {
        String mysqlSql = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100) NOT NULL)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 基础CREATE TABLE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(targetSql.contains("users"), "应包含表名");
        assertTrue(targetSql.contains("INTEGER"), "INT应转换为INTEGER");
        assertTrue(targetSql.contains("VARCHAR"), "VARCHAR应保持不变");
        assertTrue(targetSql.contains("NOT NULL"), "应保留NOT NULL约束");
    }
    
    @Test
    @DisplayName("测试基础CREATE TABLE转换到金仓")
    void testBasicCreateTableToKingbase() {
        String mysqlSql = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100) NOT NULL)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== 基础CREATE TABLE转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(targetSql.contains("users"), "应包含表名");
    }
    
    @Test
    @DisplayName("测试基础CREATE TABLE转换到神通")
    void testBasicCreateTableToShentong() {
        String mysqlSql = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100) NOT NULL)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToShentong().transpile(mysqlSql);
        
        System.out.println("=== 基础CREATE TABLE转换到神通 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(targetSql.contains("users"), "应包含表名");
    }
    
    @Test
    @DisplayName("测试复杂CREATE TABLE转换到达梦")
    void testComplexCreateTableToDameng() {
        String mysqlSql = "CREATE TABLE orders (" +
                         "id INT AUTO_INCREMENT PRIMARY KEY, " +
                         "user_id INT NOT NULL, " +
                         "order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                         "total_amount DECIMAL(10,2) NOT NULL, " +
                         "status ENUM('pending','completed','cancelled') DEFAULT 'pending', " +
                         "notes TEXT" +
                         ")";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 复杂CREATE TABLE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(targetSql.contains("orders"), "应包含表名");
        assertTrue(targetSql.contains("DECIMAL"), "DECIMAL应保持不变");
        assertTrue(targetSql.contains("CLOB"), "TEXT应转换为CLOB");
    }
    
    @Test
    @DisplayName("测试数据类型映射")
    void testDataTypeMappingToDameng() {
        String mysqlSql = "CREATE TABLE test_types (" +
                         "tiny_col TINYINT, " +
                         "medium_col MEDIUMINT, " +
                         "text_col TEXT, " +
                         "json_col JSON, " +
                         "enum_col ENUM('a','b','c')" +
                         ")";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 数据类型映射测试 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("SMALLINT"), "TINYINT应转换为SMALLINT");
        assertTrue(targetSql.contains("INTEGER"), "MEDIUMINT应转换为INTEGER");
        assertTrue(targetSql.contains("CLOB"), "TEXT应转换为CLOB");
        assertTrue(targetSql.contains("VARCHAR"), "ENUM应转换为VARCHAR");
    }
}
