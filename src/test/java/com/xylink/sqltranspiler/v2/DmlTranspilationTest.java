package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * DML语句转换测试
 * 
 * 验证MySQL DML语句（INSERT、UPDATE、DELETE）到各种目标数据库的转换功能
 * 
 * 根据官方文档：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/sql-data-manipulation-statements.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 */
public class DmlTranspilationTest {
    
    @Test
    @DisplayName("测试基础INSERT转换到达梦")
    void testBasicInsertToDameng() {
        String mysqlSql = "INSERT INTO users (name, age, email) VALUES ('John', 25, '<EMAIL>')";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 基础INSERT转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("INSERT INTO"), "应包含INSERT INTO");
        assertTrue(targetSql.contains("users"), "应包含表名");
        assertTrue(targetSql.contains("VALUES"), "应包含VALUES");
    }
    
    @Test
    @DisplayName("测试多行INSERT转换到达梦")
    void testMultiRowInsertToDameng() {
        String mysqlSql = "INSERT INTO users (name, age) VALUES ('John', 25), ('Jane', 30), ('Bob', 35)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 多行INSERT转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("INSERT INTO"), "应包含INSERT INTO");
        assertTrue(targetSql.contains("VALUES"), "应包含VALUES");
    }
    
    @Test
    @DisplayName("测试INSERT IGNORE转换到达梦")
    void testInsertIgnoreToDameng() {
        String mysqlSql = "INSERT IGNORE INTO users (name, age) VALUES ('John', 25)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== INSERT IGNORE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        // 达梦不支持INSERT IGNORE，应该转换为其他形式或添加注释
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("INSERT"), "应包含INSERT");
    }
    
    @Test
    @DisplayName("测试ON DUPLICATE KEY UPDATE转换到达梦")
    void testOnDuplicateKeyUpdateToDameng() {
        String mysqlSql = "INSERT INTO users (id, name, age) VALUES (1, 'John', 25) ON DUPLICATE KEY UPDATE age = VALUES(age)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== ON DUPLICATE KEY UPDATE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        // 达梦不支持ON DUPLICATE KEY UPDATE，应该转换为MERGE语句
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("MERGE") || targetSql.contains("INSERT"), "应包含MERGE或INSERT");
    }
    
    @Test
    @DisplayName("测试基础UPDATE转换到达梦")
    void testBasicUpdateToDameng() {
        String mysqlSql = "UPDATE users SET age = 26, email = '<EMAIL>' WHERE id = 1";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 基础UPDATE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("UPDATE"), "应包含UPDATE");
        assertTrue(targetSql.contains("SET"), "应包含SET");
        assertTrue(targetSql.contains("WHERE"), "应包含WHERE");
    }
    
    @Test
    @DisplayName("测试多表UPDATE转换到达梦")
    void testMultiTableUpdateToDameng() {
        String mysqlSql = "UPDATE users u JOIN orders o ON u.id = o.user_id SET u.last_order_date = o.order_date WHERE o.status = 'completed'";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 多表UPDATE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("UPDATE"), "应包含UPDATE");
        assertTrue(targetSql.contains("JOIN"), "应包含JOIN");
    }
    
    @Test
    @DisplayName("测试基础DELETE转换到达梦")
    void testBasicDeleteToDameng() {
        String mysqlSql = "DELETE FROM users WHERE age < 18";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 基础DELETE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("DELETE"), "应包含DELETE");
        assertTrue(targetSql.contains("FROM"), "应包含FROM");
        assertTrue(targetSql.contains("WHERE"), "应包含WHERE");
    }
    
    @Test
    @DisplayName("测试多表DELETE转换到达梦")
    void testMultiTableDeleteToDameng() {
        String mysqlSql = "DELETE u FROM users u JOIN orders o ON u.id = o.user_id WHERE o.status = 'cancelled'";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 多表DELETE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("DELETE"), "应包含DELETE");
        assertTrue(targetSql.contains("JOIN"), "应包含JOIN");
    }
    
    @Test
    @DisplayName("测试基础INSERT转换到金仓")
    void testBasicInsertToKingbase() {
        String mysqlSql = "INSERT INTO users (name, age, email) VALUES ('John', 25, '<EMAIL>')";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== 基础INSERT转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("INSERT INTO"), "应包含INSERT INTO");
        assertTrue(targetSql.contains("users"), "应包含表名");
        assertTrue(targetSql.contains("VALUES"), "应包含VALUES");
    }
    
    @Test
    @DisplayName("测试基础UPDATE转换到金仓")
    void testBasicUpdateToKingbase() {
        String mysqlSql = "UPDATE users SET age = 26 WHERE id = 1";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== 基础UPDATE转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("UPDATE"), "应包含UPDATE");
        assertTrue(targetSql.contains("SET"), "应包含SET");
        assertTrue(targetSql.contains("WHERE"), "应包含WHERE");
    }
    
    @Test
    @DisplayName("测试基础DELETE转换到金仓")
    void testBasicDeleteToKingbase() {
        String mysqlSql = "DELETE FROM users WHERE age < 18";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== 基础DELETE转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("DELETE"), "应包含DELETE");
        assertTrue(targetSql.contains("FROM"), "应包含FROM");
        assertTrue(targetSql.contains("WHERE"), "应包含WHERE");
    }
}
