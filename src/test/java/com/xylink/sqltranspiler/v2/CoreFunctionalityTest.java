package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * 核心功能验证测试
 * 
 * 验证SQL转换器的核心功能是否正常工作
 */
public class CoreFunctionalityTest {
    
    @Test
    @DisplayName("验证完整的SQL转换流程")
    void testCompleteTranspilationFlow() {
        // 测试各种SQL语句类型的转换
        
        // 1. CREATE TABLE
        String createTableSql = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))";
        TranspilationResult result1 = TranspilerBuilder.mysqlToDameng().transpile(createTableSql);
        assertTrue(result1.isSuccess(), "CREATE TABLE转换应该成功");
        assertTrue(result1.getTargetSql().contains("IDENTITY"), "应包含IDENTITY");
        
        // 2. SELECT
        String selectSql = "SELECT id, name FROM users WHERE age > 18 LIMIT 10";
        TranspilationResult result2 = TranspilerBuilder.mysqlToDameng().transpile(selectSql);
        assertTrue(result2.isSuccess(), "SELECT转换应该成功");
        assertTrue(result2.getTargetSql().contains("SELECT"), "应包含SELECT");
        
        // 3. INSERT
        String insertSql = "INSERT INTO users (name, age) VALUES ('John', 25)";
        TranspilationResult result3 = TranspilerBuilder.mysqlToDameng().transpile(insertSql);
        assertTrue(result3.isSuccess(), "INSERT转换应该成功");
        assertTrue(result3.getTargetSql().contains("INSERT"), "应包含INSERT");
        
        // 4. UPDATE
        String updateSql = "UPDATE users SET age = 26 WHERE id = 1";
        TranspilationResult result4 = TranspilerBuilder.mysqlToDameng().transpile(updateSql);
        assertTrue(result4.isSuccess(), "UPDATE转换应该成功");
        assertTrue(result4.getTargetSql().contains("UPDATE"), "应包含UPDATE");
        
        // 5. DELETE
        String deleteSql = "DELETE FROM users WHERE age < 18";
        TranspilationResult result5 = TranspilerBuilder.mysqlToDameng().transpile(deleteSql);
        assertTrue(result5.isSuccess(), "DELETE转换应该成功");
        assertTrue(result5.getTargetSql().contains("DELETE"), "应包含DELETE");
        
        // 6. CREATE INDEX
        String createIndexSql = "CREATE INDEX idx_name ON users (name)";
        TranspilationResult result6 = TranspilerBuilder.mysqlToDameng().transpile(createIndexSql);
        assertTrue(result6.isSuccess(), "CREATE INDEX转换应该成功");
        assertTrue(result6.getTargetSql().contains("CREATE INDEX"), "应包含CREATE INDEX");
        
        // 7. ALTER TABLE
        String alterTableSql = "ALTER TABLE users ADD COLUMN email VARCHAR(100)";
        TranspilationResult result7 = TranspilerBuilder.mysqlToDameng().transpile(alterTableSql);
        assertTrue(result7.isSuccess(), "ALTER TABLE转换应该成功");
        assertTrue(result7.getTargetSql().contains("ALTER TABLE"), "应包含ALTER TABLE");
        
        System.out.println("=== 完整SQL转换流程验证成功 ===");
        System.out.println("✅ CREATE TABLE: " + result1.isSuccess());
        System.out.println("✅ SELECT: " + result2.isSuccess());
        System.out.println("✅ INSERT: " + result3.isSuccess());
        System.out.println("✅ UPDATE: " + result4.isSuccess());
        System.out.println("✅ DELETE: " + result5.isSuccess());
        System.out.println("✅ CREATE INDEX: " + result6.isSuccess());
        System.out.println("✅ ALTER TABLE: " + result7.isSuccess());
    }
    
    @Test
    @DisplayName("验证数据类型映射")
    void testDataTypeMapping() {
        String mysqlSql = "CREATE TABLE test_types (tiny_col TINYINT, medium_col MEDIUMINT, text_col TEXT, json_col JSON)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        assertTrue(result.isSuccess(), "数据类型映射转换应该成功");
        String targetSql = result.getTargetSql();
        
        assertTrue(targetSql.contains("SMALLINT"), "TINYINT应转换为SMALLINT");
        assertTrue(targetSql.contains("INTEGER"), "MEDIUMINT应转换为INTEGER");
        assertTrue(targetSql.contains("CLOB"), "TEXT和JSON应转换为CLOB");
        
        System.out.println("=== 数据类型映射验证 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + targetSql);
    }
    
    @Test
    @DisplayName("验证MySQL特有语法处理")
    void testMysqlSpecificSyntax() {
        // 测试AUTO_INCREMENT
        String autoIncrementSql = "CREATE TABLE test (id INT AUTO_INCREMENT PRIMARY KEY)";
        TranspilationResult result1 = TranspilerBuilder.mysqlToDameng().transpile(autoIncrementSql);
        assertTrue(result1.isSuccess(), "AUTO_INCREMENT转换应该成功");
        assertTrue(result1.getTargetSql().contains("IDENTITY"), "应转换为IDENTITY");
        
        // 测试REPLACE INTO
        String replaceIntoSql = "REPLACE INTO test (id, name) VALUES (1, 'John')";
        TranspilationResult result2 = TranspilerBuilder.mysqlToDameng().transpile(replaceIntoSql);
        assertTrue(result2.isSuccess(), "REPLACE INTO转换应该成功");
        assertTrue(result2.getTargetSql().contains("注意"), "应包含转换说明");
        
        System.out.println("=== MySQL特有语法处理验证 ===");
        System.out.println("✅ AUTO_INCREMENT: " + result1.isSuccess());
        System.out.println("✅ REPLACE INTO: " + result2.isSuccess());
    }
    
    @Test
    @DisplayName("验证多数据库支持")
    void testMultipleDatabaseSupport() {
        String testSql = "CREATE TABLE test (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))";
        
        // 测试达梦
        TranspilationResult damengResult = TranspilerBuilder.mysqlToDameng().transpile(testSql);
        assertTrue(damengResult.isSuccess(), "达梦转换应该成功");
        assertTrue(damengResult.getTargetSql().contains("IDENTITY"), "达梦应使用IDENTITY");
        
        // 测试金仓
        TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase().transpile(testSql);
        assertTrue(kingbaseResult.isSuccess(), "金仓转换应该成功");
        assertTrue(kingbaseResult.getTargetSql().contains("AUTO_INCREMENT"), "金仓应保持AUTO_INCREMENT");
        
        // 测试神通
        TranspilationResult shentongResult = TranspilerBuilder.mysqlToShentong().transpile(testSql);
        assertTrue(shentongResult.isSuccess(), "神通转换应该成功");
        
        System.out.println("=== 多数据库支持验证 ===");
        System.out.println("✅ 达梦: " + damengResult.isSuccess());
        System.out.println("✅ 金仓: " + kingbaseResult.isSuccess());
        System.out.println("✅ 神通: " + shentongResult.isSuccess());
    }
    
    @Test
    @DisplayName("验证错误处理")
    void testErrorHandling() {
        // 测试空SQL
        TranspilationResult result1 = TranspilerBuilder.mysqlToDameng().transpile("");
        assertFalse(result1.isSuccess(), "空SQL应该失败");
        
        // 测试null SQL
        TranspilationResult result2 = TranspilerBuilder.mysqlToDameng().transpile(null);
        assertFalse(result2.isSuccess(), "null SQL应该失败");
        
        // 测试无效SQL
        TranspilationResult result3 = TranspilerBuilder.mysqlToDameng().transpile("INVALID SQL STATEMENT");
        assertTrue(result3.isSuccess(), "无效SQL应该返回TODO注释");
        assertTrue(result3.getTargetSql().contains("TODO"), "应包含TODO注释");
        
        System.out.println("=== 错误处理验证 ===");
        System.out.println("✅ 空SQL处理: " + !result1.isSuccess());
        System.out.println("✅ null SQL处理: " + !result2.isSuccess());
        System.out.println("✅ 无效SQL处理: " + result3.isSuccess());
    }
}
