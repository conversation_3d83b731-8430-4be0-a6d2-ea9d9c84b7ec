package com.xylink.sqltranspiler.v2;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

/**
 * DDL语句转换测试
 * 
 * 验证MySQL DDL语句（CREATE INDEX、DROP INDEX、ALTER TABLE等）到各种目标数据库的转换功能
 * 
 * 根据官方文档：
 * - MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/sql-data-definition-statements.html
 * - 达梦官方文档：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * - 金仓官方文档：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
 */
public class DdlTranspilationTest {
    
    @Test
    @DisplayName("测试CREATE INDEX转换到达梦")
    void testCreateIndexToDameng() {
        String mysqlSql = "CREATE INDEX idx_user_email ON users (email)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== CREATE INDEX转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE INDEX"), "应包含CREATE INDEX");
        assertTrue(targetSql.contains("idx_user_email"), "应包含索引名");
        assertTrue(targetSql.contains("users"), "应包含表名");
        assertTrue(targetSql.contains("email"), "应包含列名");
    }
    
    @Test
    @DisplayName("测试CREATE UNIQUE INDEX转换到达梦")
    void testCreateUniqueIndexToDameng() {
        String mysqlSql = "CREATE UNIQUE INDEX idx_user_username ON users (username)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== CREATE UNIQUE INDEX转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE UNIQUE INDEX"), "应包含CREATE UNIQUE INDEX");
        assertTrue(targetSql.contains("idx_user_username"), "应包含索引名");
    }
    
    @Test
    @DisplayName("测试复合索引转换到达梦")
    void testCompositeIndexToDameng() {
        String mysqlSql = "CREATE INDEX idx_user_name_age ON users (last_name, first_name, age)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== 复合索引转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE INDEX"), "应包含CREATE INDEX");
        assertTrue(targetSql.contains("last_name"), "应包含第一个列名");
        assertTrue(targetSql.contains("first_name"), "应包含第二个列名");
        assertTrue(targetSql.contains("age"), "应包含第三个列名");
    }
    
    @Test
    @DisplayName("测试DROP INDEX转换到达梦")
    void testDropIndexToDameng() {
        String mysqlSql = "DROP INDEX idx_user_email ON users";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== DROP INDEX转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("DROP INDEX"), "应包含DROP INDEX");
        assertTrue(targetSql.contains("idx_user_email"), "应包含索引名");
    }
    
    @Test
    @DisplayName("测试ALTER TABLE ADD COLUMN转换到达梦")
    void testAlterTableAddColumnToDameng() {
        String mysqlSql = "ALTER TABLE users ADD COLUMN phone VARCHAR(20) NOT NULL";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== ALTER TABLE ADD COLUMN转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(targetSql.contains("ADD COLUMN"), "应包含ADD COLUMN");
        assertTrue(targetSql.contains("phone"), "应包含列名");
        assertTrue(targetSql.contains("VARCHAR"), "应包含数据类型");
    }
    
    @Test
    @DisplayName("测试ALTER TABLE DROP COLUMN转换到达梦")
    void testAlterTableDropColumnToDameng() {
        String mysqlSql = "ALTER TABLE users DROP COLUMN phone";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== ALTER TABLE DROP COLUMN转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(targetSql.contains("DROP COLUMN"), "应包含DROP COLUMN");
        assertTrue(targetSql.contains("phone"), "应包含列名");
    }
    
    @Test
    @DisplayName("测试ALTER TABLE MODIFY COLUMN转换到达梦")
    void testAlterTableModifyColumnToDameng() {
        String mysqlSql = "ALTER TABLE users MODIFY COLUMN age INT NOT NULL DEFAULT 0";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== ALTER TABLE MODIFY COLUMN转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(targetSql.contains("age"), "应包含列名");
        assertTrue(targetSql.contains("INTEGER"), "INT应转换为INTEGER");
    }
    
    @Test
    @DisplayName("测试DROP TABLE转换到达梦")
    void testDropTableToDameng() {
        String mysqlSql = "DROP TABLE IF EXISTS temp_users";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysqlSql);
        
        System.out.println("=== DROP TABLE转换到达梦 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("DROP TABLE"), "应包含DROP TABLE");
        assertTrue(targetSql.contains("temp_users"), "应包含表名");
    }
    
    @Test
    @DisplayName("测试CREATE INDEX转换到金仓")
    void testCreateIndexToKingbase() {
        String mysqlSql = "CREATE INDEX idx_user_email ON users (email)";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== CREATE INDEX转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("CREATE INDEX"), "应包含CREATE INDEX");
        assertTrue(targetSql.contains("idx_user_email"), "应包含索引名");
        assertTrue(targetSql.contains("users"), "应包含表名");
        assertTrue(targetSql.contains("email"), "应包含列名");
    }
    
    @Test
    @DisplayName("测试ALTER TABLE转换到金仓")
    void testAlterTableToKingbase() {
        String mysqlSql = "ALTER TABLE users ADD COLUMN phone VARCHAR(20) NOT NULL";
        
        TranspilationResult result = TranspilerBuilder.mysqlToKingbase().transpile(mysqlSql);
        
        System.out.println("=== ALTER TABLE转换到金仓 ===");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));
        System.out.println("成功: " + result.isSuccess());
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertNotNull(result.getTargetSql(), "目标SQL不应为空");
        
        String targetSql = result.getTargetSql();
        assertTrue(targetSql.contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(targetSql.contains("ADD COLUMN"), "应包含ADD COLUMN");
        assertTrue(targetSql.contains("phone"), "应包含列名");
        assertTrue(targetSql.contains("VARCHAR"), "应包含数据类型");
    }
}
