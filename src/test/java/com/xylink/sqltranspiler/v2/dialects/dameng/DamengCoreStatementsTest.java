package com.xylink.sqltranspiler.v2.dialects.dameng;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库核心语句测试 - v2.0版本
 * 
 * 根据达梦官方文档，测试MySQL核心SQL语句到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 严格遵循rule-db.md规范：
 * - 所有转换规则基于达梦官方文档
 * - 禁止推测和猜测
 * - 每个测试都有明确的官方文档依据
 * 
 * 测试覆盖：
 * - CREATE TABLE语句的核心功能
 * - 数据类型转换的核心支持
 * - 约束定义的核心转换
 * - 索引定义的核心转换
 */
@DisplayName("达梦数据库核心语句测试 - v2.0")
public class DamengCoreStatementsTest {

    private SqlTranspiler damengTranspiler;

    @BeforeEach
    void setUp() {
        damengTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(false)
            .strictMode(false)
            .timeout(Duration.ofSeconds(10))
            .build();
    }

    @Test
    @DisplayName("测试基础CREATE TABLE语句转换")
    void testBasicCreateTableStatement() {
        String mysqlSql = "CREATE TABLE basic_table (" +
                "id INT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "age INT" +
                ")";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("基础CREATE TABLE语句转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，验证基础CREATE TABLE语句转换
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE关键字");
        assertTrue(damengSql.contains("basic_table"), "应保留表名");
        assertTrue(damengSql.contains("id"), "应保留id列");
        assertTrue(damengSql.contains("name"), "应保留name列");
        assertTrue(damengSql.contains("age"), "应保留age列");
        assertTrue(damengSql.contains("INT"), "应保留INT数据类型");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR数据类型");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
    }

    @Test
    @DisplayName("测试核心数据类型转换")
    void testCoreDataTypeConversion() {
        String mysqlSql = "CREATE TABLE core_types (" +
                "int_col INT, " +
                "varchar_col VARCHAR(255), " +
                "text_col TEXT, " +
                "decimal_col DECIMAL(10,2), " +
                "date_col DATE, " +
                "timestamp_col TIMESTAMP" +
                ")";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("核心数据类型转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，验证核心数据类型转换
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("core_types"), "应保留表名");
        assertTrue(damengSql.contains("INT"), "应保留INT类型");
        assertTrue(damengSql.contains("VARCHAR(255)"), "应保留VARCHAR类型");
        // 根据达梦官方文档，TEXT可能转换为CLOB或保留TEXT
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
    }

    @Test
    @DisplayName("测试AUTO_INCREMENT转换")
    void testAutoIncrementConversion() {
        String mysqlSql = "CREATE TABLE auto_increment_test (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL" +
                ")";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("AUTO_INCREMENT转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，AUTO_INCREMENT应转换为IDENTITY
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("auto_increment_test"), "应保留表名");
        // 根据达梦官方文档，AUTO_INCREMENT转换为IDENTITY
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为IDENTITY或保留");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
    }

    @Test
    @DisplayName("测试核心约束转换")
    void testCoreConstraintConversion() {
        String mysqlSql = "CREATE TABLE core_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "email VARCHAR(100) NOT NULL UNIQUE, " +
                "age INT CHECK (age >= 0), " +
                "status VARCHAR(20) DEFAULT 'active'" +
                ")";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("核心约束转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，验证核心约束转换
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("core_constraints"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为IDENTITY或保留");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("CHECK") || damengSql.contains("age"), 
                  "应处理CHECK约束");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("'active'"), "应保留字符串默认值");
    }

    @Test
    @DisplayName("测试时间戳和默认值转换")
    void testTimestampAndDefaultValueConversion() {
        String mysqlSql = "CREATE TABLE timestamp_defaults (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "status VARCHAR(20) DEFAULT 'pending', " +
                "count_value INT DEFAULT 0" +
                ")";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("时间戳和默认值转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，验证时间戳和默认值转换
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("timestamp_defaults"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为IDENTITY或保留");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 根据达梦官方文档，CURRENT_TIMESTAMP可能转换为SYSDATE
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数为SYSDATE或保留CURRENT_TIMESTAMP");
        assertTrue(damengSql.contains("'pending'"), "应保留字符串默认值");
        assertTrue(damengSql.contains("DEFAULT 0"), "应保留数值默认值");
    }

    @Test
    @DisplayName("测试复合主键转换")
    void testCompositeKeyConversion() {
        String mysqlSql = "CREATE TABLE composite_key (" +
                "user_id INT NOT NULL, " +
                "role_id INT NOT NULL, " +
                "assigned_date DATE NOT NULL, " +
                "status VARCHAR(20) DEFAULT 'active', " +
                "PRIMARY KEY (user_id, role_id)" +
                ")";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("复合主键转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，验证复合主键转换
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("composite_key"), "应保留表名");
        assertTrue(damengSql.contains("user_id"), "应保留user_id列");
        assertTrue(damengSql.contains("role_id"), "应保留role_id列");
        assertTrue(damengSql.contains("assigned_date"), "应保留assigned_date列");
        assertTrue(damengSql.contains("status"), "应保留status列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留复合主键");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("'active'"), "应保留字符串默认值");
    }

    @Test
    @DisplayName("测试LIMIT语句转换")
    void testLimitStatementConversion() {
        String mysqlSql = "SELECT * FROM users WHERE age > 18 LIMIT 10";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("LIMIT语句转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，验证LIMIT转换
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("SELECT"), "应保留SELECT");
        assertTrue(damengSql.contains("users"), "应保留表名");
        assertTrue(damengSql.contains("age"), "应保留列名");
        // 根据达梦官方文档，LIMIT可能转换为ROWNUM或保留LIMIT
        assertTrue(damengSql.contains("ROWNUM") || damengSql.contains("LIMIT") || 
                  damengSql.contains("TOP"), "应转换LIMIT为ROWNUM、保留LIMIT或转换为TOP");
    }

    @Test
    @DisplayName("测试INSERT语句转换")
    void testInsertStatementConversion() {
        String mysqlSql = "INSERT INTO users (name, email, age) VALUES ('John', '<EMAIL>', 25)";

        TranspilationResult result = damengTranspiler.transpile(mysqlSql);

        System.out.println("INSERT语句转换结果:");
        System.out.println("输入: " + mysqlSql);
        System.out.println("输出: " + (result.isSuccess() ? result.getTargetSql() : result.getErrorMessage()));

        // 根据达梦官方文档，验证INSERT语句转换
        assertTrue(result.isSuccess(), "转换应该成功");
        String damengSql = result.getTargetSql();
        assertNotNull(damengSql, "转换结果不应为空");
        assertTrue(damengSql.contains("INSERT INTO"), "应保留INSERT INTO");
        assertTrue(damengSql.contains("users"), "应保留表名");
        assertTrue(damengSql.contains("name"), "应保留列名");
        assertTrue(damengSql.contains("email"), "应保留列名");
        assertTrue(damengSql.contains("age"), "应保留列名");
        assertTrue(damengSql.contains("VALUES"), "应保留VALUES");
        assertTrue(damengSql.contains("'John'"), "应保留字符串值");
        assertTrue(damengSql.contains("'<EMAIL>'"), "应保留邮箱值");
        assertTrue(damengSql.contains("25"), "应保留数值");
    }
}
