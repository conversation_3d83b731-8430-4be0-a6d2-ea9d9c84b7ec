-- 达梦数据库预期输出 - MySQL官方DDL示例转换结果
-- 
-- 官方文档依据：
-- - 达梦数据库官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-ddl.html
--   第4章 DDL语句 - 达梦数据库DDL语法规范
-- - 达梦数据库官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
--   第3章 数据类型 - 达梦数据库数据类型规范
-- 
-- 验证日期: 2024-01-15
-- 
-- 转换原则：严格基于达梦数据库官方文档进行转换，确保转换结果符合达梦语法规范
-- 
-- 官方文档搜索记录：
-- - 搜索关键词："AUTO_INCREMENT"、"自增"、"IDENTITY"
-- - 搜索结果：达梦数据库使用IDENTITY(1,1)语法实现自增
-- - 搜索关键词："TIMESTAMP"、"时间戳"、"DEFAULT CURRENT_TIMESTAMP"
-- - 搜索结果：达梦数据库支持TIMESTAMP和DEFAULT CURRENT_TIMESTAMP

-- 基本表创建转换结果
-- MySQL AUTO_INCREMENT转换为达梦IDENTITY
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据类型转换结果
-- 根据达梦官方文档，大部分MySQL数据类型在达梦中直接支持
CREATE TABLE data_types_example (
    -- 整数类型 - 达梦支持INT、BIGINT
    int_col INT,
    bigint_col BIGINT,
    
    -- 精确数值类型 - 达梦支持DECIMAL
    decimal_col DECIMAL(10,2),
    
    -- 字符串类型 - 达梦支持VARCHAR、TEXT
    varchar_col VARCHAR(255),
    text_col TEXT,
    
    -- 日期时间类型 - 达梦支持DATE、TIMESTAMP
    date_col DATE,
    timestamp_col TIMESTAMP
);

-- 约束和索引转换结果
-- 达梦数据库支持标准的约束和索引语法
CREATE TABLE orders (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id)
);

-- 索引创建转换结果
-- 根据达梦官方文档，CREATE INDEX语法与MySQL兼容
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_created_at ON users (created_at);
