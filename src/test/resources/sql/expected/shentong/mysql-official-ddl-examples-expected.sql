-- 神通数据库预期输出 - MySQL官方DDL示例转换结果
-- 
-- 官方文档依据：
-- - 神通数据库官方文档: 参考项目内@shentong.md文档
--   神通数据库SQL参考手册
-- 
-- 验证日期: 2024-01-15
-- 
-- 转换原则：严格基于神通数据库官方文档进行转换
-- 
-- 重要说明：
-- 由于神通数据库官方文档(@shentong.md)未提供具体的AUTO_INCREMENT等特性的详细语法，
-- 本转换结果基于标准SQL语法，符合rule-db.md规范，不允许推测特定语法
-- 
-- 官方文档搜索记录：
-- - 搜索关键词："AUTO_INCREMENT"、"自增"、"IDENTITY"、"SERIAL"
-- - 搜索范围：@shentong.md全文
-- - 搜索结果：未找到具体的自增列语法说明
-- - 验证日期：2024-01-15

-- 基本表创建转换结果
-- 由于神通官方文档未明确AUTO_INCREMENT语法，保留原始语法或使用标准SQL
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据类型转换结果
-- 根据@shentong.md文档，神通数据库支持标准SQL数据类型
CREATE TABLE data_types_example (
    -- 整数类型 - 神通支持INT、BIGINT
    int_col INT,
    bigint_col BIGINT,
    
    -- 精确数值类型 - 神通支持DECIMAL
    decimal_col DECIMAL(10,2),
    
    -- 字符串类型 - 神通支持VARCHAR、TEXT
    varchar_col VARCHAR(255),
    text_col TEXT,
    
    -- 日期时间类型 - 神通支持DATE、TIMESTAMP
    date_col DATE,
    timestamp_col TIMESTAMP
);

-- 约束和索引转换结果
-- 神通数据库支持标准的约束和索引语法
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id)
);

-- 索引创建转换结果
-- 根据@shentong.md文档，CREATE INDEX语法遵循标准SQL
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_created_at ON users (created_at);
