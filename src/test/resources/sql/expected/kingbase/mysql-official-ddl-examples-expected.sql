-- 金仓数据库预期输出 - MySQL官方DDL示例转换结果
-- 
-- 官方文档依据：
-- - 金仓数据库官方文档: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
--   金仓数据库SQL参考手册
-- - PostgreSQL官方文档: https://www.postgresql.org/docs/current/sql-createtable.html
--   CREATE TABLE语法（金仓基于PostgreSQL内核）
-- 
-- 验证日期: 2024-01-15
-- 
-- 转换原则：基于金仓数据库官方文档，金仓数据库基于PostgreSQL内核，
-- 因此大部分PostgreSQL语法在金仓中直接支持
-- 
-- 官方文档验证结果：
-- - 金仓数据库支持PostgreSQL的SERIAL类型实现自增
-- - 金仓数据库支持PostgreSQL的所有标准数据类型
-- - 金仓数据库支持PostgreSQL的约束和索引语法

-- 基本表创建转换结果
-- MySQL AUTO_INCREMENT转换为金仓SERIAL
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据类型转换结果
-- 根据金仓官方文档，支持PostgreSQL标准数据类型
CREATE TABLE data_types_example (
    -- 整数类型 - 金仓支持INTEGER、BIGINT
    int_col INTEGER,
    bigint_col BIGINT,
    
    -- 精确数值类型 - 金仓支持DECIMAL/NUMERIC
    decimal_col DECIMAL(10,2),
    
    -- 字符串类型 - 金仓支持VARCHAR、TEXT
    varchar_col VARCHAR(255),
    text_col TEXT,
    
    -- 日期时间类型 - 金仓支持DATE、TIMESTAMP
    date_col DATE,
    timestamp_col TIMESTAMP
);

-- 约束和索引转换结果
-- 金仓数据库支持PostgreSQL标准的约束和索引语法
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引创建转换结果
-- 根据金仓官方文档，CREATE INDEX语法与PostgreSQL兼容
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_created_at ON users (created_at);

-- 为orders表添加索引
CREATE INDEX idx_orders_user_id ON orders (user_id);
