-- MySQL官方DDL示例测试数据
-- 
-- 官方文档依据：
-- - MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
--   第15.1.20节 CREATE TABLE Statement - 官方DDL语法示例
-- - MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/data-types.html
--   第13章 Data Types - 官方数据类型示例
-- 
-- 验证日期: 2024-01-15
-- 
-- 测试原则：严格基于MySQL官方文档的示例，确保测试数据的权威性和准确性
-- 
-- 特性覆盖：
-- 1. 基本CREATE TABLE语法
-- 2. 各种数据类型
-- 3. 约束定义
-- 4. 索引创建
-- 5. AUTO_INCREMENT特性

-- 基本表创建 - 基于MySQL官方文档示例
-- https://dev.mysql.com/doc/refman/8.4/en/create-table.html
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 数据类型示例 - 基于MySQL官方文档第13章
-- https://dev.mysql.com/doc/refman/8.4/en/data-types.html
CREATE TABLE data_types_example (
    -- 整数类型
    tiny_int_col TINYINT,
    small_int_col SMALLINT,
    medium_int_col MEDIUMINT,
    int_col INT,
    big_int_col BIGINT,
    
    -- 浮点类型
    float_col FLOAT,
    double_col DOUBLE,
    decimal_col DECIMAL(10,2),
    
    -- 字符串类型
    char_col CHAR(10),
    varchar_col VARCHAR(255),
    text_col TEXT,
    longtext_col LONGTEXT,
    
    -- 日期时间类型
    date_col DATE,
    time_col TIME,
    datetime_col DATETIME,
    timestamp_col TIMESTAMP,
    year_col YEAR,
    
    -- 布尔类型
    bool_col BOOLEAN,
    
    -- JSON类型 (MySQL 5.7+)
    json_col JSON
);

-- 约束示例 - 基于MySQL官方文档约束定义
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount > 0),
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 复合索引
    INDEX idx_user_status (user_id, status),
    INDEX idx_order_number (order_number),
    INDEX idx_created_at (created_at)
);

-- 分区表示例 - 基于MySQL官方文档分区功能
-- https://dev.mysql.com/doc/refman/8.4/en/partitioning.html
CREATE TABLE sales_data (
    id INT AUTO_INCREMENT,
    sale_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (id, sale_date)
) PARTITION BY RANGE (YEAR(sale_date)) (
    PARTITION p2022 VALUES LESS THAN (2023),
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 临时表示例 - 基于MySQL官方文档临时表语法
-- https://dev.mysql.com/doc/refman/8.4/en/create-temporary-table.html
CREATE TEMPORARY TABLE temp_calculations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    calculation_result DECIMAL(15,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 存储引擎示例 - 基于MySQL官方文档存储引擎选项
CREATE TABLE innodb_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data VARCHAR(255)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE myisam_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data VARCHAR(255)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4;

-- 全文索引示例 - 基于MySQL官方文档全文搜索
-- https://dev.mysql.com/doc/refman/8.4/en/fulltext-search.html
CREATE TABLE articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    author VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FULLTEXT(title, content)
) ENGINE=InnoDB;

-- 生成列示例 - 基于MySQL官方文档生成列功能
-- https://dev.mysql.com/doc/refman/8.4/en/create-table-generated-columns.html
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    tax_rate DECIMAL(4,4) DEFAULT 0.0825,
    
    -- 虚拟生成列
    price_with_tax DECIMAL(10,2) AS (price * (1 + tax_rate)) VIRTUAL,
    
    -- 存储生成列
    price_category VARCHAR(20) AS (
        CASE 
            WHEN price < 10 THEN 'Budget'
            WHEN price < 100 THEN 'Standard'
            ELSE 'Premium'
        END
    ) STORED,
    
    INDEX idx_price_category (price_category)
);

-- 检查约束示例 - 基于MySQL官方文档检查约束
-- https://dev.mysql.com/doc/refman/8.4/en/create-table-check-constraints.html
CREATE TABLE employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INT NOT NULL,
    salary DECIMAL(10,2) NOT NULL,
    department VARCHAR(50) NOT NULL,
    
    -- 检查约束
    CONSTRAINT chk_age CHECK (age >= 18 AND age <= 65),
    CONSTRAINT chk_salary CHECK (salary > 0),
    CONSTRAINT chk_department CHECK (department IN ('HR', 'IT', 'Finance', 'Marketing'))
);

-- 多列索引示例 - 基于MySQL官方文档索引优化
-- https://dev.mysql.com/doc/refman/8.4/en/multiple-column-indexes.html
CREATE TABLE user_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    activity_date DATE NOT NULL,
    activity_time TIME NOT NULL,
    description TEXT,
    
    -- 复合索引（顺序很重要）
    INDEX idx_user_activity (user_id, activity_type, activity_date),
    INDEX idx_date_type (activity_date, activity_type),
    
    -- 前缀索引
    INDEX idx_description_prefix (description(100))
);

-- 外键级联示例 - 基于MySQL官方文档外键约束
-- https://dev.mysql.com/doc/refman/8.4/en/create-table-foreign-keys.html
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    
    -- 外键约束与级联操作
    FOREIGN KEY (order_id) REFERENCES orders(id) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE,
    
    FOREIGN KEY (product_id) REFERENCES products(id) 
        ON DELETE RESTRICT 
        ON UPDATE CASCADE,
    
    -- 唯一约束
    UNIQUE KEY unique_order_product (order_id, product_id)
);

-- 自引用外键示例 - 基于MySQL官方文档自引用表设计
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    
    -- 自引用外键
    FOREIGN KEY (parent_id) REFERENCES categories(id) 
        ON DELETE SET NULL 
        ON UPDATE CASCADE,
    
    INDEX idx_parent (parent_id),
    INDEX idx_sort (sort_order)
);

-- 视图创建示例 - 基于MySQL官方文档视图语法
-- https://dev.mysql.com/doc/refman/8.4/en/create-view.html
CREATE VIEW user_order_summary AS
SELECT 
    u.id,
    u.username,
    u.email,
    COUNT(o.id) as total_orders,
    COALESCE(SUM(o.total_amount), 0) as total_spent,
    MAX(o.created_at) as last_order_date
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, u.username, u.email;

-- 索引创建示例 - 基于MySQL官方文档索引管理
-- https://dev.mysql.com/doc/refman/8.4/en/create-index.html
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_created_at ON users (created_at);
CREATE UNIQUE INDEX idx_users_username ON users (username);

-- 降序索引示例 - MySQL 8.0+功能
CREATE INDEX idx_orders_amount_desc ON orders (total_amount DESC);

-- 函数索引示例 - MySQL 8.0+功能
CREATE INDEX idx_users_email_domain ON users ((SUBSTRING_INDEX(email, '@', -1)));

-- 不可见索引示例 - MySQL 8.0+功能
CREATE INDEX idx_users_updated_at ON users (updated_at) INVISIBLE;
