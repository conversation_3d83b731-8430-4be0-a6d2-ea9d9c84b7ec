-- MySQL CREATE TABLE语句测试数据
-- 严格遵循MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/create-table.html

-- 基础CREATE TABLE语句
CREATE TABLE basic_users (
    id INT PRIMARY KEY,
    name VA<PERSON>HAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    age INT
);

-- AUTO_INCREMENT主键
CREATE TABLE auto_increment_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 复合主键
CREATE TABLE user_roles (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    assigned_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    PRIMARY KEY (user_id, role_id)
);

-- 外键约束
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    FOREIGN KEY (user_id) REFERENCES basic_users(id)
);

-- 多种数据类型
CREATE TABLE data_types_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tiny_int_col TINYINT,
    small_int_col SMALLINT,
    medium_int_col MEDIUMINT,
    int_col INT,
    big_int_col BIGINT,
    decimal_col DECIMAL(10,2),
    float_col FLOAT,
    double_col DOUBLE,
    char_col CHAR(10),
    varchar_col VARCHAR(255),
    text_col TEXT,
    mediumtext_col MEDIUMTEXT,
    longtext_col LONGTEXT,
    binary_col BINARY(16),
    varbinary_col VARBINARY(255),
    blob_col BLOB,
    date_col DATE,
    time_col TIME,
    datetime_col DATETIME,
    timestamp_col TIMESTAMP,
    year_col YEAR,
    json_col JSON,
    enum_col ENUM('small', 'medium', 'large'),
    set_col SET('read', 'write', 'execute')
);

-- CHECK约束
CREATE TABLE check_constraints_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    age INT CHECK (age >= 0 AND age <= 150),
    email VARCHAR(100) NOT NULL UNIQUE,
    salary DECIMAL(10,2) CHECK (salary > 0),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending'))
);

-- 索引定义
CREATE TABLE indexed_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    UNIQUE INDEX idx_email (email),
    INDEX idx_name (first_name, last_name),
    INDEX idx_created (created_at)
);

-- 表选项
CREATE TABLE table_options_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  AUTO_INCREMENT=1000 
  COMMENT='测试表选项';

-- 分区表
CREATE TABLE partitioned_table (
    id INT AUTO_INCREMENT,
    user_id INT NOT NULL,
    order_date DATE NOT NULL,
    amount DECIMAL(10,2),
    PRIMARY KEY (id, order_date)
) PARTITION BY RANGE (YEAR(order_date)) (
    PARTITION p2020 VALUES LESS THAN (2021),
    PARTITION p2021 VALUES LESS THAN (2022),
    PARTITION p2022 VALUES LESS THAN (2023),
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 临时表
CREATE TEMPORARY TABLE temp_calculations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    calculation_result DECIMAL(15,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 如果不存在则创建
CREATE TABLE IF NOT EXISTS conditional_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 基于查询创建表
CREATE TABLE users_backup AS 
SELECT id, name, email, created_at 
FROM basic_users 
WHERE created_at >= '2024-01-01';

-- 复杂的外键约束
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT ON UPDATE CASCADE
);

-- 生成列
CREATE TABLE generated_columns_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    full_name VARCHAR(101) GENERATED ALWAYS AS (CONCAT(first_name, ' ', last_name)) VIRTUAL,
    email VARCHAR(100) NOT NULL,
    email_domain VARCHAR(50) GENERATED ALWAYS AS (SUBSTRING(email, LOCATE('@', email) + 1)) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    year_created INT GENERATED ALWAYS AS (YEAR(created_at)) STORED
);

-- 全文索引
CREATE TABLE articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    author VARCHAR(100),
    published_date DATE,
    FULLTEXT INDEX idx_title_content (title, content),
    FULLTEXT INDEX idx_content (content)
);

-- 空间数据类型
CREATE TABLE locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    coordinates POINT NOT NULL,
    area POLYGON,
    route LINESTRING,
    SPATIAL INDEX idx_coordinates (coordinates),
    SPATIAL INDEX idx_area (area)
);
