-- MySQL DML语句测试数据
-- 严格遵循MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/sql-data-manipulation-statements.html

-- 基础SELECT语句
SELECT * FROM users;

SELECT id, name, email FROM users WHERE age > 18;

SELECT COUNT(*) as total_users FROM users;

-- JOIN查询
SELECT u.name, u.email, o.order_date, o.total_amount
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.age >= 21;

-- 子查询
SELECT name, email 
FROM users 
WHERE id IN (
    SELECT DISTINCT user_id 
    FROM orders 
    WHERE total_amount > 100
);

-- 聚合查询
SELECT 
    status,
    COUNT(*) as order_count,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_order_value,
    MIN(total_amount) as min_order,
    MAX(total_amount) as max_order
FROM orders 
GROUP BY status 
HAVING COUNT(*) > 5
ORDER BY total_revenue DESC;

-- LIMIT和OFFSET
SELECT * FROM users ORDER BY created_at DESC LIMIT 10;

SELECT * FROM users ORDER BY id LIMIT 10 OFFSET 20;

-- UNION查询
SELECT name, email, 'customer' as type FROM users
UNION
SELECT company_name, contact_email, 'supplier' as type FROM suppliers
ORDER BY name;

-- 窗口函数
SELECT 
    name,
    email,
    order_date,
    total_amount,
    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY order_date DESC) as order_rank,
    SUM(total_amount) OVER (PARTITION BY user_id) as user_total_spent
FROM users u
JOIN orders o ON u.id = o.user_id;

-- CTE (Common Table Expression)
WITH monthly_sales AS (
    SELECT 
        DATE_FORMAT(order_date, '%Y-%m') as month,
        SUM(total_amount) as monthly_total
    FROM orders
    GROUP BY DATE_FORMAT(order_date, '%Y-%m')
),
avg_monthly AS (
    SELECT AVG(monthly_total) as avg_monthly_sales
    FROM monthly_sales
)
SELECT 
    ms.month,
    ms.monthly_total,
    am.avg_monthly_sales,
    (ms.monthly_total - am.avg_monthly_sales) as variance
FROM monthly_sales ms
CROSS JOIN avg_monthly am
ORDER BY ms.month;

-- 基础INSERT语句
INSERT INTO users (name, email, age) VALUES ('John Doe', '<EMAIL>', 25);

-- 多行INSERT
INSERT INTO users (name, email, age) VALUES 
    ('Jane Smith', '<EMAIL>', 30),
    ('Bob Johnson', '<EMAIL>', 35),
    ('Alice Brown', '<EMAIL>', 28);

-- INSERT ... SELECT
INSERT INTO users_backup (name, email, age, created_at)
SELECT name, email, age, created_at 
FROM users 
WHERE created_at >= '2024-01-01';

-- INSERT ... ON DUPLICATE KEY UPDATE
INSERT INTO users (id, name, email, age) 
VALUES (1, 'John Doe Updated', '<EMAIL>', 26)
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    email = VALUES(email),
    age = VALUES(age),
    updated_at = CURRENT_TIMESTAMP;

-- REPLACE语句
REPLACE INTO users (id, name, email, age) 
VALUES (1, 'John Doe Replaced', '<EMAIL>', 27);

-- INSERT IGNORE
INSERT IGNORE INTO users (name, email, age) VALUES 
    ('Duplicate User', '<EMAIL>', 25),
    ('New User', '<EMAIL>', 30);

-- 基础UPDATE语句
UPDATE users SET age = 26 WHERE id = 1;

-- 多列UPDATE
UPDATE users 
SET 
    email = '<EMAIL>',
    age = age + 1,
    updated_at = CURRENT_TIMESTAMP
WHERE id = 1;

-- 条件UPDATE
UPDATE users 
SET status = 'inactive' 
WHERE last_login < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- JOIN UPDATE
UPDATE users u
JOIN orders o ON u.id = o.user_id
SET u.last_order_date = o.order_date
WHERE o.order_date = (
    SELECT MAX(order_date) 
    FROM orders o2 
    WHERE o2.user_id = u.id
);

-- 基础DELETE语句
DELETE FROM users WHERE id = 1;

-- 条件DELETE
DELETE FROM users WHERE status = 'inactive' AND last_login < '2023-01-01';

-- JOIN DELETE
DELETE u 
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE o.user_id IS NULL AND u.created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- TRUNCATE语句
TRUNCATE TABLE temp_calculations;

-- 事务语句
START TRANSACTION;

INSERT INTO users (name, email, age) VALUES ('Transaction User', '<EMAIL>', 25);

UPDATE users SET age = 26 WHERE email = '<EMAIL>';

COMMIT;

-- 回滚事务
START TRANSACTION;

DELETE FROM users WHERE email = '<EMAIL>';

ROLLBACK;

-- 保存点
START TRANSACTION;

INSERT INTO users (name, email, age) VALUES ('Savepoint User', '<EMAIL>', 25);

SAVEPOINT sp1;

UPDATE users SET age = 26 WHERE email = '<EMAIL>';

ROLLBACK TO SAVEPOINT sp1;

COMMIT;

-- 锁定语句
SELECT * FROM users WHERE id = 1 FOR UPDATE;

SELECT * FROM users WHERE status = 'active' LOCK IN SHARE MODE;

-- 复杂查询示例
SELECT 
    u.name,
    u.email,
    COUNT(o.id) as total_orders,
    COALESCE(SUM(o.total_amount), 0) as total_spent,
    CASE 
        WHEN COUNT(o.id) = 0 THEN 'No Orders'
        WHEN COUNT(o.id) < 5 THEN 'Low Activity'
        WHEN COUNT(o.id) < 20 THEN 'Medium Activity'
        ELSE 'High Activity'
    END as activity_level,
    RANK() OVER (ORDER BY COALESCE(SUM(o.total_amount), 0) DESC) as spending_rank
FROM users u
LEFT JOIN orders o ON u.id = o.user_id AND o.status = 'completed'
WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)
GROUP BY u.id, u.name, u.email
HAVING total_spent > 0 OR COUNT(o.id) = 0
ORDER BY total_spent DESC, u.name
LIMIT 100;

-- 递归CTE
WITH RECURSIVE category_hierarchy AS (
    -- 基础情况：顶级分类
    SELECT id, name, parent_id, 0 as level, CAST(name AS CHAR(1000)) as path
    FROM categories 
    WHERE parent_id IS NULL
    
    UNION ALL
    
    -- 递归情况：子分类
    SELECT c.id, c.name, c.parent_id, ch.level + 1, CONCAT(ch.path, ' > ', c.name)
    FROM categories c
    JOIN category_hierarchy ch ON c.parent_id = ch.id
    WHERE ch.level < 10  -- 防止无限递归
)
SELECT id, name, level, path
FROM category_hierarchy
ORDER BY path;
