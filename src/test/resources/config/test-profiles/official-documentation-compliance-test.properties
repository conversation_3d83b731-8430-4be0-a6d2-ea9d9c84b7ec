# 官方文档合规性测试配置
# 
# 官方文档依据：
# - MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/
#   MySQL 8.4参考手册 - 源数据库规范
# - 达梦数据库官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
#   达梦数据库SQL开发指南 - 目标数据库规范
# - 金仓数据库官方文档: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
#   金仓数据库SQL参考手册 - 目标数据库规范
# - 神通数据库官方文档: 参考项目内@shentong.md文档
#   神通数据库SQL参考手册 - 目标数据库规范
# 
# 验证日期: 2024-01-15
# 
# 配置原则：严格基于官方文档进行配置，确保测试环境符合官方规范要求

# 基础配置
test.profile.name=official-documentation-compliance
test.profile.description=官方文档合规性测试配置
test.profile.version=2.0.0

# 源数据库配置 - MySQL 8.4
source.database.dialect=mysql
source.database.version=8.4
source.database.official.documentation.url=https://dev.mysql.com/doc/refman/8.4/en/
source.database.validation.enabled=true
source.database.strict.mode=true

# 目标数据库配置 - 达梦
target.dameng.dialect=dameng
target.dameng.official.documentation.url=https://eco.dameng.com/document/dm/zh-cn/sql-dev/
target.dameng.validation.enabled=true
target.dameng.cost.model.enabled=true

# 目标数据库配置 - 金仓
target.kingbase.dialect=kingbase
target.kingbase.official.documentation.url=https://help.kingbase.com.cn/v8/development/sql-plsql/sql/
target.kingbase.validation.enabled=true
target.kingbase.cost.model.enabled=true

# 目标数据库配置 - 神通
target.shentong.dialect=shentong
target.shentong.official.documentation.reference=@shentong.md
target.shentong.validation.enabled=true
target.shentong.cost.model.enabled=true

# 转换器配置
transpiler.validation.enabled=true
transpiler.optimization.enabled=false
transpiler.strict.mode=true
transpiler.timeout.seconds=30
transpiler.fail.on.error=true

# 测试数据配置
test.data.input.directory=src/test/resources/sql/input/mysql
test.data.expected.dameng.directory=src/test/resources/sql/expected/dameng
test.data.expected.kingbase.directory=src/test/resources/sql/expected/kingbase
test.data.expected.shentong.directory=src/test/resources/sql/expected/shentong

# 官方文档验证配置
official.documentation.verification.enabled=true
official.documentation.verification.strict=true
official.documentation.cache.enabled=false
official.documentation.timeout.seconds=10

# 合规性检查配置
compliance.check.enabled=true
compliance.check.mysql.syntax=true
compliance.check.target.syntax=true
compliance.check.data.types=true
compliance.check.functions=true
compliance.check.constraints=true

# 诊断配置
diagnostics.enabled=true
diagnostics.detailed.reports=true
diagnostics.include.warnings=true
diagnostics.include.performance.metrics=true

# 性能测试配置
performance.test.enabled=true
performance.test.max.duration.ms=1000
performance.test.iterations=10
performance.test.memory.limit.mb=512

# 日志配置
logging.level.root=INFO
logging.level.com.xylink.sqltranspiler=DEBUG
logging.level.compliance=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# 报告配置
report.generation.enabled=true
report.format=html,json
report.output.directory=target/test-reports/compliance
report.include.statistics=true
report.include.diagnostics=true
