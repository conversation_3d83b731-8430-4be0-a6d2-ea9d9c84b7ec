package com.xylink.sqltranspiler.maven;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugins.annotations.LifecyclePhase;
import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;
import org.apache.maven.project.MavenProject;

import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;

/**
 * Maven插件：SQL转换Mojo
 * 
 * 在Maven构建过程中执行SQL转换。
 * 
 * 官方文档依据：
 * - Apache Maven官方文档: https://maven.apache.org/guides/plugin/guide-java-plugin-development.html
 *   Maven插件开发指南
 * - Maven Plugin Tools官方文档: https://maven.apache.org/plugin-tools/maven-plugin-annotations/
 *   Maven插件注解参考
 * 
 * 验证日期: 2024-01-15
 * 
 * 使用示例：
 * <plugin>
 *   <groupId>com.xylink</groupId>
 *   <artifactId>sql-transpiler-maven-plugin</artifactId>
 *   <version>2.0.0</version>
 *   <configuration>
 *     <sourceDialect>mysql</sourceDialect>
 *     <targetDialect>dameng</targetDialect>
 *     <inputDirectory>src/main/sql</inputDirectory>
 *     <outputDirectory>target/generated-sql</outputDirectory>
 *   </configuration>
 *   <executions>
 *     <execution>
 *       <goals>
 *         <goal>transpile</goal>
 *       </goals>
 *     </execution>
 *   </executions>
 * </plugin>
 */
@Mojo(
    name = "transpile",
    defaultPhase = LifecyclePhase.GENERATE_RESOURCES,
    threadSafe = true
)
public class TranspileMojo extends AbstractMojo {
    
    /**
     * Maven项目对象
     */
    @Parameter(defaultValue = "${project}", readonly = true, required = true)
    private MavenProject project;
    
    /**
     * 源数据库方言
     */
    @Parameter(property = "sql.transpiler.sourceDialect", defaultValue = "mysql")
    private String sourceDialect;
    
    /**
     * 目标数据库方言
     */
    @Parameter(property = "sql.transpiler.targetDialect", required = true)
    private String targetDialect;
    
    /**
     * 输入SQL文件目录
     */
    @Parameter(property = "sql.transpiler.inputDirectory", defaultValue = "${project.basedir}/src/main/sql")
    private File inputDirectory;
    
    /**
     * 输出SQL文件目录
     */
    @Parameter(property = "sql.transpiler.outputDirectory", defaultValue = "${project.build.directory}/generated-sql")
    private File outputDirectory;
    
    /**
     * 包含的文件模式
     */
    @Parameter(property = "sql.transpiler.includes")
    private String[] includes = {"**/*.sql"};
    
    /**
     * 排除的文件模式
     */
    @Parameter(property = "sql.transpiler.excludes")
    private String[] excludes = {};
    
    /**
     * 是否启用严格模式
     */
    @Parameter(property = "sql.transpiler.strictMode", defaultValue = "false")
    private boolean strictMode;
    
    /**
     * 是否启用SQL验证
     */
    @Parameter(property = "sql.transpiler.enableValidation", defaultValue = "true")
    private boolean enableValidation;
    
    /**
     * 是否启用查询优化
     */
    @Parameter(property = "sql.transpiler.enableOptimization", defaultValue = "false")
    private boolean enableOptimization;
    
    /**
     * 是否跳过转换
     */
    @Parameter(property = "sql.transpiler.skip", defaultValue = "false")
    private boolean skip;
    
    /**
     * 是否在转换失败时失败构建
     */
    @Parameter(property = "sql.transpiler.failOnError", defaultValue = "true")
    private boolean failOnError;
    
    /**
     * 输出文件名后缀
     */
    @Parameter(property = "sql.transpiler.outputSuffix")
    private String outputSuffix;
    
    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {
        if (skip) {
            getLog().info("跳过SQL转换");
            return;
        }
        
        try {
            // 验证参数
            validateParameters();
            
            // 解析方言
            SqlDialect source = parseDialect(sourceDialect);
            SqlDialect target = parseDialect(targetDialect);
            
            // 创建转换器
            TranspilerBuilder builder = createTranspilerBuilder(source, target);
            
            // 查找输入文件
            List<Path> sqlFiles = findSqlFiles();
            
            if (sqlFiles.isEmpty()) {
                getLog().info("在输入目录中未找到SQL文件: " + inputDirectory);
                return;
            }
            
            getLog().info("找到 " + sqlFiles.size() + " 个SQL文件");
            
            // 确保输出目录存在
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            }
            
            // 处理文件
            int successCount = 0;
            int failureCount = 0;
            
            for (Path sqlFile : sqlFiles) {
                try {
                    if (processSqlFile(sqlFile, builder)) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    getLog().error("处理文件失败: " + sqlFile, e);
                    failureCount++;
                }
            }
            
            getLog().info("SQL转换完成: " + successCount + " 成功, " + failureCount + " 失败");
            
            if (failureCount > 0 && failOnError) {
                throw new MojoFailureException("SQL转换失败，有 " + failureCount + " 个文件转换失败");
            }
            
        } catch (MojoExecutionException | MojoFailureException e) {
            throw e;
        } catch (Exception e) {
            throw new MojoExecutionException("SQL转换执行失败", e);
        }
    }
    
    /**
     * 验证参数
     */
    private void validateParameters() throws MojoExecutionException {
        if (targetDialect == null || targetDialect.trim().isEmpty()) {
            throw new MojoExecutionException("目标方言不能为空");
        }
        
        if (inputDirectory == null || !inputDirectory.exists()) {
            throw new MojoExecutionException("输入目录不存在: " + inputDirectory);
        }
        
        if (!inputDirectory.isDirectory()) {
            throw new MojoExecutionException("输入路径不是目录: " + inputDirectory);
        }
    }
    
    /**
     * 解析数据库方言
     */
    private SqlDialect parseDialect(String dialectName) throws MojoExecutionException {
        try {
            return SqlDialect.valueOf(dialectName.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new MojoExecutionException("不支持的方言: " + dialectName + 
                ". 支持的方言: mysql, dameng, kingbase, shentong");
        }
    }
    
    /**
     * 创建转换器建造者
     */
    private TranspilerBuilder createTranspilerBuilder(SqlDialect source, SqlDialect target) {
        TranspilerBuilder builder;
        
        // 根据目标方言创建预配置的建造者
        switch (target) {
            case DAMENG:
                builder = TranspilerBuilder.mysqlToDameng();
                break;
            case KINGBASE:
                builder = TranspilerBuilder.mysqlToKingbase();
                break;
            case SHENTONG:
                builder = TranspilerBuilder.mysqlToShentong();
                break;
            default:
                builder = TranspilerBuilder.create(source, target);
                break;
        }
        
        // 配置选项
        return builder
            .withValidation(enableValidation)
            .withOptimization(enableOptimization)
            .strictMode(strictMode);
    }
    
    /**
     * 查找SQL文件
     */
    private List<Path> findSqlFiles() throws IOException {
        return Files.walk(inputDirectory.toPath())
            .filter(path -> path.toString().toLowerCase().endsWith(".sql"))
            .filter(path -> shouldIncludeFile(path))
            .toList();
    }
    
    /**
     * 判断是否应该包含文件
     */
    private boolean shouldIncludeFile(Path path) {
        String relativePath = inputDirectory.toPath().relativize(path).toString();
        
        // 检查包含模式
        boolean included = false;
        for (String include : includes) {
            if (matchesPattern(relativePath, include)) {
                included = true;
                break;
            }
        }
        
        if (!included) {
            return false;
        }
        
        // 检查排除模式
        for (String exclude : excludes) {
            if (matchesPattern(relativePath, exclude)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 简单的模式匹配
     */
    private boolean matchesPattern(String path, String pattern) {
        // 简化的通配符匹配
        String regex = pattern.replace("**", ".*").replace("*", "[^/]*");
        return path.matches(regex);
    }
    
    /**
     * 处理单个SQL文件
     */
    private boolean processSqlFile(Path sqlFile, TranspilerBuilder builder) throws IOException {
        getLog().debug("处理文件: " + sqlFile);
        
        // 读取SQL内容
        String sql = Files.readString(sqlFile);
        
        // 执行转换
        TranspilationResult result = builder.transpile(sql);
        
        if (!result.isSuccess()) {
            getLog().error("转换失败: " + sqlFile + " - " + result.getErrorMessage());
            if (result.getDiagnosticReport().isPresent()) {
                getLog().error(result.getDiagnosticReport().get().toFormattedString());
            }
            return false;
        }
        
        // 生成输出文件路径
        Path outputFile = generateOutputPath(sqlFile);
        
        // 确保输出目录存在
        Files.createDirectories(outputFile.getParent());
        
        // 写入转换结果
        Files.writeString(outputFile, result.getTranspiledSql());
        
        getLog().debug("转换完成: " + sqlFile + " -> " + outputFile);
        
        return true;
    }
    
    /**
     * 生成输出文件路径
     */
    private Path generateOutputPath(Path inputFile) {
        Path relativePath = inputDirectory.toPath().relativize(inputFile);
        String fileName = relativePath.getFileName().toString();
        
        // 添加后缀
        if (outputSuffix != null && !outputSuffix.isEmpty()) {
            String baseName = fileName.replaceAll("\\.sql$", "");
            fileName = baseName + outputSuffix + ".sql";
        }
        
        return outputDirectory.toPath().resolve(relativePath.getParent()).resolve(fileName);
    }
}
