---
type: "always_apply"
---

# MySQL到国产数据库SQL转换规范

## 🎯 核心目标

**实现MySQL到达梦、金仓、神通数据库的准确、完整SQL转换**

项目的唯一使命是提供高质量的MySQL SQL语句转换服务，确保转换后的SQL能在目标数据库中正确执行。

## 📋 绝对不可妥协的基本原则

### 1. 官方文档绝对权威原则 【最高优先级】
- **绝对要求**：任何关于数据库功能支持的判断，必须有明确的官方文档支撑
- **禁止行为**：
  - 禁止基于推测、类比、经验做出功能支持判断
  - 禁止基于部分文档信息推断全部功能支持
  - 禁止基于相似功能推断其他功能支持
  - 禁止基于个人理解或常识做出技术判断
- **强制要求**：
  - 每个转换规则必须引用具体的官方文档页面和章节
  - 每个"支持"声明必须有官方文档明确说明
  - 每个"不支持"声明必须有官方文档明确说明或缺失证明
  - 代码注释必须包含官方文档URL和具体章节引用

### 2. 严格验证机制
- **双重验证**：MySQL官方文档 + 目标数据库官方文档
- **证据链要求**：
  - MySQL功能：必须有MySQL 8.4官方文档明确定义
  - 目标数据库功能：必须有目标数据库官方文档明确说明
  - 转换规则：必须基于两个官方文档的明确对比
- **禁止推断**：不允许基于一个功能的存在推断其他功能的存在

### 3. 转换完整性原则
- **目标**：最大化MySQL语法的转换覆盖率
- **策略**：优先实现转换，但必须基于官方文档验证
- **标准**：能转换的必须转换，不能转换的必须有官方文档依据

### 4. 质量优先原则
- **准确性**：转换结果必须在目标数据库中语法正确
- **完整性**：功能实现必须完整，不允许半成品
- **可维护性**：代码必须包含官方文档引用和清晰注释

## 🔍 强制实施策略

### 1. 官方文档强制验证流程
**每一个技术判断都必须遵循以下流程：**

1. **明确功能定义**：
   - 在MySQL 8.4官方文档中找到该功能的明确定义
   - 记录具体的文档URL、章节号、页码
   - 复制相关的官方描述文本

2. **目标数据库验证**：
   - 在目标数据库官方文档中搜索相同功能
   - 如果找到：记录支持情况、语法差异、限制条件
   - 如果未找到：记录搜索过程，确认功能确实不存在

3. **证据记录**：
   - 在代码注释中包含完整的官方文档引用
   - 格式：`// 根据[数据库名]官方文档 [URL] 第[章节]节，[功能描述]`
   - 包含验证日期，确保文档时效性

### 2. "不支持"声明的绝对验证要求
当遇到任何"Unsupported"、"不支持"、"暂不支持"声明时：

**强制验证步骤：**
1. **质疑假设**：假设目标数据库可能支持该功能
2. **穷尽搜索**：在官方文档中进行全面搜索
   - 搜索功能名称的各种变体
   - 搜索相关的同义词和近义词
   - 检查不同版本的文档
3. **多源验证**：
   - 官方文档
   - 官方示例代码
   - 官方FAQ和问题解答
   - 官方发布说明
4. **决策依据**：
   - **支持**：有明确的官方文档说明 → 实现转换
   - **不支持**：官方文档明确说明不支持 → 保留"不支持"
   - **未明确**：官方文档未提及 → 标记为"官方文档未明确，需进一步验证"

### 3. 转换实现的证据要求
**每个转换规则必须包含：**
1. **MySQL官方文档引用**：证明源功能的存在和定义
2. **目标数据库官方文档引用**：证明目标功能的支持情况
3. **转换逻辑说明**：基于官方文档的转换规则
4. **测试用例**：验证转换的正确性
5. **限制说明**：基于官方文档的功能限制

### 4. 禁止的推断行为
**绝对禁止以下行为：**
- ❌ "因为支持A功能，所以应该也支持B功能"
- ❌ "这是常见功能，应该都支持"
- ❌ "根据经验，这个功能通常..."
- ❌ "类似的数据库都支持，所以..."
- ❌ "从技术角度看，实现这个功能很简单，所以..."

**必须的验证行为：**
- ✅ "根据[数据库]官方文档[URL]第[章节]节明确说明..."
- ✅ "官方示例代码显示..."
- ✅ "官方FAQ明确回答..."
- ✅ "经过穷尽搜索，官方文档未提及此功能..."

## 📚 官方文档权威来源

### MySQL 8.4 官方文档
- **主文档**：https://dev.mysql.com/doc/refman/8.4/en/
- **SQL语句**：https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html
- **数据类型**：https://dev.mysql.com/doc/refman/8.4/en/data-types.html
- **函数参考**：https://dev.mysql.com/doc/refman/8.4/en/functions.html

### 目标数据库官方文档
- **达梦数据库**：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
- **金仓数据库**：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
- **神通数据库**：参考项目内@shentong.md文档

## ⚡ 持续改进机制

### 动态验证机制
- 所有测试用例都使用基于官方文档的动态验证方法
- 每个验证方法都包含详细的官方文档链接
- 提供清晰的成功/警告/错误信息

### 测试驱动开发
- **当测试期望与官方文档不符时，修正测试用例而不是降低代码质量**
- **不妥协代码质量**：坚持正确的实现，确保功能的准确性和完整性
- **双重测试策略**：
  - 测试非MySQL语法被正确拒绝，验证MySQL强制语法校验工作正常
  - 添加标准MySQL语法的转换测试，验证正确的MySQL语法能够成功转换

### MySQL语法校验规则
MySQL强制语法校验应该只拒绝非MySQL语法，而不应该拒绝正确的MySQL语法（即使它们是MySQL特有的）。

## 🔧 技术实现规范

### 1. 数据类型系统设计原则
**基于官方文档的类型映射**：
- **MySQL 8.4官方文档**：https://dev.mysql.com/doc/refman/8.4/en/data-types.html 作为源类型定义标准
- **目标数据库官方文档**：各数据库官方文档作为目标类型映射依据
- **映射策略**：优先保持语义等价，其次考虑存储兼容，最后提供转换说明

### 2. IRDataType实现规范
**当前架构分析**：
- IRDataType使用Builder模式，不是预定义枚举常量
- 只有三个预定义常量：IRDataType.BOOLEAN, IRDataType.INTEGER, IRDataType.VARCHAR
- 类型名称通过`getTypeName()`方法获取字符串表示
- 类型映射器应使用字符串映射而非枚举映射

**正确实现模式**：
```java
// ✅ 正确：使用字符串映射
Map<String, String> dataTypeMapping = new HashMap<>();
dataTypeMapping.put("TINYINT", "SMALLINT");
dataTypeMapping.put("MEDIUMINT", "INTEGER");

// ✅ 正确：通过getTypeName()获取类型名
public String mapDataType(IRDataType irDataType) {
    String typeName = irDataType.getTypeName().toUpperCase();
    return dataTypeMapping.getOrDefault(typeName, typeName);
}

// ✅ 正确：使用字符串比较而非枚举
public boolean requiresLength(IRDataType irDataType) {
    String typeName = irDataType.getTypeName().toUpperCase();
    switch (typeName) {
        case "CHAR":
        case "VARCHAR":
            return true;
        default:
            return false;
    }
}

// ❌ 错误：使用不存在的枚举常量
mapping.put(IRDataType.TINYINT, "SMALLINT"); // IRDataType.TINYINT不存在

// ❌ 错误：在switch中使用不存在的枚举
switch (irDataType) {
    case CHAR: // 编译错误：CHAR常量不存在
        return true;
}
```

**IRDataType可用的预定义常量**：
- IRDataType.BOOLEAN - 布尔类型
- IRDataType.INTEGER - 整数类型
- IRDataType.VARCHAR - 字符串类型

**所有其他数据类型都必须通过getTypeName()获取字符串名称进行处理**

### 3. SqlFeature枚举使用规范
**已验证存在的功能特性**：
- 基础DDL：CREATE_TABLE, ALTER_TABLE, DROP_TABLE, CREATE_INDEX, DROP_INDEX
- 基础DML：SELECT, INSERT, UPDATE, DELETE
- 查询功能：SUBQUERY, JOIN, CTE, WINDOW_FUNCTIONS, COMMON_TABLE_EXPRESSIONS, RECURSIVE_CTE
- 数据类型：INTEGER_TYPES, DECIMAL_TYPES, CHARACTER_TYPES, DATE_TIME_TYPES, BOOLEAN_TYPE
- 约束：PRIMARY_KEY, FOREIGN_KEY, UNIQUE_CONSTRAINT, CHECK_CONSTRAINT, NOT_NULL_CONSTRAINT
- 索引：PARTIAL_INDEXES, FUNCTION_INDEXES, FULLTEXT_INDEXES, SPATIAL_INDEX
- 函数：AGGREGATE_FUNCTIONS, STRING_FUNCTIONS, NUMERIC_FUNCTIONS, DATE_FUNCTIONS
- 事务：TRANSACTIONS, SAVEPOINTS, TRANSACTION_ISOLATION
- 高级功能：STORED_PROCEDURES, TRIGGERS, VIEWS, USER_DEFINED_FUNCTIONS
- MySQL特有：AUTO_INCREMENT, REPLACE_INTO, INSERT_IGNORE, ON_DUPLICATE_KEY_UPDATE, ENUM_TYPE, SET_TYPE, JSON_TYPE, MYSQL_SPECIFIC_FUNCTIONS
- 分页：LIMIT_CLAUSE, OFFSET_CLAUSE
- 字符集：MULTIPLE_CHARSETS, UNICODE_SUPPORT
- 存储引擎：MULTIPLE_STORAGE_ENGINES, TABLE_PARTITIONING
- 空间数据：SPATIAL_TYPES
- 约束特性：FOREIGN_KEYS, CHECK_CONSTRAINTS, DEFERRABLE_CONSTRAINTS
- 序列：IDENTITY_COLUMNS, SERIAL_TYPE

**重要提醒**：
- ✅ 使用前必须在SqlFeature.java中验证常量确实存在
- ✅ 所有SqlFeature常量都基于官方文档定义，有明确的文档引用
- ❌ 禁止使用未在SqlFeature.java中定义的常量名称

### 4. 方言生成器实现规范
**接口方法名称**：
- 使用`generate(IRNode node)`而不是`generateSql(IRNode node)`
- 严格按照SqlDialectGenerator接口定义实现

**API兼容性处理**：
```java
// ✅ 正确：处理Optional类型
if (column.getDefaultValue().isPresent()) {
    sql.append(" DEFAULT ");
    sql.append(generateExpression(column.getDefaultValue().get()));
}

// ✅ 正确：处理数据类型属性
if (column.getDataType().getPrecision().isPresent()) {
    sql.append("(").append(column.getDataType().getPrecision().get());
    if (column.getDataType().getScale().isPresent()) {
        sql.append(",").append(column.getDataType().getScale().get());
    }
    sql.append(")");
}

// ✅ 正确：处理LIMIT子句
if (node.getLimitClause() != null) {
    sql.append("\nLIMIT ").append(node.getLimitClause().getLimit());
    if (node.getLimitClause().getOffset() > 0) {
        sql.append(" OFFSET ").append(node.getLimitClause().getOffset());
    }
}
```

### 5. 编译错误处理原则
**优先级顺序**：
1. **API兼容性**：确保使用正确的方法签名和返回类型
2. **类型安全**：避免使用不存在的枚举常量或类
3. **功能完整性**：实现核心功能，标记TODO的扩展功能
4. **文档合规性**：确保所有实现都有官方文档支撑

**渐进式实现策略**：
- 先实现基础框架，确保编译通过
- 再逐步完善功能实现
- 最后添加完整的测试覆盖

### 6. 编译错误诊断和修复规范
**常见编译错误类型及修复方法**：

**IRDataType相关错误**：
```
错误：找不到符号 IRDataType.TINYINT
修复：改为使用字符串 "TINYINT" 或 irDataType.getTypeName()

错误：不兼容的类型: IRDataType无法转换为String
修复：使用 irDataType.getTypeName() 获取字符串表示
```

**SqlFeature相关错误**：
```
错误：找不到符号 SqlFeature.UNION
修复：检查SqlFeature.java确认常量是否存在，使用正确的常量名

错误：SqlFeature.NUMERIC_TYPES cannot be resolved
修复：使用存在的常量如 SqlFeature.INTEGER_TYPES, SqlFeature.DECIMAL_TYPES
```

**方法签名错误**：
```
错误：方法 generateSql(IRNode) 不存在
修复：使用正确的方法名 generate(IRNode)

错误：返回类型不匹配
修复：检查接口定义，确保返回类型正确
```

### 7. 测试用例升级规范
**v2 API迁移检查清单**：
- [ ] 导入语句使用v2包路径
- [ ] 使用正确的IRNode.NodeType枚举
- [ ] 使用Builder模式创建IR对象
- [ ] 处理Optional返回值
- [ ] 使用正确的访问者模式接口
- [ ] 移除不存在的API调用

## 💡 实施案例

### 案例1：SET NAMES语句的错误判断和纠正

**错误的推理过程**：
```java
// 错误：基于部分信息推断
// 在达梦官方文档中看到 `set define off;` 命令
// 错误地推断达梦数据库支持所有SET语句，包括SET NAMES
```

**正确的验证过程**：
1. **查阅MySQL官方文档**：确认SET NAMES是MySQL用于设置客户端字符集的命令
2. **查阅达梦官方文档**：确认达梦数据库字符集设置通过配置文件dm.ini实现
3. **官方确认**：达梦数据库确实不支持SET NAMES语句
4. **正确实现**：保留转换为注释的逻辑

**教训**：绝对不能基于部分功能的存在推断其他功能的支持情况

### 案例2：IRDataType常量使用错误

**错误做法**：
```java
mapping.put(IRDataType.TINYINT, "SMALLINT"); // 编译错误：TINYINT常量不存在
switch (irDataType) {
    case CHAR: // 编译错误：CHAR常量不存在
        return true;
}
```

**正确做法**：
1. 查阅IRDataType类：只有BOOLEAN、INTEGER、VARCHAR三个预定义常量
2. 使用字符串映射：
```java
mapping.put("TINYINT", "SMALLINT"); // 正确：使用字符串键
String typeName = irDataType.getTypeName().toUpperCase();
switch (typeName) {
    case "CHAR": // 正确：使用字符串比较
        return true;
}
```

### 案例3：MySQL特有语法处理

**REPLACE INTO语句**：
- MySQL：完全支持
- 达梦：不支持，转换为MERGE INTO
- 金仓：支持（MySQL兼容模式）
- 神通：不支持，标记为MySQL特有语法

**处理原则**：根据各数据库官方文档实现相应的转换或拒绝逻辑