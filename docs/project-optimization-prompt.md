# MySQL到国产数据库转换项目优化Prompt

## 🎯 项目优化目标

基于Apache Calcite设计理念，将当前的MySQL到国产数据库转换项目升级为企业级SQL转换框架。请严格遵照 @`.augment/rules/rule-db.md` 中的规范，确保所有技术判断都有官方文档支撑。

## 📋 核心优化任务

### 阶段1：架构重构基础（优先级：最高）

#### 任务1.1：设计中间表示（IR）系统
**目标：** 建立数据库无关的中间表示层，类似Calcite的RelNode体系

**具体要求：**
```java
// 需要实现的核心接口
public abstract class IRNode {
    // 访问者模式支持
    public abstract <T> T accept(IRVisitor<T> visitor);
    
    // 转换支持
    public abstract IRNode transform(IRTransformer transformer);
    
    // 验证支持 - 必须基于官方文档验证
    public abstract ValidationResult validate(ValidationContext context);
}

// 具体实现类
- IRCreateTable: 表创建语句的中间表示
- IRAlterTable: 表修改语句的中间表示  
- IRSelect: 查询语句的中间表示
- IRColumn: 列定义的中间表示
- IRDataType: 数据类型的中间表示
- IRConstraint: 约束的中间表示
```

**遵循规范：**
- 每个数据类型映射必须有官方文档支撑
- 每个功能支持判断必须引用具体的官方文档章节
- 代码注释必须包含官方文档URL和验证日期

#### 任务1.2：增强方言系统
**目标：** 重新设计SqlDialect接口，支持特性检测和能力声明

**具体要求：**
```java
public abstract class SqlDialect {
    // 特性支持检测 - 必须基于官方文档
    public abstract boolean supportsFeature(SqlFeature feature);
    public abstract Set<SqlFeature> getSupportedFeatures();
    
    // 类型系统 - 每个映射必须有官方文档依据
    public abstract DataTypeMapping getTypeMapping();
    
    // 语法生成 - 基于官方文档的语法规则
    public abstract String generateCreateTable(IRCreateTable table);
    public abstract String generateAlterTable(IRAlterTable alter);
}

// 特性枚举 - 每个特性必须有官方文档定义
public enum SqlFeature {
    AUTO_INCREMENT,      // 自增列支持
    FOREIGN_KEYS,        // 外键约束支持
    CHECK_CONSTRAINTS,   // CHECK约束支持
    JSON_FUNCTIONS,      // JSON函数支持
    WINDOW_FUNCTIONS,    // 窗口函数支持
    // ... 更多特性
}
```

**强制验证要求：**
- 每个`supportsFeature()`返回值必须有官方文档明确说明
- 每个"不支持"声明必须经过穷尽搜索验证
- 所有类型映射必须基于官方文档的数据类型章节

#### 任务1.3：实现规则引擎
**目标：** 建立可配置的转换规则系统

**具体要求：**
```java
public abstract class TransformationRule {
    // 规则匹配 - 基于官方文档的语法规则
    public abstract boolean matches(IRNode node, TransformationContext context);
    
    // 规则应用 - 转换逻辑必须有官方文档依据
    public abstract IRNode apply(IRNode node, TransformationContext context);
    
    // 优先级定义
    public abstract int getPriority();
}

// 具体规则示例
public class MySQLToKingbaseDataTypeRule extends TransformationRule {
    // 必须在注释中包含官方文档引用
    // 根据金仓官方文档 https://help.kingbase.com.cn/v8/development/sql-plsql/sql/datatype.html
    // 第3.1节明确说明支持的数据类型映射
}
```

### 阶段2：API优化与用户体验

#### 任务2.1：流式API设计
**目标：** 提供类似Calcite RelBuilder的流式API

**具体要求：**
```java
public class TranspilerBuilder {
    public static TranspilerBuilder create();
    public TranspilerBuilder fromDialect(Class<? extends SqlDialect> dialectClass);
    public TranspilerBuilder toDialect(Class<? extends SqlDialect> dialectClass);
    public TranspilerBuilder withValidation(boolean enabled);
    public TranspilerBuilder withOptimization(boolean enabled);
    public TranspilationResult transpile(String sql);
}

// 使用示例
TranspilationResult result = TranspilerBuilder.create()
    .fromDialect(MySQLDialect.class)
    .toDialect(DamengDialect.class)
    .withValidation(true)
    .transpile("CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY)");
```

#### 任务2.2：完善错误处理
**目标：** 建立完善的错误处理和诊断系统

**具体要求：**
```java
public class TranspilationContext {
    // 错误收集 - 必须包含官方文档引用
    public void addError(String code, String message, SourceLocation location, String docReference);
    public void addWarning(String code, String message, SourceLocation location, String docReference);
    
    // 诊断报告生成
    public DiagnosticReport generateReport();
}
```

### 阶段3：高级功能实现

#### 任务3.1：查询优化器
**目标：** 添加SQL优化功能

#### 任务3.2：成本模型
**目标：** 建立成本模型支持基于成本的优化

### 阶段4：生态建设

#### 任务4.1：插件系统
**目标：** 支持第三方扩展

#### 任务4.2：工具链集成
**目标：** 与开发工具链集成

## 🔍 强制验证要求

### 官方文档验证流程
每个技术判断都必须遵循以下流程：

1. **明确功能定义**
   - 在MySQL 8.4官方文档中找到该功能的明确定义
   - 记录具体的文档URL、章节号、页码
   - 复制相关的官方描述文本

2. **目标数据库验证**
   - 在目标数据库官方文档中搜索相同功能
   - 如果找到：记录支持情况、语法差异、限制条件
   - 如果未找到：记录搜索过程，确认功能确实不存在

3. **证据记录**
   - 在代码注释中包含完整的官方文档引用
   - 格式：`// 根据[数据库名]官方文档 [URL] 第[章节]节，[功能描述]`
   - 包含验证日期，确保文档时效性

### 代码注释要求
```java
/**
 * MySQL AUTO_INCREMENT到达梦IDENTITY的转换规则
 * 
 * 官方文档依据：
 * - MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/example-auto-increment.html
 *   第3.6.9节明确定义AUTO_INCREMENT语法和行为
 * - 达梦官方文档: https://eco.dameng.com/document/dm/zh-cn/sql-dev/dmpl-sql-datatype.html
 *   第2.1.1节说明IDENTITY列的语法：IDENTITY(start_value, increment_value)
 * 
 * 验证日期: 2024-01-15
 * 转换逻辑: AUTO_INCREMENT -> IDENTITY(1,1)
 */
public class MySQLAutoIncrementToDamengRule extends TransformationRule {
    // 实现代码
}
```

### 禁止的推断行为
**绝对禁止：**
- ❌ "因为支持A功能，所以应该也支持B功能"
- ❌ "这是常见功能，应该都支持"
- ❌ "根据经验，这个功能通常..."
- ❌ "类似的数据库都支持，所以..."

**必须的验证行为：**
- ✅ "根据[数据库]官方文档[URL]第[章节]节明确说明..."
- ✅ "官方示例代码显示..."
- ✅ "经过穷尽搜索，官方文档未提及此功能..."

## 📚 官方文档权威来源

### MySQL 8.4 官方文档
- **主文档**：https://dev.mysql.com/doc/refman/8.4/en/
- **SQL语句**：https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html
- **数据类型**：https://dev.mysql.com/doc/refman/8.4/en/data-types.html
- **函数参考**：https://dev.mysql.com/doc/refman/8.4/en/functions.html

### 目标数据库官方文档
- **达梦数据库**：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
- **金仓数据库**：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html
- **神通数据库**：参考项目内@shentong.md文档

## 🎯 实施指导原则

### 1. 质量优先原则
- 转换结果必须在目标数据库中语法正确
- 功能实现必须完整，不允许半成品
- 代码必须包含官方文档引用和清晰注释

### 2. 官方文档绝对权威原则
- 任何关于数据库功能支持的判断，必须有明确的官方文档支撑
- 禁止基于推测、类比、经验做出功能支持判断
- 每个转换规则必须引用具体的官方文档页面和章节

### 3. 测试驱动开发原则
- 当测试期望与官方文档不符时，修正测试用例而不是降低代码质量
- 不妥协代码质量：坚持正确的实现，确保功能的准确性和完整性
- 双重测试策略：测试非MySQL语法被正确拒绝，测试正确的MySQL语法能够成功转换

### 4. 渐进式实施原则
- 保持现有功能的稳定性
- 通过桥接层实现平滑迁移
- 支持新旧架构并行运行

## 📋 验收标准

### 代码质量标准
- [ ] 所有功能支持判断都有官方文档引用
- [ ] 所有类型映射都有官方文档依据
- [ ] 代码注释包含完整的官方文档URL和章节引用
- [ ] 单元测试覆盖率达到90%以上
- [ ] 所有"不支持"声明都经过穷尽搜索验证

### 功能完整性标准
- [ ] 现有所有功能在新架构下正常工作
- [ ] 转换准确率达到98%以上
- [ ] 支持MySQL 8.4的主要语法特性
- [ ] 错误处理覆盖所有异常场景

### 性能标准
- [ ] 转换性能不低于现有实现
- [ ] 内存使用优化，避免内存泄漏
- [ ] 支持并发转换处理

请严格按照以上要求进行项目优化，确保每一步都有充分的官方文档支撑，不允许任何基于推测的技术判断。

## 🔧 具体实施指令

### 开发工作流程

#### 步骤1：需求分析与文档验证
```bash
# 对于每个新功能或修改，必须先执行文档验证
1. 确定要实现的MySQL功能
2. 在MySQL官方文档中找到该功能的定义
3. 在目标数据库官方文档中验证支持情况
4. 记录验证结果和文档引用
5. 如果官方文档不明确，标记为"需进一步验证"
```

#### 步骤2：代码实现
```java
// 每个类都必须包含官方文档引用头部注释
/**
 * [类名] - [功能描述]
 *
 * 官方文档依据：
 * - MySQL 8.4: [具体URL] 第[章节]节
 * - [目标数据库]: [具体URL] 第[章节]节
 *
 * 验证日期: [YYYY-MM-DD]
 * 实现状态: [完全支持/部分支持/不支持]
 * 限制说明: [如有限制，详细说明]
 */
public class ExampleClass {
    // 实现代码
}
```

#### 步骤3：测试验证
```java
// 每个测试类都必须验证官方文档的行为
@Test
public void testMySQLFeatureConversion() {
    // 根据MySQL官方文档 [URL] 第[章节]节的示例
    String mysqlSql = "CREATE TABLE test (id INT AUTO_INCREMENT PRIMARY KEY)";

    // 根据达梦官方文档 [URL] 第[章节]节的语法
    String expectedDamengSql = "CREATE TABLE test (id INT IDENTITY(1,1) PRIMARY KEY)";

    TranspilationResult result = transpiler.transpile(mysqlSql, DamengDialect.class);
    assertEquals(expectedDamengSql, result.getSql());
}
```

### 代码审查检查清单

#### 架构层面检查
- [ ] 是否遵循新的IR系统设计
- [ ] 是否正确使用访问者模式
- [ ] 是否保持不可变性原则
- [ ] 是否有适当的错误处理

#### 文档验证检查
- [ ] 每个功能支持判断都有官方文档引用
- [ ] 官方文档URL是否有效且准确
- [ ] 章节引用是否具体明确
- [ ] 验证日期是否最新

#### 代码质量检查
- [ ] 是否有单元测试覆盖
- [ ] 是否有集成测试验证
- [ ] 是否有性能测试
- [ ] 是否有错误场景测试

### 常见问题处理指南

#### 问题1：官方文档不明确怎么办？
**处理方式：**
```java
/**
 * 注意：官方文档未明确说明此功能的支持情况
 *
 * 搜索记录：
 * - 在[数据库]官方文档中搜索关键词：[关键词列表]
 * - 检查的文档章节：[章节列表]
 * - 搜索日期：[YYYY-MM-DD]
 *
 * 结论：官方文档未明确，标记为"需进一步验证"
 * 建议：联系官方技术支持或社区确认
 */
public boolean supportsFeature(SqlFeature feature) {
    // 保守处理，返回false
    return false;
}
```

#### 问题2：不同版本文档有冲突怎么办？
**处理方式：**
```java
/**
 * 版本兼容性说明：
 * - MySQL 8.0: 支持该功能
 * - MySQL 8.4: 增强了该功能，新增[具体特性]
 * - 达梦 v8.0: 部分支持，限制：[具体限制]
 *
 * 实现策略：按最小公共特性集实现
 */
```

#### 问题3：测试用例与官方文档不符怎么办？
**处理原则：**
1. 优先相信官方文档
2. 修正测试用例而不是代码实现
3. 如果确认官方文档有误，记录并报告

### 性能优化指导

#### 缓存策略
```java
// 解析结果缓存 - 基于官方文档的语法稳定性
public class DocumentationBasedCache {
    // 只缓存官方文档明确定义的稳定语法
    private final Cache<String, ParseResult> stableSyntaxCache;

    public ParseResult parse(String sql) {
        if (isStableSyntax(sql)) {
            return stableSyntaxCache.get(sql, this::doParse);
        }
        return doParse(sql);
    }
}
```

#### 并发处理
```java
// 线程安全的转换器 - 基于不可变IR设计
public class ConcurrentTranspiler {
    // IR节点的不可变性保证线程安全
    public CompletableFuture<TranspilationResult> transpileAsync(String sql) {
        return CompletableFuture.supplyAsync(() -> {
            IRNode ir = parseToIR(sql);
            IRNode transformed = applyRules(ir);
            return generateSQL(transformed);
        });
    }
}
```

### 监控和诊断

#### 转换质量监控
```java
// 基于官方文档的质量评估
public class TranspilationQualityMonitor {
    public QualityReport assessQuality(TranspilationResult result) {
        return QualityReport.builder()
            .documentationCompliance(checkDocumentationCompliance(result))
            .syntaxCorrectness(validateSyntax(result))
            .featureSupport(assessFeatureSupport(result))
            .build();
    }
}
```

#### 错误诊断
```java
// 基于官方文档的错误诊断
public class DocumentationBasedDiagnostics {
    public DiagnosticReport diagnose(TranspilationError error) {
        return DiagnosticReport.builder()
            .errorCode(error.getCode())
            .errorMessage(error.getMessage())
            .documentationReference(findRelevantDocumentation(error))
            .suggestedFix(generateSuggestedFix(error))
            .build();
    }
}
```

## 📈 进度跟踪

### 每日检查清单
- [ ] 今日新增代码是否都有官方文档引用
- [ ] 今日修改的功能是否重新验证了官方文档
- [ ] 今日新增测试是否基于官方文档示例
- [ ] 今日是否发现任何与官方文档不符的情况

### 每周检查清单
- [ ] 本周实现的功能是否都通过了官方文档验证
- [ ] 本周是否有任何"不支持"声明需要重新验证
- [ ] 本周的代码审查是否都通过了文档验证检查
- [ ] 本周是否更新了相关的技术文档

### 里程碑检查清单
- [ ] 所有核心功能都有完整的官方文档支撑
- [ ] 所有转换规则都经过了严格的文档验证
- [ ] 所有测试用例都基于官方文档的行为定义
- [ ] 所有错误处理都提供了官方文档引用

## 🎯 最终交付要求

### 代码交付标准
1. **100%官方文档覆盖**：每个功能判断都有官方文档支撑
2. **完整的引用链**：从代码到官方文档的完整追溯链
3. **验证记录完整**：所有验证过程都有详细记录
4. **测试覆盖全面**：基于官方文档的全面测试覆盖

### 文档交付标准
1. **官方文档映射表**：MySQL功能到各目标数据库的完整映射
2. **验证报告**：详细的官方文档验证报告
3. **限制说明**：所有已知限制和不支持功能的详细说明
4. **最佳实践指南**：基于官方文档的使用最佳实践

严格遵循以上指令进行开发，确保项目的每一个技术决策都有坚实的官方文档基础。
