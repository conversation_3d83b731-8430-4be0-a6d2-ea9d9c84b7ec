# 快速开始指南

本指南将帮助您快速上手SQL Transpiler v2.0，实现MySQL到国产数据库的SQL转换。

## 📋 前置要求

- Java 17 或更高版本
- Maven 3.6+ 或 Gradle 7.0+
- 基本的SQL和Java知识

## 🚀 安装

### Maven项目

在您的`pom.xml`中添加依赖：

```xml
<dependency>
    <groupId>com.xylink</groupId>
    <artifactId>sql-transpiler-core</artifactId>
    <version>2.0.0</version>
</dependency>
```

### Gradle项目

在您的`build.gradle`中添加依赖：

```gradle
implementation 'com.xylink:sql-transpiler-core:2.0.0'
```

## 💡 第一个转换示例

### 1. 基本转换

```java
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

public class QuickStartExample {
    public static void main(String[] args) {
        // MySQL到达梦数据库转换
        String mysqlSql = "CREATE TABLE users (" +
                         "id INT AUTO_INCREMENT PRIMARY KEY, " +
                         "name VARCHAR(100) NOT NULL, " +
                         "email VARCHAR(255) UNIQUE, " +
                         "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                         ")";
        
        TranspilationResult result = TranspilerBuilder.mysqlToDameng()
            .transpile(mysqlSql);
        
        if (result.isSuccess()) {
            System.out.println("✅ 转换成功!");
            System.out.println("达梦SQL: " + result.getTranspiledSql());
        } else {
            System.err.println("❌ 转换失败: " + result.getErrorMessage());
        }
    }
}
```

### 2. 不同目标数据库

```java
// MySQL到金仓数据库
TranspilationResult kingbaseResult = TranspilerBuilder.mysqlToKingbase()
    .transpile(mysqlSql);

// MySQL到神通数据库
TranspilationResult shentongResult = TranspilerBuilder.mysqlToShentong()
    .transpile(mysqlSql);
```

### 3. 批量转换

```java
import java.util.Arrays;
import java.util.List;

List<String> sqlStatements = Arrays.asList(
    "CREATE TABLE products (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))",
    "INSERT INTO products (name) VALUES ('Product 1'), ('Product 2')",
    "SELECT * FROM products WHERE name LIKE '%Product%'",
    "UPDATE products SET name = 'Updated Product' WHERE id = 1",
    "DELETE FROM products WHERE id = 2"
);

List<TranspilationResult> results = TranspilerBuilder.mysqlToDameng()
    .transpileAll(sqlStatements);

for (int i = 0; i < results.size(); i++) {
    TranspilationResult result = results.get(i);
    System.out.println("SQL " + (i + 1) + ": " + 
        (result.isSuccess() ? "✅ 成功" : "❌ 失败"));
    
    if (result.isSuccess()) {
        System.out.println("  转换结果: " + result.getTranspiledSql());
    } else {
        System.out.println("  错误信息: " + result.getErrorMessage());
    }
}
```

## ⚙️ 配置选项

### 启用验证和优化

```java
TranspilationResult result = TranspilerBuilder.mysqlToDameng()
    .withValidation(true)        // 启用SQL验证
    .withOptimization(true)      // 启用查询优化
    .strictMode(false)           // 非严格模式
    .transpile(sql);
```

### 自定义超时设置

```java
import java.time.Duration;

TranspilationResult result = TranspilerBuilder.mysqlToDameng()
    .timeout(Duration.ofSeconds(30))  // 设置30秒超时
    .transpile(sql);
```

### 高级配置

```java
import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;

SqlTranspiler transpiler = TranspilerBuilder.create(SqlDialect.MYSQL, SqlDialect.DAMENG)
    .withValidation(true)
    .withOptimization(true)
    .strictMode(false)
    .timeout(Duration.ofMinutes(5))
    .build();

// 可重复使用的转换器
TranspilationResult result1 = transpiler.transpile(sql1);
TranspilationResult result2 = transpiler.transpile(sql2);
```

## 📊 处理转换结果

### 检查转换状态

```java
TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(sql);

// 基本状态检查
if (result.isSuccess()) {
    System.out.println("转换成功");
    System.out.println("原始SQL: " + result.getOriginalSql());
    System.out.println("转换后SQL: " + result.getTranspiledSql());
    System.out.println("转换耗时: " + result.getDurationMillis() + "ms");
} else {
    System.err.println("转换失败: " + result.getErrorMessage());
}
```

### 获取详细统计信息

```java
if (result.getStatistics().isPresent()) {
    TransformationStatistics stats = result.getStatistics().get();
    System.out.println("应用的规则数量: " + stats.getAppliedRulesCount());
    System.out.println("转换的节点数量: " + stats.getTransformedNodesCount());
    System.out.println("优化改进数量: " + stats.getOptimizationImprovements());
}
```

### 查看诊断信息

```java
if (result.getDiagnosticReport().isPresent()) {
    DiagnosticReport report = result.getDiagnosticReport().get();
    
    // 打印格式化的诊断报告
    System.out.println(report.toFormattedString());
    
    // 获取特定类型的问题
    if (report.hasWarnings()) {
        System.out.println("警告信息:");
        report.getWarnings().forEach(warning -> 
            System.out.println("  - " + warning.getMessage()));
    }
    
    if (report.hasErrors()) {
        System.err.println("错误信息:");
        report.getErrors().forEach(error -> 
            System.err.println("  - " + error.getMessage()));
    }
}
```

## 🔍 常见转换示例

### 数据类型转换

```java
// MySQL LONGTEXT -> 达梦 CLOB
String mysql = "CREATE TABLE articles (content LONGTEXT)";
TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysql);
// 结果: CREATE TABLE articles (content CLOB)

// MySQL AUTO_INCREMENT -> 达梦 IDENTITY
String mysql2 = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY)";
TranspilationResult result2 = TranspilerBuilder.mysqlToDameng().transpile(mysql2);
// 结果: CREATE TABLE users (id INT IDENTITY(1,1) PRIMARY KEY)
```

### 函数转换

```java
// MySQL NOW() -> 达梦 SYSDATE
String mysql = "UPDATE users SET last_login = NOW() WHERE id = 1";
TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(mysql);
// 结果: UPDATE users SET last_login = SYSDATE WHERE id = 1

// MySQL LIMIT -> 达梦 ROWNUM
String mysql2 = "SELECT * FROM users ORDER BY id LIMIT 10";
TranspilationResult result2 = TranspilerBuilder.mysqlToDameng().transpile(mysql2);
// 结果: SELECT * FROM (SELECT * FROM users ORDER BY id) WHERE ROWNUM <= 10
```

### 复杂查询转换

```java
String complexQuery = """
    SELECT u.name, COUNT(o.id) as order_count
    FROM users u
    LEFT JOIN orders o ON u.id = o.user_id
    WHERE u.created_at >= '2024-01-01'
    GROUP BY u.id, u.name
    HAVING COUNT(o.id) > 5
    ORDER BY order_count DESC
    LIMIT 20
    """;

TranspilationResult result = TranspilerBuilder.mysqlToDameng()
    .withOptimization(true)  // 启用查询优化
    .transpile(complexQuery);

if (result.isSuccess()) {
    System.out.println("优化后的达梦SQL:");
    System.out.println(result.getTranspiledSql());
}
```

## ❗ 错误处理最佳实践

### 1. 总是检查转换结果

```java
TranspilationResult result = TranspilerBuilder.mysqlToDameng().transpile(sql);

if (!result.isSuccess()) {
    // 记录错误日志
    logger.error("SQL转换失败: {}", result.getErrorMessage());
    
    // 获取详细错误信息
    if (result.getDiagnosticReport().isPresent()) {
        DiagnosticReport report = result.getDiagnosticReport().get();
        logger.error("详细错误信息: {}", report.toFormattedString());
    }
    
    // 根据业务需求决定如何处理
    throw new RuntimeException("SQL转换失败: " + result.getErrorMessage());
}
```

### 2. 处理批量转换的部分失败

```java
List<TranspilationResult> results = TranspilerBuilder.mysqlToDameng()
    .transpileAll(sqlStatements);

List<String> successfulSqls = new ArrayList<>();
List<String> failedSqls = new ArrayList<>();

for (int i = 0; i < results.size(); i++) {
    TranspilationResult result = results.get(i);
    if (result.isSuccess()) {
        successfulSqls.add(result.getTranspiledSql());
    } else {
        failedSqls.add(sqlStatements.get(i));
        logger.warn("SQL转换失败: {} - {}", sqlStatements.get(i), result.getErrorMessage());
    }
}

System.out.println("成功转换: " + successfulSqls.size() + " 条SQL");
System.out.println("转换失败: " + failedSqls.size() + " 条SQL");
```

## 🎯 下一步

现在您已经掌握了基本用法，可以继续学习：

1. [配置指南](configuration.md) - 了解更多配置选项
2. [最佳实践](best-practices.md) - 学习生产环境使用技巧
3. [API参考文档](../api-reference/README.md) - 查看完整API文档
4. [数据库特定指南](../databases/) - 了解各数据库的特殊转换规则

## 🆘 获取帮助

如果遇到问题，可以：

- 查看[常见问题解答](../faq.md)
- 提交[GitHub Issue](https://github.com/xylink/sql-transpiler/issues)
- 参与[GitHub Discussions](https://github.com/xylink/sql-transpiler/discussions)
