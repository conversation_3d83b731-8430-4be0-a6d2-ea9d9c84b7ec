# 最佳实践指南

本指南总结了在生产环境中使用SQL Transpiler v2.0的最佳实践，帮助您获得最佳的性能和可靠性。

## 🏗️ 架构设计最佳实践

### 1. 转换器实例管理

**✅ 推荐做法：重用转换器实例**

```java
// 好的做法：创建一次，重复使用
public class SqlTranspilerService {
    private final SqlTranspiler transpiler;
    
    public SqlTranspilerService() {
        this.transpiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(true)
            .timeout(Duration.ofSeconds(30))
            .build();
    }
    
    public TranspilationResult transpile(String sql) {
        return transpiler.transpile(sql);
    }
}
```

**❌ 避免的做法：每次都创建新实例**

```java
// 不好的做法：性能低下
public TranspilationResult transpile(String sql) {
    return TranspilerBuilder.mysqlToDameng()  // 每次都创建新实例
        .withValidation(true)
        .transpile(sql);
}
```

### 2. 线程安全使用

**✅ 转换器是线程安全的**

```java
@Service
public class SqlTranspilerService {
    private final SqlTranspiler transpiler = TranspilerBuilder.mysqlToDameng().build();
    
    // 可以在多线程环境中安全使用
    public CompletableFuture<TranspilationResult> transpileAsync(String sql) {
        return CompletableFuture.supplyAsync(() -> transpiler.transpile(sql));
    }
}
```

### 3. 资源管理

**✅ 合理配置超时和并发**

```java
SqlTranspiler transpiler = TranspilerBuilder.mysqlToDameng()
    .timeout(Duration.ofSeconds(30))        // 设置合理的超时时间
    .withOptimization(true)                 // 根据需要启用优化
    .build();
```

## 🔧 配置最佳实践

### 1. 验证级别选择

```java
// 开发环境：启用严格验证
TranspilerBuilder.mysqlToDameng()
    .withValidation(true)
    .strictMode(true)
    .build();

// 生产环境：平衡性能和安全性
TranspilerBuilder.mysqlToDameng()
    .withValidation(true)
    .strictMode(false)  // 允许一些兼容性转换
    .build();

// 性能敏感场景：最小验证
TranspilerBuilder.mysqlToDameng()
    .withValidation(false)  // 仅在确保输入SQL正确时使用
    .build();
```

### 2. 优化策略选择

```java
// 复杂查询：启用优化
TranspilerBuilder.mysqlToDameng()
    .withOptimization(true)  // 对复杂查询有显著改善
    .build();

// 简单查询：禁用优化
TranspilerBuilder.mysqlToDameng()
    .withOptimization(false)  // 避免不必要的开销
    .build();

// 批量处理：根据查询复杂度动态选择
public TranspilationResult smartTranspile(String sql) {
    boolean isComplex = sql.toLowerCase().contains("join") || 
                       sql.toLowerCase().contains("subquery");
    
    return TranspilerBuilder.mysqlToDameng()
        .withOptimization(isComplex)
        .transpile(sql);
}
```

## 📊 性能优化最佳实践

### 1. 批量处理

**✅ 使用批量API**

```java
// 好的做法：批量处理
List<String> sqlStatements = loadSqlStatements();
List<TranspilationResult> results = transpiler.transpileAll(sqlStatements);

// 处理结果
List<String> successfulSqls = results.stream()
    .filter(TranspilationResult::isSuccess)
    .map(TranspilationResult::getTranspiledSql)
    .collect(Collectors.toList());
```

**❌ 避免逐个处理**

```java
// 不好的做法：逐个处理，性能低下
List<TranspilationResult> results = new ArrayList<>();
for (String sql : sqlStatements) {
    results.add(transpiler.transpile(sql));  // 每次都有开销
}
```

### 2. 结果缓存

```java
@Service
public class CachedSqlTranspilerService {
    private final SqlTranspiler transpiler;
    private final Cache<String, TranspilationResult> cache;
    
    public CachedSqlTranspilerService() {
        this.transpiler = TranspilerBuilder.mysqlToDameng().build();
        this.cache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofHours(1))
            .build();
    }
    
    public TranspilationResult transpile(String sql) {
        return cache.get(sql, transpiler::transpile);
    }
}
```

### 3. 异步处理

```java
@Service
public class AsyncSqlTranspilerService {
    private final SqlTranspiler transpiler;
    private final ExecutorService executor;
    
    public AsyncSqlTranspilerService() {
        this.transpiler = TranspilerBuilder.mysqlToDameng().build();
        this.executor = Executors.newFixedThreadPool(4);
    }
    
    public CompletableFuture<List<TranspilationResult>> transpileAllAsync(List<String> sqls) {
        List<CompletableFuture<TranspilationResult>> futures = sqls.stream()
            .map(sql -> CompletableFuture.supplyAsync(() -> transpiler.transpile(sql), executor))
            .collect(Collectors.toList());
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList()));
    }
}
```

## 🛡️ 错误处理最佳实践

### 1. 分层错误处理

```java
@Service
public class RobustSqlTranspilerService {
    private final SqlTranspiler transpiler;
    private final Logger logger = LoggerFactory.getLogger(getClass());
    
    public TranspilationResult transpileWithFallback(String sql) {
        try {
            // 首先尝试优化转换
            TranspilationResult result = transpiler.transpile(sql);
            
            if (result.isSuccess()) {
                return result;
            }
            
            // 如果失败，尝试非优化转换
            logger.warn("优化转换失败，尝试基础转换: {}", result.getErrorMessage());
            return TranspilerBuilder.mysqlToDameng()
                .withOptimization(false)
                .transpile(sql);
                
        } catch (Exception e) {
            logger.error("SQL转换异常", e);
            return TranspilationResult.failure(sql, "转换异常: " + e.getMessage());
        }
    }
}
```

### 2. 详细的错误日志

```java
public void logTranspilationResult(TranspilationResult result) {
    if (result.isSuccess()) {
        logger.info("SQL转换成功，耗时: {}ms", result.getDurationMillis());
        
        // 记录统计信息
        result.getStatistics().ifPresent(stats -> 
            logger.debug("转换统计: 规则数={}, 节点数={}", 
                stats.getAppliedRulesCount(), stats.getTransformedNodesCount()));
    } else {
        logger.error("SQL转换失败: {}", result.getErrorMessage());
        
        // 记录详细诊断信息
        result.getDiagnosticReport().ifPresent(report -> {
            logger.error("诊断报告: {}", report.getSummary());
            
            // 记录具体错误
            report.getErrors().forEach(error -> 
                logger.error("错误详情: [{}] {}", error.getCode(), error.getMessage()));
            
            // 记录警告
            report.getWarnings().forEach(warning -> 
                logger.warn("警告: [{}] {}", warning.getCode(), warning.getMessage()));
        });
    }
}
```

### 3. 优雅降级

```java
public class GracefulDegradationService {
    private final SqlTranspiler primaryTranspiler;
    private final SqlTranspiler fallbackTranspiler;
    
    public GracefulDegradationService() {
        // 主要转换器：功能完整但可能失败
        this.primaryTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(true)
            .withOptimization(true)
            .strictMode(true)
            .build();
        
        // 备用转换器：功能简化但更稳定
        this.fallbackTranspiler = TranspilerBuilder.mysqlToDameng()
            .withValidation(false)
            .withOptimization(false)
            .strictMode(false)
            .build();
    }
    
    public TranspilationResult transpileWithDegradation(String sql) {
        // 尝试主要转换器
        TranspilationResult result = primaryTranspiler.transpile(sql);
        
        if (result.isSuccess()) {
            return result;
        }
        
        // 降级到备用转换器
        logger.warn("主要转换器失败，使用备用转换器: {}", result.getErrorMessage());
        return fallbackTranspiler.transpile(sql);
    }
}
```

## 🔍 监控和诊断最佳实践

### 1. 性能监控

```java
@Component
public class SqlTranspilerMetrics {
    private final MeterRegistry meterRegistry;
    private final SqlTranspiler transpiler;
    
    public SqlTranspilerMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.transpiler = TranspilerBuilder.mysqlToDameng().build();
    }
    
    public TranspilationResult transpileWithMetrics(String sql) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            TranspilationResult result = transpiler.transpile(sql);
            
            // 记录成功/失败指标
            meterRegistry.counter("sql.transpiler.requests", 
                "status", result.isSuccess() ? "success" : "failure").increment();
            
            // 记录转换时间
            sample.stop(Timer.builder("sql.transpiler.duration")
                .description("SQL转换耗时")
                .register(meterRegistry));
            
            return result;
            
        } catch (Exception e) {
            meterRegistry.counter("sql.transpiler.requests", "status", "error").increment();
            sample.stop(meterRegistry.timer("sql.transpiler.duration"));
            throw e;
        }
    }
}
```

### 2. 健康检查

```java
@Component
public class SqlTranspilerHealthIndicator implements HealthIndicator {
    private final SqlTranspiler transpiler;
    
    public SqlTranspilerHealthIndicator() {
        this.transpiler = TranspilerBuilder.mysqlToDameng().build();
    }
    
    @Override
    public Health health() {
        try {
            // 使用简单的SQL测试转换器
            TranspilationResult result = transpiler.transpile("SELECT 1");
            
            if (result.isSuccess()) {
                return Health.up()
                    .withDetail("transpiler", "operational")
                    .withDetail("duration", result.getDurationMillis() + "ms")
                    .build();
            } else {
                return Health.down()
                    .withDetail("transpiler", "failed")
                    .withDetail("error", result.getErrorMessage())
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("transpiler", "exception")
                .withException(e)
                .build();
        }
    }
}
```

## 🧪 测试最佳实践

### 1. 单元测试

```java
@ExtendWith(MockitoExtension.class)
class SqlTranspilerServiceTest {
    
    private SqlTranspilerService service;
    
    @BeforeEach
    void setUp() {
        service = new SqlTranspilerService();
    }
    
    @Test
    void shouldTranspileSimpleSelect() {
        String sql = "SELECT * FROM users WHERE id = 1";
        
        TranspilationResult result = service.transpile(sql);
        
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTranspiledSql()).isNotEmpty();
        assertThat(result.getDurationMillis()).isGreaterThan(0);
    }
    
    @Test
    void shouldHandleInvalidSql() {
        String invalidSql = "INVALID SQL STATEMENT";
        
        TranspilationResult result = service.transpile(invalidSql);
        
        assertThat(result.isFailure()).isTrue();
        assertThat(result.getErrorMessage()).isNotEmpty();
    }
    
    @ParameterizedTest
    @ValueSource(strings = {
        "CREATE TABLE test (id INT)",
        "INSERT INTO test VALUES (1)",
        "UPDATE test SET id = 2",
        "DELETE FROM test WHERE id = 1"
    })
    void shouldTranspileVariousSqlTypes(String sql) {
        TranspilationResult result = service.transpile(sql);
        assertThat(result.isSuccess()).isTrue();
    }
}
```

### 2. 集成测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "sql.transpiler.validation.enabled=true",
    "sql.transpiler.optimization.enabled=false"
})
class SqlTranspilerIntegrationTest {
    
    @Autowired
    private SqlTranspilerService transpilerService;
    
    @Test
    void shouldTranspileComplexQuery() {
        String complexSql = """
            SELECT u.name, COUNT(o.id) as order_count
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            WHERE u.created_at >= '2024-01-01'
            GROUP BY u.id, u.name
            HAVING COUNT(o.id) > 5
            ORDER BY order_count DESC
            LIMIT 20
            """;
        
        TranspilationResult result = transpilerService.transpile(complexSql);
        
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTranspiledSql()).contains("ROWNUM");  // 达梦特有语法
    }
}
```

## 📈 生产环境部署最佳实践

### 1. 配置管理

```yaml
# application.yml
sql:
  transpiler:
    validation:
      enabled: true
      level: STANDARD
    optimization:
      enabled: true
      level: MODERATE
    timeout: 30s
    cache:
      enabled: true
      max-size: 1000
      expire-after-write: 1h
```

### 2. 资源限制

```java
@Configuration
public class SqlTranspilerConfig {
    
    @Bean
    @ConfigurationProperties("sql.transpiler")
    public SqlTranspilerProperties properties() {
        return new SqlTranspilerProperties();
    }
    
    @Bean
    public SqlTranspiler sqlTranspiler(SqlTranspilerProperties properties) {
        return TranspilerBuilder.mysqlToDameng()
            .withValidation(properties.getValidation().isEnabled())
            .withOptimization(properties.getOptimization().isEnabled())
            .timeout(properties.getTimeout())
            .build();
    }
}
```

### 3. 监控告警

```java
@EventListener
public void handleTranspilationFailure(TranspilationFailureEvent event) {
    // 发送告警
    if (event.getFailureRate() > 0.1) {  // 失败率超过10%
        alertService.sendAlert("SQL转换失败率过高: " + event.getFailureRate());
    }
}
```

## 🔗 相关文档

- [配置指南](configuration.md)
- [API参考文档](../api-reference/README.md)
- [架构设计](../developer-guide/architecture.md)
- [性能调优指南](performance-tuning.md)
