# SQL Transpiler v2.0 迁移指南

## 🎯 概述

SQL Transpiler v2.0 是一个完全重写的版本，基于Apache Calcite的设计理念，提供了更强大的SQL转换能力和更好的扩展性。本指南将帮助您从v1.x版本迁移到v2.0版本。

## 🔄 主要变化

### 架构变化
- **v1.x**: 基于ANTLR4的直接AST转换
- **v2.0**: 基于IR（中间表示）的现代化架构，类似Apache Calcite

### API变化
- **v1.x**: 简单的转换器API
- **v2.0**: 流式Builder API，支持高级配置

### 功能增强
- **查询优化**: 新增Volcano优化器
- **诊断系统**: 详细的错误报告和统计信息
- **插件系统**: 可扩展的方言和规则系统

## 📋 迁移步骤

### 1. 依赖更新

**v1.x Maven依赖**:
```xml
<dependency>
    <groupId>com.xylink</groupId>
    <artifactId>sql-transpiler</artifactId>
    <version>1.0.0</version>
</dependency>
```

**v2.0 Maven依赖**:
```xml
<dependency>
    <groupId>com.xylink</groupId>
    <artifactId>sql-transpiler-core</artifactId>
    <version>2.0.0</version>
</dependency>
```

### 2. API迁移

#### 基本转换

**v1.x代码**:
```java
// v1.x API (已移除)
Transpiler transpiler = new Transpiler();
String result = transpiler.transpile(sql, TargetDatabase.DAMENG);
```

**v2.0代码**:
```java
// v2.0 API
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

TranspilationResult result = TranspilerBuilder.mysqlToDameng()
    .transpile("CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY)");

if (result.isSuccess()) {
    System.out.println("转换成功: " + result.getTargetSql());
} else {
    System.err.println("转换失败: " + result.getErrorMessage());
}
```

#### 高级配置

**v1.x代码**:
```java
// v1.x 配置 (已移除)
TranspilerConfig config = new TranspilerConfig();
config.setValidationEnabled(true);
config.setTargetDatabase(TargetDatabase.KINGBASE);

Transpiler transpiler = new Transpiler(config);
String result = transpiler.transpile(sql);
```

**v2.0代码**:
```java
// v2.0 高级配置
import com.xylink.sqltranspiler.v2.api.SqlTranspiler;
import com.xylink.sqltranspiler.v2.dialects.SqlDialectType;
import java.time.Duration;

SqlTranspiler transpiler = TranspilerBuilder.create()
    .fromDialect(SqlDialectType.MYSQL)
    .toDialect(SqlDialectType.KINGBASE)
    .withValidation(true)
    .withOptimization(true)
    .strictMode(false)
    .timeout(Duration.ofSeconds(30))
    .build();

TranspilationResult result = transpiler.transpile(sql);
```

### 3. CLI工具迁移

**v1.x CLI**:
```bash
java -jar sql-transpiler-1.0.0.jar -i input.sql -t dameng -o output.sql
```

**v2.0 CLI**:
```bash
java -jar sql-transpiler-2.0.0.jar --target DAMENG "CREATE TABLE test (id INT)"
```

### 4. 错误处理迁移

**v1.x错误处理**:
```java
// v1.x 简单异常处理
try {
    String result = transpiler.transpile(sql, TargetDatabase.DAMENG);
    System.out.println(result);
} catch (TranspilerException e) {
    System.err.println("转换失败: " + e.getMessage());
}
```

**v2.0错误处理**:
```java
// v2.0 详细错误处理
TranspilationResult result = transpiler.transpile(sql);
if (result.isSuccess()) {
    System.out.println("转换成功: " + result.getTargetSql());
    System.out.println("统计信息: " + result.getStatistics());
} else {
    System.err.println("转换失败: " + result.getErrorMessage());
    
    // 获取详细诊断信息
    result.getDiagnostics().forEach(diagnostic -> {
        System.err.println("诊断: " + diagnostic.getMessage());
        System.err.println("位置: " + diagnostic.getLocation());
        System.err.println("建议: " + diagnostic.getSuggestion());
    });
}
```

## 🆕 v2.0 新功能

### 1. 查询优化
```java
// 启用查询优化
SqlTranspiler transpiler = TranspilerBuilder.mysqlToDameng()
    .withOptimization(true)  // 启用Volcano优化器
    .build();
```

### 2. 批量转换
```java
// 批量转换多个SQL语句
List<String> sqls = Arrays.asList(
    "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY)",
    "INSERT INTO users (name) VALUES ('test')",
    "SELECT * FROM users WHERE id > 10 LIMIT 5"
);

for (String sql : sqls) {
    TranspilationResult result = transpiler.transpile(sql);
    // 处理结果...
}
```

### 3. 详细统计信息
```java
TranspilationResult result = transpiler.transpile(sql);
if (result.isSuccess()) {
    TranspilationStatistics stats = result.getStatistics();
    System.out.println("解析时间: " + stats.getParseTime());
    System.out.println("转换时间: " + stats.getTransformTime());
    System.out.println("优化时间: " + stats.getOptimizeTime());
    System.out.println("生成时间: " + stats.getGenerateTime());
}
```

## ⚠️ 重要注意事项

### 1. 不兼容的变化
- **v1.x的所有API都已移除**，必须使用v2.0的新API
- **包名变化**: 从`com.xylink.sqltranspiler.*`变为`com.xylink.sqltranspiler.v2.*`
- **配置方式变化**: 从配置类变为Builder模式

### 2. 功能变化
- **Web服务已移除**: v2.0专注于核心转换功能，Web服务将在后续版本中重新实现
- **文件批量处理**: CLI工具的文件处理功能正在重新实现中

### 3. 测试迁移
- **v1.x的测试用例已清理**: 需要基于v2.0 API重写测试用例
- **新的测试架构**: 采用更严格的官方文档合规性测试

## 🔧 迁移工具

### 自动化迁移脚本
我们提供了一个迁移脚本来帮助您快速迁移：

```bash
# 下载迁移脚本
wget https://github.com/your-repo/sql-transpiler/releases/download/v2.0.0/migrate-to-v2.sh

# 运行迁移脚本
chmod +x migrate-to-v2.sh
./migrate-to-v2.sh /path/to/your/project
```

### 手动迁移检查清单
- [ ] 更新Maven/Gradle依赖到v2.0
- [ ] 更新import语句到v2包路径
- [ ] 替换v1 API调用为v2 Builder API
- [ ] 更新错误处理逻辑
- [ ] 更新CLI命令参数
- [ ] 重写测试用例
- [ ] 验证转换结果

## 📞 获取帮助

如果在迁移过程中遇到问题，请：

1. 查看 [v2.0文档](README.md)
2. 查看 [API指南](api-guide.md)
3. 查看 [示例代码](examples/)
4. 提交 [GitHub Issue](https://github.com/your-repo/sql-transpiler/issues)

## 🎉 迁移完成

迁移完成后，您将享受到v2.0带来的：
- 更强大的SQL转换能力
- 更好的错误诊断
- 更高的转换准确性
- 更好的扩展性
- 基于官方文档的100%合规性保证

欢迎使用SQL Transpiler v2.0！
