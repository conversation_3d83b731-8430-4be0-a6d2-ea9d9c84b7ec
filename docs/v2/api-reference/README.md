# API参考文档

SQL Transpiler v2.0 提供了简洁而强大的API，本文档详细介绍了所有公共API的使用方法。

## 📋 目录

- [核心API](#核心api)
  - [TranspilerBuilder](#transpilerbuilder)
  - [SqlTranspiler](#sqltranspiler)
  - [TranspilationResult](#transpilationresult)
- [配置API](#配置api)
- [诊断API](#诊断api)
- [插件API](#插件api)
- [优化器API](#优化器api)

## 🏗️ 核心API

### TranspilerBuilder

`TranspilerBuilder` 是创建SQL转换器的主要入口点，提供了流式API来配置和构建转换器。

#### 静态工厂方法

```java
// 预配置的转换器建造者
public static TranspilerBuilder mysqlToDameng()
public static TranspilerBuilder mysqlToKingbase()
public static TranspilerBuilder mysqlToShentong()

// 通用转换器建造者
public static TranspilerBuilder create(SqlDialect source, SqlDialect target)
```

**示例：**

```java
// 使用预配置的建造者
TranspilerBuilder builder = TranspilerBuilder.mysqlToDameng();

// 使用通用建造者
TranspilerBuilder builder = TranspilerBuilder.create(SqlDialect.MYSQL, SqlDialect.DAMENG);
```

#### 配置方法

```java
// 启用/禁用SQL验证
public TranspilerBuilder withValidation(boolean enable)

// 启用/禁用查询优化
public TranspilerBuilder withOptimization(boolean enable)

// 设置严格模式
public TranspilerBuilder strictMode(boolean strict)

// 设置超时时间
public TranspilerBuilder timeout(Duration timeout)

// 添加自定义规则
public TranspilerBuilder addCustomRule(TransformationRule rule)
public TranspilerBuilder addCustomRules(List<TransformationRule> rules)

// 设置自定义成本模型
public TranspilerBuilder withCostModel(CostModel costModel)
```

**示例：**

```java
TranspilerBuilder builder = TranspilerBuilder.mysqlToDameng()
    .withValidation(true)
    .withOptimization(true)
    .strictMode(false)
    .timeout(Duration.ofSeconds(30))
    .addCustomRule(new MyCustomRule());
```

#### 构建和转换方法

```java
// 构建转换器
public SqlTranspiler build()

// 直接转换（一次性使用）
public TranspilationResult transpile(String sql)
public List<TranspilationResult> transpileAll(List<String> sqlStatements)
```

**示例：**

```java
// 构建可重用的转换器
SqlTranspiler transpiler = builder.build();

// 直接转换
TranspilationResult result = builder.transpile("SELECT * FROM users");

// 批量转换
List<String> sqls = Arrays.asList("SELECT * FROM users", "SELECT * FROM orders");
List<TranspilationResult> results = builder.transpileAll(sqls);
```

### SqlTranspiler

`SqlTranspiler` 是实际执行SQL转换的接口，通过 `TranspilerBuilder.build()` 创建。

#### 方法

```java
// 转换单个SQL语句
public TranspilationResult transpile(String sql)

// 批量转换SQL语句
public List<TranspilationResult> transpileAll(List<String> sqlStatements)

// 获取转换器配置
public TranspilerConfig getConfig()

// 获取支持的源方言
public SqlDialect getSourceDialect()

// 获取支持的目标方言
public SqlDialect getTargetDialect()
```

**示例：**

```java
SqlTranspiler transpiler = TranspilerBuilder.mysqlToDameng()
    .withValidation(true)
    .build();

// 可重复使用
TranspilationResult result1 = transpiler.transpile(sql1);
TranspilationResult result2 = transpiler.transpile(sql2);

System.out.println("源方言: " + transpiler.getSourceDialect());
System.out.println("目标方言: " + transpiler.getTargetDialect());
```

### TranspilationResult

`TranspilationResult` 包含SQL转换的完整结果信息。

#### 方法

```java
// 转换状态
public boolean isSuccess()
public boolean isFailure()

// SQL内容
public String getOriginalSql()
public String getTranspiledSql()

// 错误信息
public String getErrorMessage()
public Optional<DiagnosticReport> getDiagnosticReport()

// 统计信息
public Optional<TransformationStatistics> getStatistics()
public long getDurationMillis()

// 元数据
public SqlDialect getSourceDialect()
public SqlDialect getTargetDialect()
public LocalDateTime getTimestamp()
```

**示例：**

```java
TranspilationResult result = transpiler.transpile(sql);

if (result.isSuccess()) {
    System.out.println("原始SQL: " + result.getOriginalSql());
    System.out.println("转换后SQL: " + result.getTranspiledSql());
    System.out.println("转换耗时: " + result.getDurationMillis() + "ms");
    
    // 获取统计信息
    if (result.getStatistics().isPresent()) {
        TransformationStatistics stats = result.getStatistics().get();
        System.out.println("应用规则数: " + stats.getAppliedRulesCount());
    }
} else {
    System.err.println("转换失败: " + result.getErrorMessage());
    
    // 获取详细诊断信息
    if (result.getDiagnosticReport().isPresent()) {
        DiagnosticReport report = result.getDiagnosticReport().get();
        System.err.println(report.toFormattedString());
    }
}
```

## ⚙️ 配置API

### TranspilerConfig

转换器配置类，包含所有配置选项。

```java
public class TranspilerConfig {
    // 验证选项
    public boolean isValidationEnabled()
    public ValidationLevel getValidationLevel()
    
    // 优化选项
    public boolean isOptimizationEnabled()
    public OptimizationLevel getOptimizationLevel()
    
    // 模式选项
    public boolean isStrictMode()
    public boolean isFailFast()
    
    // 性能选项
    public Duration getTimeout()
    public int getMaxConcurrency()
    
    // 自定义选项
    public List<TransformationRule> getCustomRules()
    public Optional<CostModel> getCustomCostModel()
}
```

### 配置建造者

```java
TranspilerConfig config = TranspilerConfig.builder()
    .validationEnabled(true)
    .validationLevel(ValidationLevel.STRICT)
    .optimizationEnabled(true)
    .optimizationLevel(OptimizationLevel.AGGRESSIVE)
    .strictMode(false)
    .timeout(Duration.ofMinutes(5))
    .maxConcurrency(4)
    .build();
```

## 📊 诊断API

### DiagnosticReport

诊断报告包含转换过程中的所有问题和信息。

```java
public class DiagnosticReport {
    // 基本信息
    public String getReportId()
    public LocalDateTime getTimestamp()
    public DiagnosticSeverity getOverallSeverity()
    
    // 问题统计
    public int getTotalIssueCount()
    public int getErrorCount()
    public int getWarningCount()
    public int getInfoCount()
    
    // 问题列表
    public List<DiagnosticIssue> getAllIssues()
    public List<DiagnosticIssue> getErrors()
    public List<DiagnosticIssue> getWarnings()
    public List<DiagnosticIssue> getInfos()
    
    // 状态检查
    public boolean hasErrors()
    public boolean hasWarnings()
    public boolean hasIssues()
    
    // 格式化输出
    public String toFormattedString()
    public String getSummary()
    
    // 元数据
    public Map<String, Object> getMetadata()
}
```

### DiagnosticIssue

单个诊断问题的详细信息。

```java
public class DiagnosticIssue {
    // 基本信息
    public DiagnosticSeverity getSeverity()
    public String getCode()
    public String getMessage()
    public DiagnosticCategory getCategory()
    
    // 位置信息
    public Optional<SourceLocation> getLocation()
    
    // 附加信息
    public Optional<String> getDocumentationReference()
    public Optional<String> getSuggestedFix()
    
    // 工厂方法
    public static DiagnosticIssue error(String code, String message)
    public static DiagnosticIssue warning(String code, String message)
    public static DiagnosticIssue info(String code, String message)
    public static DiagnosticIssue compatibilityWarning(String feature, String source, String target, String docs)
}
```

## 🔌 插件API

### Plugin接口

```java
public interface Plugin {
    // 插件信息
    PluginInfo getPluginInfo()
    
    // 生命周期
    void initialize(PluginContext context) throws PluginException
    void start() throws PluginException
    void stop() throws PluginException
    void destroy() throws PluginException
    
    // 状态管理
    PluginState getState()
    boolean isCompatible(String coreVersion)
    PluginDependency[] getDependencies()
}
```

### PluginManager

```java
public class PluginManager {
    // 插件加载
    public void loadPlugins(File pluginDirectory) throws PluginException
    public void loadPlugin(File jarFile) throws PluginException
    
    // 生命周期管理
    public void startAllPlugins() throws PluginException
    public void stopAllPlugins() throws PluginException
    public void unloadAllPlugins() throws PluginException
    
    // 插件查询
    public Plugin getPlugin(String pluginId)
    public List<Plugin> getAllPlugins()
    public List<PluginInfo> getPluginInfos()
    public boolean hasPlugin(String pluginId)
    
    // 注册表访问
    public PluginRegistry getRegistry()
}
```

## 🧠 优化器API

### QueryOptimizer

```java
public interface QueryOptimizer {
    // 优化执行
    OptimizationResult optimize(IRNode query, OptimizationContext context)
    
    // 规则管理
    void addRule(OptimizationRule rule)
    void addRules(List<OptimizationRule> rules)
    void removeRule(OptimizationRule rule)
    Set<OptimizationRule> getRules()
    
    // 配置管理
    void setCostModel(CostModel costModel)
    CostModel getCostModel()
    void setConfig(OptimizerConfig config)
    OptimizerConfig getConfig()
    
    // 统计信息
    OptimizerStatistics getStatistics()
    void reset()
}
```

### CostModel

```java
public interface CostModel {
    // 成本计算
    Cost calculateCost(IRNode node, OptimizationContext context)
    int compareCosts(Cost cost1, Cost cost2)
    
    // 模型信息
    String getModelName()
    String getModelVersion()
    
    // 参数管理
    CostParameters getParameters()
    void setParameters(CostParameters parameters)
}
```

## 📝 使用示例

### 完整的API使用示例

```java
import com.xylink.sqltranspiler.v2.api.*;
import com.xylink.sqltranspiler.v2.dialects.SqlDialect;
import com.xylink.sqltranspiler.v2.diagnostics.*;
import java.time.Duration;

public class ApiExample {
    public void demonstrateApi() {
        // 1. 创建配置
        TranspilerConfig config = TranspilerConfig.builder()
            .validationEnabled(true)
            .optimizationEnabled(true)
            .strictMode(false)
            .timeout(Duration.ofSeconds(30))
            .build();
        
        // 2. 构建转换器
        SqlTranspiler transpiler = TranspilerBuilder.mysqlToDameng()
            .withConfig(config)
            .addCustomRule(new MyCustomRule())
            .build();
        
        // 3. 执行转换
        String sql = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY)";
        TranspilationResult result = transpiler.transpile(sql);
        
        // 4. 处理结果
        if (result.isSuccess()) {
            System.out.println("转换成功:");
            System.out.println("  原始SQL: " + result.getOriginalSql());
            System.out.println("  转换后: " + result.getTranspiledSql());
            System.out.println("  耗时: " + result.getDurationMillis() + "ms");
            
            // 5. 查看统计信息
            result.getStatistics().ifPresent(stats -> {
                System.out.println("  应用规则: " + stats.getAppliedRulesCount());
                System.out.println("  转换节点: " + stats.getTransformedNodesCount());
            });
            
        } else {
            System.err.println("转换失败: " + result.getErrorMessage());
            
            // 6. 查看诊断信息
            result.getDiagnosticReport().ifPresent(report -> {
                System.err.println("诊断报告:");
                System.err.println(report.toFormattedString());
                
                // 处理特定类型的问题
                report.getErrors().forEach(error -> 
                    System.err.println("错误: " + error.getMessage()));
                
                report.getWarnings().forEach(warning -> 
                    System.out.println("警告: " + warning.getMessage()));
            });
        }
    }
}
```

## 🔗 相关文档

- [快速开始指南](../user-guide/quick-start.md)
- [配置指南](../user-guide/configuration.md)
- [插件开发指南](../developer-guide/plugin-development.md)
- [架构设计文档](../developer-guide/architecture.md)
