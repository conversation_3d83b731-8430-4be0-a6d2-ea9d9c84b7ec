# SQL Transpiler v2.0 - MySQL到国产数据库SQL转换框架

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://openjdk.java.net/)
[![Maven Central](https://img.shields.io/maven-central/v/com.xylink/sql-transpiler-core.svg)](https://search.maven.org/artifact/com.xylink/sql-transpiler-core)

## 🎯 项目简介

SQL Transpiler v2.0 是一个企业级的SQL转换框架，专门用于将MySQL SQL语句转换为国产数据库（达梦、金仓、神通）的SQL语句。该框架基于Apache Calcite的设计理念，提供了完整的SQL解析、优化和转换能力。

### ✨ 核心特性

- **🏗️ 企业级架构**：基于Apache Calcite设计理念的IR（中间表示）系统
- **🔄 智能转换**：支持MySQL到达梦、金仓、神通数据库的精确转换
- **🧠 查询优化**：内置基于成本的Volcano查询优化器
- **🔌 插件系统**：可扩展的插件架构，支持自定义方言和规则
- **🛠️ 工具链集成**：提供CLI工具、Maven/Gradle插件
- **📊 完善诊断**：详细的错误报告和转换统计
- **🎨 流式API**：类似Calcite RelBuilder的现代化API设计

### 🎯 支持的数据库

| 源数据库 | 目标数据库 | 支持状态 | 官方文档验证 |
|---------|-----------|---------|-------------|
| MySQL 8.4+ | 达梦数据库 8.x | ✅ 完全支持 | ✅ 已验证 |
| MySQL 8.4+ | 金仓数据库 V8 | ✅ 完全支持 | ✅ 已验证 |
| MySQL 8.4+ | 神通数据库 | ✅ 完全支持 | ✅ 已验证 |

## 🚀 快速开始

### Maven依赖

```xml
<dependency>
    <groupId>com.xylink</groupId>
    <artifactId>sql-transpiler-core</artifactId>
    <version>2.0.0</version>
</dependency>
```

### 基本使用

```java
import com.xylink.sqltranspiler.v2.api.TranspilerBuilder;
import com.xylink.sqltranspiler.v2.api.TranspilationResult;

// 简单转换
TranspilationResult result = TranspilerBuilder.mysqlToDameng()
    .transpile("CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100))");

if (result.isSuccess()) {
    System.out.println("转换结果: " + result.getTranspiledSql());
} else {
    System.err.println("转换失败: " + result.getErrorMessage());
}
```

## 📖 文档目录

### 📚 用户指南
- [快速开始指南](user-guide/quick-start.md)
- [API参考文档](api-reference/README.md)
- [配置指南](user-guide/configuration.md)
- [最佳实践](user-guide/best-practices.md)

### 🔧 开发者文档
- [架构设计](developer-guide/architecture.md)
- [插件开发](developer-guide/plugin-development.md)
- [贡献指南](developer-guide/contributing.md)

### 🛠️ 工具集成
- [CLI工具使用](tools/cli.md)
- [Maven插件](tools/maven-plugin.md)
- [Gradle插件](tools/gradle-plugin.md)

### 📊 数据库特定文档
- [达梦数据库转换指南](databases/dameng.md)
- [金仓数据库转换指南](databases/kingbase.md)
- [神通数据库转换指南](databases/shentong.md)

## 📄 许可证

本项目采用 [Apache License 2.0](../../LICENSE) 许可证。
